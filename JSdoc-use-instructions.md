## how to JSDoc interfaces/types

```ts
/**
 * @category Interfaces
 * @typedef {Object} SentryNavigationContainerProps
 * @property {React.ReactNode} children - The navigation structure of your app.
 * @property {React.RefObject<NavigationContainerRef<any>>} ref - A ref to pass to the underlying NavigationContainer.
 * @property {Function} onReady - A callback to invoke when the navigation container is ready.
 */
interface SentryNavigationContainerProps {
  children: React.ReactNode;
  ref?: React.RefObject<NavigationContainerRef<any>>;
  onReady?: () => void;
}
```

➡️ Using `@category Interfaces` and `@typedef` tags is required to describe interfaces or type objects.
➡️ Use `@property` tag to describe each field of the interface.

---

## how to JSDoc a navigation related component

```ts
/**
 * @component
 * @category Navigation
 * @description A wrapper around NavigationContainer that adds Sentry tracking
 * for navigation events and screen views.
 *
 * @param {SentryNavigationContainerProps} props - The properties for the SentryNavigationContainer component.
 * @returns {JSX.Element} A NavigationContainer with Sentry tracking.
 */
```

➡️ Use `@category Navigation` to classify anything related to routing or navigation.
➡️ Use `@component` to indicate it’s a React component.
➡️ Use `@description` to clearly state what the component does.
➡️ Use `@param` to describe the props argument.
➡️ Use `@returns` to state what the component returns.

---

## how to JSDoc a Screen component (in screens folder)

```ts
/**
 * @component
 * @category Screens
 *
 * @description This screen displays a list of matches for the user. The user can search for matches using the search bar.
 * The matches are filtered based on the search value.
 * The user can also toggle notifications and the chat widget on/off.
 * The screen also displays an offer banner at the bottom.
 *
 * @see {@link OfferBanner} - Used to display the promotional banner at the bottom.
 * @returns {JSX.Element}
 */
```

➡️ Use `@category Screens` to classify anything that lives inside the `screens/` folder.
➡️ Use `@component` for React components.
➡️ Use `@description` for explaining the purpose and features of the screen.
➡️ Use `@see` to link related components or modules.
➡️ Use `@returns` to describe the returned JSX.

---

## how to JSDoc common components (in components folder)

```ts
/**
 * @component
 * @category Components
 * @description
 * A component that acts as a biometric authentication gate for accessing its children.
 *
 * This component checks if biometric authentication is enabled and, if so, prompts
 * the user to authenticate using biometrics before granting access to its children.
 * It displays a loading state while checking biometric settings and shows an
 * authentication screen if biometrics are enabled but not authenticated.
 *
 * @param {BiometricGateProps} props - The properties for the BiometricGate component
 * @returns {JSX.Element | null} - Returns the children if authenticated or biometrics are not enabled,
 * or a biometric authentication screen if biometrics are enabled but not authenticated.
 */
```

➡️ Use `@category Components` for reusable or common components.
➡️ Use `@component` to mark it as a component.
➡️ Use `@description` to explain the behaviour and logic of the component.
➡️ Use `@param` to document props passed to it.
➡️ Use `@returns` to describe what it renders.

---

## how to JSDoc a function that exists inside a component/screen

```ts
/**
 * @function promptBiometricAuth
 * @memberof BiometricGate
 * @description
 * Prompts the user for biometric authentication.
 * If authentication fails, it shows an alert and allows the user to retry.
 * If an error occurs during authentication, it shows an error alert.
 * @returns {Promise<void>} - A promise that resolves when the authentication process is complete.
 */
```

➡️ Use `@function` to mark it as a function.
➡️ Use `@memberof` to tie the function to a specific parent component or class.
➡️ Use `@description` to explain the function purpose and flow.
➡️ Use `@returns` to describe what the function resolves to or returns.

---

## how to JSDoc a common function/hooks (in services, utils, hooks folders)

```ts
/**
 * @category Utils
 * @function socialLoginApi
 * @description Login with social media (Google/Facebook)
 * @param {SocialRegisterRequest} data - SocialRegisterRequest with provider and credentials
 * @returns {Promise<AuthResponse>}
 */
export const socialLoginApi = async (data: SocialRegisterRequest): Promise<AuthResponse> => {
  try {
    const response = await api.post(API_PATHS.social, {
      ...data,
    });

    if (response?.data?.data?.accessToken) {
      tokenStorage.set('accessToken', response.data.data.accessToken);
    }

    if (response?.data?.data?.refreshToken) {
      tokenStorage.set('refreshToken', response.data.data.refreshToken);
    }
    await saveFcmTokenInDB();
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};
```

➡️ Use `@category Utils` for functions, hooks, or services.
➡️ Use `@function` to identify it as a standalone function.
➡️ Use `@description` to explain its purpose.
➡️ Use `@param` for the input arguments.
➡️ Use `@returns` to explain the resolved or returned value.
