#!/bin/bash

# <PERSON><PERSON>t to clean the repository and create a clean zip file

# Run the cleanup script
./clean.sh

# Create a zip file with excluded directories and files
echo "Creating clean zip file..."
zip -r clean-GoRaqt.zip . \
  --exclude "*.git*" \
  --exclude "node_modules/*" \
  --exclude "android/app/build/*" \
  --exclude "android/build/*" \
  --exclude "android/.gradle/*" \
  --exclude "android/.cxx/*" \
  --exclude "ios/build/*" \
  --exclude "ios/Pods/*" \
  --exclude "*.so" \
  --exclude "*.dex" \
  --exclude "yarn.lock" \
  --exclude "package-lock.json"

# Show the zip file size
ZIP_SIZE=$(du -h clean-GoRaqt.zip | cut -f1)
echo "Clean zip file created: clean-GoRaqt.zip (Size: $ZIP_SIZE)" 