import React from 'react';
import {createGroupChannelOperatorsFragment, useSendbirdChat} from '@sendbird/uikit-react-native';
import {useGroupChannel} from '@sendbird/uikit-chat-hooks';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation';

type NavigationProp = StackNavigationProp<RootStackParamList, 'SendBird'>;

const OperatorList = ({route}: {route: any}) => {
  const GroupChannelOperatorsFragment = createGroupChannelOperatorsFragment();

  const {channelUrl} = route.params;
  const {sdk} = useSendbirdChat();
  const {channel} = useGroupChannel(sdk, channelUrl);
  const navigation = useNavigation<NavigationProp>();
  if (!channel) return null;

  return (
    <GroupChannelOperatorsFragment
      channel={channel}
      onPressHeaderLeft={() => navigation.goBack()}
      onPressHeaderRight={() =>
        navigation.navigate('ManageOperator', {
          channelUrl: channelUrl,
        })
      }
    />
  );
};

export default OperatorList;
