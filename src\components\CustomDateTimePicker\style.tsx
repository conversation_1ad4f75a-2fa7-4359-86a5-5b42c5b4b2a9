import {Platform, StyleSheet} from 'react-native';

const createDynamicStyles = (theme: any) => {
  return StyleSheet.create({
    container: {
      // flex: 1,
      borderRadius: 15,
      backgroundColor: theme.colors.greyBackground,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      padding: 16,
    },
    modalHeader: {
      alignItems: 'center',
      marginBottom: 15,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.activeColor,
    },
    calendarContainer: {
      marginBottom: 0,
    },
    monthSelector: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 20,
      alignItems: 'center',
      //   marginBottom: 10,
    },
    monthNavContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 20,
    },
    monthNavButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: theme.colors.actionBackground,
      justifyContent: 'center',
      alignItems: 'center',
    },
    monthTitle: {
      height: 36,
      flex: 1,
      borderRadius: 18,
      paddingHorizontal: 10,
      textAlign: 'center',
      fontSize: 16,
      color: theme.colors.activeColor,
      backgroundColor: theme.colors.actionBackground,
      ...Platform.select({
        ios: {
          lineHeight: 30, // Match height value for iOS
        },
        android: {
          textAlignVertical: 'center',
        },
      }),
    },
    monthNewSelector: {
      flexDirection: 'row',
      // justifyContent: 'space-between',
      gap: 20,
      flex: 1,
      alignItems: 'center',
      //   marginBottom: 10,
    },
    monthNewNavContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      flex: 1,
    },
    monthNewNavButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      // backgroundColor: theme.colors.actionBackground,
      justifyContent: 'center',
      alignItems: 'center',
    },
    dayTitleContainer: {
      height: 36,
      // flex: 1,
      justifyContent: 'center',
      borderRadius: 18,
      paddingHorizontal: 10,
      textAlign: 'center',
      fontSize: 16,
      color: theme.colors.activeColor,
      backgroundColor: theme.colors.actionBackground,
      ...Platform.select({
        ios: {
          lineHeight: 30, // Match height value for iOS
        },
        android: {
          textAlignVertical: 'center',
        },
      }),
    },
    monthNewTitleContainer: {
      flex: 0.6,
      // width: screenWidth / 4.4,
    },
    monthNewTitle: {
      height: 36,
      flex: 1,
      borderRadius: 18,
      paddingHorizontal: 10,
      textAlign: 'center',
      fontSize: 16,
      color: theme.colors.activeColor,
      // backgroundColor: theme.colors.actionBackground,
      ...Platform.select({
        ios: {
          lineHeight: 30, // Match height value for iOS
        },
        android: {
          textAlignVertical: 'center',
        },
      }),
    },
    weekDayHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 10,
    },
    weekDayText: {
      flex: 1,
      textAlign: 'center',
      fontSize: 14,
      color: theme.colors.activeColor,
      fontWeight: '600',
    },
    timeSelectorsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 15,
      flex: 1,
    },
    timeSelector: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    timeNewSelector: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.actionBackground,
      borderWidth: 1,
      borderRadius: 20,
      paddingVertical: 8,
      paddingHorizontal: 16,
      flex: 1,
      borderColor: theme.colors.activeColor,
    },
    selectedNewTimeDisplay: {
      borderRadius: 20,
    },
    selectedTimeDisplay: {
      backgroundColor: theme.colors.actionBackground,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      borderRadius: 20,
      paddingVertical: 8,
      paddingHorizontal: 16,
      minWidth: 80,
      alignItems: 'center',
    },
    timeDisplay: {
      color: theme.colors.activeColor,
      fontSize: 15,
    },
    timePlaceholder: {
      color: theme.colors.activeColor,
      fontSize: 15,
      opacity: 0.7,
    },
    amPmNewButton: {
      // backgroundColor: theme.colors.actionBackground,
      // borderWidth: 1,
      // borderColor: theme.colors.activeColor,
      // borderRadius: 20,
      // paddingVertical: 8,
      // paddingHorizontal: 10,
      marginLeft: 5,
      // minWidth: 50,
      alignItems: 'center',
      justifyContent: 'center',
    },
    amPmButton: {
      backgroundColor: theme.colors.actionBackground,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      borderRadius: 20,
      paddingVertical: 8,
      paddingHorizontal: 10,
      marginLeft: 5,
      minWidth: 50,
      alignItems: 'center',
      justifyContent: 'center',
    },
    amPmText: {
      color: theme.colors.activeColor,
      fontSize: 15,
      textAlign: 'center',
      ...Platform.select({
        ios: {
          lineHeight: 20,
        },
        android: {
          textAlignVertical: 'center',
        },
      }),
    },
    timeSeparator: {
      color: theme.colors.activeColor,
      fontSize: 15,
      marginHorizontal: 10,
    },
    playersContainer: {
      marginBottom: 15,
      flexDirection: 'row',
      gap: 10,
    },
    checkCircle: {
      width: 40,
      height: 40,
      borderRadius: 40,
      padding: 10,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      backgroundColor: theme.colors.actionBackground,
      justifyContent: 'center',
      alignItems: 'center',
    },
    addPlayersButton: {
      flexDirection: 'row',
      backgroundColor: theme.colors.actionBackground,
      borderRadius: 25,
      paddingVertical: 10,
      paddingHorizontal: 16,
      borderWidth: 1,
      flex: 1,
      borderColor: theme.colors.activeColor,
      alignItems: 'center',
    },
    addPlayersText: {
      color: theme.colors.activeColor,
      fontSize: 15,
    },
    actionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    reserveButton: {
      flex: 1,
      marginRight: 8,
      borderRadius: 40,
    },
    standInLineText: {
      fontSize: 14,
    },
    standInLineButton: {
      flex: 1,
      borderColor: theme.colors.white,
      backgroundColor: theme.colors.actionBackground,
      borderWidth: 1,
    },
    selectedPlayersContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    playerAvatarsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    playerAvatar: {
      width: 24,
      height: 24,
      borderRadius: 12,
      marginLeft: 5,
    },
    morePlayersCircle: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: theme.colors.activeColor,
      justifyContent: 'center',
      alignItems: 'center',
    },
    morePlayersText: {
      color: theme.colors.white,
      fontSize: 12,
      fontWeight: 'bold',
    },
  });
};

export default createDynamicStyles;
