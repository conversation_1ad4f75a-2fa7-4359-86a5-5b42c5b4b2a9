import {Platform} from 'react-native';
import api, {handleApiError, tokenStorage} from './api';
import {saveFcmTokenInDB} from './notificationsApi';
import {clearAuthStorage} from './storage';

// Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  email: string;
  code: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  fullName: string;
}

/**
 * @category Interfaces
 * @typedef {Object} SocialRegisterRequest
 * @property {string} token - The token received from the social provider
 * @property {string} provider - The name of the social provider (e.g., 'google', 'facebook')
 */
export interface SocialRegisterRequest {
  token: string;
  provider: string;
}

export interface OtpVerifyRequest {
  email: string;
  code: string;
}

export interface OtpResendRequest {
  email: string;
}

export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  idToken?: string;
  user: {
    id: string;
    name: string;
    email: string;
    password: string;
    // Add other user properties as needed
  };
  // Fields for the actual API response
  status: boolean;
  message: string;
  data?: {
    accessToken?: string;
    idToken?: string;
    refreshToken?: string;
    is_profile_completed?: boolean;
    user_not_verified?: boolean;
    user_exist?: boolean;
    user?: {
      id: string;
      name: string;
      email: string;
      password: string;
    };
  };
}

// API paths - don't include leading slash as baseURL already has the full path
const API_PATHS = {
  login: 'login',
  register: 'signup',
  social: 'social-login',
  otpVerify: 'verify-email',
  refreshToken: 'refresh-token',
  me: 'me',
  otpResend: 'resend-verification-code',
  forgotPassword: 'forgot-password',
  resetPassword: 'reset-password',
};

/**
 * Login with email and password
 */
export const loginWithEmailApi = async (data: LoginRequest): Promise<AuthResponse> => {
  try {
    console.log('API URL:', `${api.defaults.baseURL}/${API_PATHS.login}`, data);

    // Try with explicit content type header
    const response = await api.post(API_PATHS.login, data, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('Full response data:', JSON.stringify(response.data, null, 2));

    if (response.data?.data?.user_not_verified) {
      return {
        status: true,
        message: response.data.message || 'Please verify your email.',
        user: {
          id: '0',
          name: 'User',
          email: data?.email,
          password: data?.password,
        },
        accessToken: '',
        refreshToken: '',
        idToken: '',
        data: response.data.data,
      };
    }
    // Check if the response is successful
    // The API might return success in different formats, so we need to handle multiple cases
    if (response.status !== 200) {
      console.log('API returned non-200 status:', response.status);
      throw new Error(
        `API error: ${response.status} - ${response.data?.message || 'Login failed'}`,
      );
    }

    // Check if the response data indicates failure
    if (response.data.status === false) {
      console.log('API returned status=false in data');
      throw new Error(response.data.message || 'Login failed');
    }

    // Extract tokens from the nested response structure
    const accessToken = response.data.data?.accessToken;
    const idToken = response.data.data?.idToken;
    const refreshToken = response.data.data?.refreshToken;

    console.log('Tokens found:', {accessToken, idToken, refreshToken});

    if (!accessToken) {
      console.log('No access token found in response');
      throw new Error('No access token received from server');
    }

    // Store tokens in secure storage
    tokenStorage.set('accessToken', accessToken);

    if (refreshToken) {
      tokenStorage.set('refreshToken', refreshToken);
    }

    if (idToken) {
      tokenStorage.set('idToken', idToken);
    }

    await saveFcmTokenInDB();

    // Extract user data from response - handle different response formats
    let user = {
      id: '0',
      name: 'User',
      email: data.email,
      password: '', // Add password to satisfy type requirement
    };

    if (response.data.data?.user) {
      user = response.data.data.user;
    } else if (response.data.user) {
      user = response.data.user;
    }

    // Create a standardized response
    const authResponse: AuthResponse = {
      accessToken: accessToken,
      refreshToken: refreshToken || accessToken, // Fallback to accessToken if no refreshToken
      idToken: idToken,
      user: user,
      status: response.data.status !== undefined ? response.data.status : true,
      message: response.data.message || 'Login successful',
      data: response.data.data || response.data,
    };

    return authResponse;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

/**
 * @category Utils
 * @function socialLoginApi
 * @description Login with social media (Google/Facebook)
 * @param {SocialRegisterRequest} data - SocialRegisterRequest with provider and credentials
 * @returns {Promise<AuthResponse>}
 */
export const socialLoginApi = async (data: SocialRegisterRequest): Promise<AuthResponse> => {
  try {
    const response = await api.post(API_PATHS.social, {
      ...data,
      platform: Platform.OS,
    });

    // Store tokens
    if (response?.data?.data?.accessToken) {
      tokenStorage.set('accessToken', response.data.data.accessToken);
    }

    if (response?.data?.data?.refreshToken) {
      tokenStorage.set('refreshToken', response.data.data.refreshToken);
    }
    await saveFcmTokenInDB();
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

/**
 * Register with email, password, and name
 */
export const registerWithEmailApi = async (data: RegisterRequest): Promise<AuthResponse> => {
  try {
    const response = await api.post(API_PATHS.register, {
      ...data,
      name: data.fullName,
    });

    // Store tokens
    if (response.data.accessToken) {
      tokenStorage.set('accessToken', response.data.accessToken);
    }

    if (response.data.refreshToken) {
      tokenStorage.set('refreshToken', response.data.refreshToken);
    }

    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

/**
 * Verify OTP code
 */
export const otpVerifyApi = async (data: OtpVerifyRequest): Promise<AuthResponse> => {
  try {
    const response = await api.post(API_PATHS.otpVerify, data);
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

/**
 * Verify OTP code
 */
export const resetPasswordApi = async (data: ResetPasswordRequest): Promise<AuthResponse> => {
  try {
    const response = await api.post(API_PATHS.resetPassword, data);
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

/**
 * Resend OTP code
 */
export const otpResendApi = async (data: OtpResendRequest): Promise<AuthResponse> => {
  try {
    const response = await api.post(API_PATHS.otpResend, data);
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

/**
 * Forgot password
 */
export const forgotPasswordApi = async (data: ForgotPasswordRequest): Promise<AuthResponse> => {
  try {
    const response = await api.post(API_PATHS.forgotPassword, data);
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

/**
 * Logout - clear tokens and all authentication-related storage
 */
export const logoutApi = async (): Promise<void> => {
  try {
    // Call logout endpoint if your API has one
    // await api.post(`${API_BASE_URL}/logout`);

    // Clear all tokens from storage
    tokenStorage.delete('accessToken');
    tokenStorage.delete('refreshToken');
    tokenStorage.delete('idToken');

    // For complete cleanup, clear the entire auth-tokens storage
    tokenStorage.clearAll();

    // Also clear auth-related data from main storage
    clearAuthStorage();

    console.log('All authentication tokens and storage cleared successfully');
  } catch (error) {
    console.error('Error during logout:', error);
    throw new Error(handleApiError(error));
  }
};

/**
 * Refresh token
 */
export const refreshTokenApi = async (refreshToken: string): Promise<AuthResponse> => {
  try {
    const response = await api.post(API_PATHS.refreshToken, {refreshToken});

    // Store new tokens
    if (response.data.accessToken) {
      tokenStorage.set('accessToken', response.data.accessToken);
    }

    if (response.data.refreshToken) {
      tokenStorage.set('refreshToken', response.data.refreshToken);
    }

    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

/**
 * Check if user is authenticated by verifying token
 */
export const checkAuthStatusApi = async (): Promise<boolean> => {
  try {
    const accessToken = tokenStorage.getString('accessToken');

    if (!accessToken) {
      return false;
    }

    // Verify token with backend
    // const response = await api.get(`${API_BASE_URL}/verify-token`);
    // return response.status === 200;

    // For now, just check if token exists
    return true;
  } catch (error) {
    // Ignore error and return false
    console.log('Error checking auth status:', error);
    return false;
  }
};

/**
 * Get current user profile
 */
export const getCurrentUserApi = async (): Promise<AuthResponse['user']> => {
  try {
    const response = await api.get(API_PATHS.me);
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};
