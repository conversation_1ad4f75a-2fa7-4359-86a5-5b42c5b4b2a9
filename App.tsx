/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, {useEffect, useRef} from 'react';
import {AppNavigator} from '@/navigation';
// import {ActivityIndicator} from 'react-native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import analytics from '@react-native-firebase/analytics';
import {StyleSheet, AppState, AppStateStatus, Platform, useColorScheme} from 'react-native';
// import {enableScreens} from 'react-native-screens';
import BiometricGate from '@/components/BiometricGate';
import {configureGoogleSignIn, configureFacebookSDK} from '@/utils/auth';
import {
  setupMemoryCleaning,
  cleanMemoryIfNeeded,
  setupTextureErrorHandlers,
} from '@/utils/memoryManager';
// Zustand stores are initialized automatically

import {EnhancedNetworkProvider} from '@/context/EnhancedNetworkContext';
import {NavigationContainerRef} from '@react-navigation/native';
import {RootStackParamList} from '@/navigation';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {QueryClientProvider} from '@/providers/QueryClientProvider';
// import {prefetchInitialQueries} from '@/utils/prefetchQueries';

// Import geolocation at the top level
import Geolocation from '@react-native-community/geolocation';
import {SocketProvider} from '@/context/SocketContext';
import {TranslationProvider} from '@/context/TranslationContext';
import * as Sentry from '@sentry/react-native';
import {initSentry, setUserContext} from '@/utils/sentryUtils';
import {ErrorBoundary} from '@/components/ErrorBoundary';
import {useAuthStore} from '@/store/authStore';
import {cleanupTokenRefresh, initializeTokenRefresh} from '@/utils/tokenRefreshManager';
import CToast from '@/components/CToast';
import StatusBarComponent from '@/components/StatusBarCmp';
import RemotePushNotification from '@/components/common/PushNotification';
import {MMKV} from 'react-native-mmkv';
// import {KeyboardProvider} from 'react-native-keyboard-controller';

import {
  createNativeClipboardService,
  createNativeFileService,
  createNativeMediaService,
  createNativeNotificationService,
  createNativePlayerService,
  createNativeRecorderService,
  SendbirdUIKitContainer,
  TypingIndicatorType,
} from '@sendbird/uikit-react-native';
import {DarkUIKitTheme, Palette} from '@sendbird/uikit-react-native-foundation';

import Clipboard from '@react-native-clipboard/clipboard';
import {CameraRoll} from '@react-native-camera-roll/camera-roll';
import Video from 'react-native-video';
import * as DocumentPicker from '@react-native-documents/picker';
import * as FileAccess from 'react-native-file-access';
import * as ImagePicker from 'react-native-image-picker';
import * as Permissions from 'react-native-permissions';
import * as CreateThumbnail from 'react-native-create-thumbnail';
import * as ImageResizer from '@bam.tech/react-native-image-resizer';
import * as AudioRecorderPlayer from 'react-native-audio-recorder-player';
import RNFBMessaging from '@react-native-firebase/messaging';
import {useThemeStore} from '@/store';
import {FONT_SIZE} from '@/utils/fonts';
import {FONTS} from '@/utils/fonts';
const mmkv = new MMKV();

const nativePlatformServices = {
  clipboard: createNativeClipboardService(Clipboard),
  notification: createNativeNotificationService({
    messagingModule: RNFBMessaging,
    permissionModule: Permissions,
  }),
  file: createNativeFileService({
    imagePickerModule: ImagePicker,
    documentPickerModule: DocumentPicker,
    permissionModule: Permissions,
    fsModule: FileAccess,
    mediaLibraryModule: CameraRoll,
  }),
  media: createNativeMediaService({
    VideoComponent: Video,
    thumbnailModule: CreateThumbnail,
    imageResizerModule: ImageResizer,
  }),
  player: createNativePlayerService({
    audioRecorderModule: AudioRecorderPlayer,
    permissionModule: Permissions,
  }),
  recorder: createNativeRecorderService({
    audioRecorderModule: AudioRecorderPlayer,
    permissionModule: Permissions,
  }),
};

// Initialize Sentry with our custom configuration
initSentry();

// enableScreens();
// Initialize geolocation service
const initializeGeolocation = () => {
  try {
    // Configure geolocation
    if (Platform.OS === 'ios') {
      Geolocation.setRNConfiguration({
        skipPermissionRequests: false,
        authorizationLevel: 'whenInUse',
      });
    }

    console.log('Geolocation service initialized');
  } catch (error) {
    console.error('Failed to initialize geolocation service:', error);
  }
};

// Main App component with AppContext Provider
function App(): React.JSX.Element {
  // Reference to the navigation container
  const navigationRef = useRef<NavigationContainerRef<RootStackParamList>>(null);
  const {user} = useAuthStore();
  const appTheme = useThemeStore();
  const theme = DarkUIKitTheme;

  DarkUIKitTheme.palette = {
    ...Palette,
    primary200: appTheme.colors.white,
    background400: appTheme.colors.gray,
    onBackgroundDark02: appTheme.colors.white,
    onBackgroundDark03: appTheme.colors.white,
  };

  DarkUIKitTheme.typography = {
    h1: {
      fontFamily: FONTS.medium,
      fontSize: FONT_SIZE.xl,
      lineHeight: 28,
      letterSpacing: 0.15,
    },
    h2: {
      fontFamily: FONTS.medium,
      fontSize: FONT_SIZE.xl,
      lineHeight: 24,
      letterSpacing: 0.15,
    },
    subtitle1: {
      fontFamily: FONTS.medium,
      fontSize: FONT_SIZE.xl,
      lineHeight: 30,
      letterSpacing: 0.15,
    },
    subtitle2: {
      fontFamily: FONTS.regular,
      fontSize: FONT_SIZE.xl,
      lineHeight: 22,
      letterSpacing: 0.15,
    },
    body1: {
      fontFamily: FONTS.regular,
      fontSize: FONT_SIZE.lg,
      lineHeight: 20,
      letterSpacing: 0.15,
    },
    body2: {
      fontFamily: FONTS.medium,
      fontSize: FONT_SIZE.lg,
      lineHeight: 20,
      letterSpacing: 0.15,
    },
    body3: {
      fontFamily: FONTS.medium,
      fontSize: FONT_SIZE.lg,
      lineHeight: 20,
      letterSpacing: 0.15,
    },
    button: {
      fontFamily: FONTS.medium,
      fontSize: FONT_SIZE.lg,
      lineHeight: 20,
      letterSpacing: 0.15,
    },
    caption1: {
      fontFamily: FONTS.medium,
      fontSize: FONT_SIZE.sm,
      lineHeight: 12,
      letterSpacing: 0.15,
    },
    caption2: {
      fontFamily: FONTS.regular,
      fontSize: FONT_SIZE.sm,
      lineHeight: 12,
      letterSpacing: 0.15,
    },
    caption3: {
      fontFamily: FONTS.bold,
      fontSize: FONT_SIZE.sm,
      lineHeight: 12,
      letterSpacing: 0.15,
    },
    caption4: {
      fontFamily: FONTS.regular,
      fontSize: FONT_SIZE.sm,
      lineHeight: 12,
      letterSpacing: 0.15,
    },
  };

  DarkUIKitTheme.colors.ui = {
    ...DarkUIKitTheme.colors.ui,
    header: {
      nav: {
        none: {
          background: appTheme.colors.primary,
          borderBottom: appTheme.colors.black,
        },
      },
    },
    input: {
      default: {
        active: {
          background: appTheme.colors.white,
          text: appTheme.colors.black,
          highlight: appTheme.colors.white,
          placeholder: appTheme.colors.gray,
        },
        disabled: {
          background: appTheme.colors.veryLightGray,
          text: appTheme.colors.midGrey,
          highlight: appTheme.colors.midGrey,
          placeholder: appTheme.colors.midGrey,
        },
      },
      underline: {
        active: {
          background: 'transparent',
          text: appTheme.colors.white,
          highlight: appTheme.colors.white,
          placeholder: appTheme.colors.white,
        },
        disabled: {
          background: 'transparent',
          text: appTheme.colors.midGrey,
          highlight: appTheme.colors.midGrey,
          placeholder: appTheme.colors.midGrey,
        },
      },
    },
    groupChannelMessage: {
      incoming: {
        enabled: {
          background: appTheme.colors.primary,
          textMsg: appTheme.colors.white,
          textSenderName: appTheme.colors.primary,
          voiceProgressTrack: appTheme.colors.primary,
          textEdited: appTheme.colors.white,
          textTime: appTheme.colors.white,
          textVoicePlaytime: appTheme.colors.white,
          voiceSpinner: appTheme.colors.white,
          voiceActionIcon: appTheme.colors.white,
          voiceActionIconBackground: appTheme.colors.orange,
        },
        pressed: {
          background: appTheme.colors.orange,
          textMsg: appTheme.colors.white,
          textSenderName: appTheme.colors.primary,
          textEdited: appTheme.colors.white,
          textTime: appTheme.colors.white,
          textVoicePlaytime: appTheme.colors.white,
          voiceSpinner: appTheme.colors.white,
          voiceProgressTrack: appTheme.colors.primary,
          voiceActionIcon: appTheme.colors.white,
          voiceActionIconBackground: appTheme.colors.orange,
        },
      },
      outgoing: {
        enabled: {
          textMsg: appTheme.colors.white,
          voiceProgressTrack: appTheme.colors.orange,
          background: appTheme.colors.orange,
          textEdited: appTheme.colors.white,
          textSenderName: appTheme.colors.primary,
          textTime: appTheme.colors.white,
          textVoicePlaytime: appTheme.colors.white,
          voiceSpinner: appTheme.colors.white,
          voiceActionIcon: appTheme.colors.white,
          voiceActionIconBackground: appTheme.colors.primary,
        },
        pressed: {
          background: appTheme.colors.primary,
          textMsg: appTheme.colors.white,
          textSenderName: appTheme.colors.primary,
          textEdited: appTheme.colors.white,
          textTime: appTheme.colors.white,
          textVoicePlaytime: appTheme.colors.white,
          voiceSpinner: appTheme.colors.white,
          voiceProgressTrack: appTheme.colors.primary,
          voiceActionIcon: appTheme.colors.white,
          voiceActionIconBackground: appTheme.colors.primary,
        },
      },
    },
    dialog: {
      default: {
        none: {
          background: appTheme.colors.primary,
          text: appTheme.colors.white,
          highlight: appTheme.colors.white,
          message: appTheme.colors.white,
          destructive: appTheme.colors.error,
          blurred: appTheme.colors.semiTransparentBlack,
        },
      },
    },
    voiceMessageInput: {
      default: {
        active: {
          textCancel: appTheme.colors.white,
          background: appTheme.colors.primary,
          textTime: appTheme.colors.primary,
          actionIcon: appTheme.colors.white,
          actionIconBackground: appTheme.colors.orange,
          sendIcon: appTheme.colors.white,
          sendIconBackground: 'transparent',
          progressTrack: appTheme.colors.white,
          recording: appTheme.colors.error,
        },
        inactive: {
          textCancel: appTheme.colors.white,
          background: appTheme.colors.primary,
          textTime: appTheme.colors.primary,
          actionIcon: appTheme.colors.white,
          actionIconBackground: appTheme.colors.white,
          sendIcon: appTheme.colors.white,
          sendIconBackground: 'transparent',
          progressTrack: appTheme.colors.white,
          recording: appTheme.colors.error,
        },
      },
    },
    reaction: {
      default: {
        enabled: {
          background: 'transparent',
          highlight: appTheme.colors.primary,
        },
        selected: {
          background: appTheme.colors.primary,
          highlight: appTheme.colors.white,
        },
      },
      rounded: {
        enabled: {
          background: appTheme.colors.primary,
          highlight: appTheme.colors.green,
        },
        selected: {
          background: appTheme.colors.primary,
          highlight: appTheme.colors.green,
        },
      },
    },
    button: {
      contained: {
        pressed: {
          content: appTheme.colors.white,
          background: 'transparent',
        },
        enabled: {
          content: appTheme.colors.white,
          background: 'transparent',
        },
        disabled: {
          content: appTheme.colors.white,
          background: 'transparent',
        },
      },
      text: {
        enabled: {
          content: appTheme.colors.white,
          background: 'transparent',
        },
        disabled: {
          content: appTheme.colors.white,
          background: 'transparent',
        },
        pressed: {
          content: appTheme.colors.white,
          background: 'transparent',
        },
      },
    },
    dateSeparator: {
      default: {
        none: {
          background: appTheme.colors.white,
          text: appTheme.colors.black,
        },
      },
    },
  };

  // Set up Sentry user context when user changes
  useEffect(() => {
    setUserContext(user);
  }, [user]);

  // Initialize services
  useEffect(() => {
    // Configure Google Sign-In
    configureGoogleSignIn();

    // Configure Facebook SDK
    configureFacebookSDK();

    // Initialize geolocation service
    initializeGeolocation();
    analytics().logAppOpen();
    analytics().setUserId(user?.id || user?.email || 'unknown-user'); // Set user ID for analytics
    // Track if get current location
    // analytics().setUserProperty('location', 'Nadiad');
    analytics().setUserProperty('app_type', `goraqt_${Platform.OS}`);
    // Prefetch initial queries
    // prefetchInitialQueries().catch(error => {
    //   console.error('Failed to prefetch initial queries:', error);
    // });
  }, []);

  // Set up memory management
  useEffect(() => {
    // Set up periodic memory cleaning
    const cleanupMemoryManager = setupMemoryCleaning();

    // Set up texture error handlers
    const cleanupErrorHandlers = setupTextureErrorHandlers();

    // Clean memory when app goes to background
    const appStateSubscription = AppState.addEventListener(
      'change',
      (nextAppState: AppStateStatus) => {
        if (nextAppState === 'background' || nextAppState === 'inactive') {
          // App is going to background, clean memory
          cleanMemoryIfNeeded(true);
        }
      },
    );

    // Initial memory clean
    cleanMemoryIfNeeded();

    // Cleanup on unmount
    return () => {
      cleanupMemoryManager();
      cleanupErrorHandlers();
      appStateSubscription.remove();
    };
  }, []);

  // Token refresh lifecycle
  useEffect(() => {
    initializeTokenRefresh();
    return () => cleanupTokenRefresh();
  }, []);

  return (
    <ErrorBoundary componentName="App">
      <SafeAreaProvider>
        <QueryClientProvider>
          <EnhancedNetworkProvider>
            <SocketProvider>
              <TranslationProvider>
                <BiometricGate>
                  <GestureHandlerRootView style={styles.container}>
                    {/* <KeyboardProvider> */}
                    <SendbirdUIKitContainer
                      appId="9164717B-F0BA-41A2-B05F-6A308C5FC079"
                      styles={{theme}}
                      platformServices={nativePlatformServices}
                      chatOptions={{localCacheStorage: mmkv}}
                      uikitOptions={{
                        groupChannel: {
                          enableVoiceMessage: true,
                          enableReactions: true,
                          enableTypingIndicator: true,
                          enableMention: true,
                          typingIndicatorTypes: new Set([
                            TypingIndicatorType.Bubble,
                            TypingIndicatorType.Text,
                          ]),
                        },
                        groupChannelList: {
                          enableMessageReceiptStatus: true,
                        },
                      }}>
                      <StatusBarComponent />
                      <AppNavigator ref={navigationRef} />
                      <RemotePushNotification />
                      <CToast />
                    </SendbirdUIKitContainer>
                    {/* </KeyboardProvider> */}
                  </GestureHandlerRootView>
                </BiometricGate>
              </TranslationProvider>
            </SocketProvider>
          </EnhancedNetworkProvider>
        </QueryClientProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
}

export default Sentry.wrap(App);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
