import Geolocation from '@react-native-community/geolocation';
import {Platform, PermissionsAndroid} from 'react-native';

// Configure geolocation
Geolocation.setRNConfiguration({
  skipPermissionRequests: false,
  authorizationLevel: 'whenInUse',
});

interface LocationData {
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp?: number;
}

interface GeocodedAddress {
  formattedAddress: string;
  streetNumber?: string;
  street?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
}

export const requestLocationPermission = async (): Promise<boolean> => {
  if (Platform.OS === 'ios') {
    // iOS permissions are handled by the geolocation module
    return true;
  }

  try {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      {
        title: 'Location Permission',
        message: 'This app needs access to your location',
        buttonNeutral: 'Ask Me Later',
        buttonNegative: 'Cancel',
        buttonPositive: 'OK',
      },
    );

    return granted === PermissionsAndroid.RESULTS.GRANTED;
  } catch (err) {
    console.warn(err);
    return false;
  }
};

export const getCurrentLocation = (): Promise<LocationData> => {
  return new Promise((resolve, reject) => {
    Geolocation.getCurrentPosition(
      position => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp,
        });
      },
      error => {
        console.error('Location error:', error);
        reject(error);
      },
      {
        enableHighAccuracy: false,
        timeout: 20000,
        maximumAge: 1000,
      },
    );
  });
};

export const watchPosition = (callback: (location: LocationData) => void): number => {
  return Geolocation.watchPosition(
    position => {
      callback({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        timestamp: position.timestamp,
      });
    },
    error => {
      console.error('Error watching position:', error);
    },
    {enableHighAccuracy: true, distanceFilter: 10, interval: 5000, fastestInterval: 2000},
  );
};

export const clearWatch = (watchId: number): void => {
  Geolocation.clearWatch(watchId);
};

/**
 * Get address from coordinates using OpenStreetMap's Nominatim API (no API key required)
 * @param latitude Latitude coordinate
 * @param longitude Longitude coordinate
 * @returns Promise with the address information
 */
export const getAddressFromCoordinates = async (
  latitude: number,
  longitude: number,
): Promise<GeocodedAddress> => {
  try {
    // Using Nominatim API (OpenStreetMap) which doesn't require an API key
    const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&addressdetails=1`;

    const response = await fetch(url, {
      headers: {
        // Adding a User-Agent header is required by Nominatim's usage policy
        'User-Agent': 'GoRaqt React Native App',
        'Accept-Language': 'en', // Request English results
      },
    });

    const data = await response.json();
    if (!data || !data.address) {
      throw new Error('No address data found');
    }

    // Extract address components from Nominatim response
    const address = data.address;

    return {
      formattedAddress: data.display_name || '',
      streetNumber: address.house_number || '',
      street: address.road || address.pedestrian || address.street || '',
      city: address.city || address.town || address.village || address.hamlet || '',
      state: address.state || address.county || '',
      country: address.country || '',
      postalCode: address.postcode || '',
    };
  } catch (error) {
    console.error('Error getting address from coordinates:', error);
    throw error;
  }
};

/**
 * Get current location and convert to address
 * @returns Promise with current location data and address
 */
export const getCurrentLocationWithAddress = async (): Promise<{
  location: LocationData;
  address: GeocodedAddress;
}> => {
  try {
    const location = await getCurrentLocation();
    const address = await getAddressFromCoordinates(location.latitude, location.longitude);

    return {
      location,
      address,
    };
  } catch (error) {
    console.error('Error getting current location with address:', error);
    throw error;
  }
};

export default {
  requestLocationPermission,
  getCurrentLocation,
  watchPosition,
  clearWatch,
  getAddressFromCoordinates,
  getCurrentLocationWithAddress,
};
