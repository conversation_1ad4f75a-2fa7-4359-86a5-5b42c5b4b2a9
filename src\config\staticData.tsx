import {Images} from './images';

export const drawerMenuList = [
  {
    id: 'notifications',
    label: 'drawer.notifications',
    title: 'Notifications',
    isVisible: true,
  },
  {
    id: 'refer_friend',
    label: 'drawer.referFriend',
    title: 'Refer a friend',
    isVisible: true,
  },
  {
    id: 'my_matches',
    label: 'drawer.myMatches',
    title: 'My Matches',
    isVisible: true,
  },
  // {
  //   id: 'payment',
  //   label: 'Payment',
  //   title: 'Payment',
  //   isVisible: true,
  // },
  {
    id: 'rewards',
    label: 'drawer.rewards',
    title: 'Rewards',
    isVisible: true,
  },
  {
    id: 'recycleBalls',
    label: 'drawer.recycleBalls',
    title: 'RecycleBalls',
    isVisible: true,
  },
  {
    id: 'help',
    label: 'drawer.help',
    title: 'Help',
    isVisible: true,
  },
  {
    id: 'settings',
    label: 'drawer.settings',
    screen: 'Settings',
    title: 'Setting<PERSON>',
    isVisible: true,
  },
  {
    id: 'manage_community',
    label: 'drawer.manageCommunity',
    title: 'Manage Community',
    isVisible: true,
  },
  {
    id: 'orders',
    label: 'drawer.orders',
    title: 'Orders',
    isVisible: true,
  },
];

export const coachDrawerMenuList = [
  {
    id: 'edit_coach_profile',
    label: 'assistantCoachOptions.editProfile',
    title: 'Edit Coach Profile',
    isVisible: true,
  },
  {
    id: 'calendar',
    label: 'assistantCoachOptions.calendar',
    title: 'Calendar',
    isVisible: true,
  },
  {
    id: 'manage_classes',
    label: 'assistantCoachOptions.manageClasses',
    title: 'Manage Classes',
    isVisible: true,
  },
  {
    id: 'manage_player_assets',
    label: 'assistantCoachOptions.managePlayerAssets',
    title: 'Manage Player Assets',
    isVisible: true,
  },
  {
    id: 'manage_service_request',
    label: 'assistantCoachOptions.manageServiceRequest',
    title: 'Manage Service Request',
    isVisible: true,
  },
  {
    id: 'manage_content',
    label: 'assistantCoachOptions.manageContent',
    title: 'Manage Content',
    isVisible: true,
  },
  {
    id: 'post_an_ad',
    label: 'assistantCoachOptions.postAnAd',
    title: 'Post an Ad',
    isVisible: true,
  },
  {
    id: 'order_reserve_equipment',
    label: 'assistantCoachOptions.orderReserveEquipment',
    title: 'Order/Reserve Equipment',
    isVisible: true,
  },
  {
    id: 'get_certified',
    label: 'assistantCoachOptions.getCertified',
    title: 'Get Certified',
    isVisible: true,
  },
  {
    id: 'message_notification_preferences',
    label: 'assistantCoachOptions.messageNotificationPreferences',
    title: 'Message Notification Preferences',
    isVisible: true,
  },
  {
    id: 'invite_players',
    label: 'assistantCoachOptions.invitePlayers',
    title: 'Invite Players',
    isVisible: true,
  },
];

export const createProfileRadioData = {
  bestDescribeOptions: [
    {label: 'createProfile.bestDescribeList.player', value: 'player'},
    {label: 'createProfile.bestDescribeList.coach', value: 'coach'},
    {label: 'createProfile.bestDescribeList.both', value: 'both'},
  ],
  describeFitnessOptions: [
    {label: 'createProfile.describeFitnessList.slow_and_steady', value: 'slow_and_steady'},
    {label: 'createProfile.describeFitnessList.workout_warrior', value: 'workout_warrior'},
    {label: 'createProfile.describeFitnessList.can_keep_a_rally', value: 'can_keep_a_rally'},
    {label: 'createProfile.describeFitnessList.play_competitively', value: 'play_competitively'},
  ],
};

export const parks = [
  {
    id: '1',
    name: 'Governor Smith Playground',
    location: 'Morris Avenue between E. 151 and E. 155 Streets',
  },
  {
    id: '2',
    name: 'Orchard Beach',
    location: 'Pelham Bay Park 1 Orchard Beach Road',
  },
  {
    id: '3',
    name: 'Seton Park',
    location: 'Seton Park W. 232nd to 235th St., Palisade and Independence Aves.',
  },
  {
    id: '4',
    name: "St. Mary's Park",
    location: "St. Mary's Park E. 145 St. and St. Ann's Ave.",
  },
  {
    id: '5',
    name: 'Haffen Park',
    location: 'Haffen Park Hammersley, Ely, and Gunther Aves.',
  },
  {
    id: '6',
    name: 'Pelham Bay Park',
    location: 'Pelham Bay Park Buckner Blvd. and Middletown Rd.',
  },
  {
    id: '7',
    name: 'St. James Park',
    location: 'St. James Park Jerome Ave. and E. 193d St.',
  },
  {
    id: '8',
    name: 'Stadium Tennis Center at Mill Pond Park',
    location: 'Mill Pond Park Gateway Center Boulevard (Exterior Street and E. 150th Street)',
  },
  {
    id: '9',
    name: 'Van Cortlandt Park',
    location: 'Van Cortlandt Park Stadium - W. 242nd St. and Broadway',
  },
  {
    id: '10',
    name: 'Williamsbridge Oval',
    location: 'E. 208th St. and Bainbridge Ave.',
  },
  {
    id: '11',
    name: 'Cooper Park Tennis Courts',
    location: 'Morgan Ave. between Maspeth Ave and Sharon Street',
  },
  {
    id: '12',
    name: 'Friends Field',
    location: '159th St. between S. Powell & 65th Ave.',
  },
  // Adding more mock data for pagination demonstration
  {
    id: '13',
    name: 'Central Park',
    location: '59th to 110th St., between Fifth Ave and Central Park West',
  },
  {
    id: '14',
    name: 'Prospect Park',
    location: 'Prospect Park West to Flatbush Ave, Park Slope',
  },
  {
    id: '15',
    name: 'Flushing Meadows Corona Park',
    location: 'Between Grand Central Pkwy and Van Wyck Expwy',
  },
  {
    id: '16',
    name: 'Brooklyn Bridge Park',
    location: 'Along the East River waterfront in Brooklyn',
  },
  {
    id: '17',
    name: 'Riverside Park',
    location: 'Along the Hudson River from 72nd to 158th St.',
  },
  {
    id: '18',
    name: 'Battery Park',
    location: 'Southern tip of Manhattan facing the harbor',
  },
  {
    id: '19',
    name: 'Washington Square Park',
    location: 'Fifth Ave, Waverly Pl, W. 4th St, and MacDougal St',
  },
  {
    id: '20',
    name: 'Bryant Park',
    location: 'Between 40th and 42nd St., and Fifth and Sixth Ave',
  },
];
export const equipmentData = [
  {label: 'Tennis', value: 'Tennis'},
  {label: 'Platform Tennis', value: 'Platform Tennis'},
];
export const padelData = [
  {label: 'Padel', value: 'Padel'},
  {label: 'Pickleball', value: 'Pickleball'},
];
export const pTennisData = [
  {label: 'Tennis', value: 'Tennis'},
  {label: 'Platform Tennis', value: 'Platform Tennis'},
  {label: 'Padel', value: 'Padel'},
  {label: 'Pickleball', value: 'Pickleball'},
];
export const pickleBallData = [
  {label: 'Tennis', value: 'Tennis'},
  {label: 'Platform Tennis', value: 'Platform Tennis'},
  {label: 'Padel', value: 'Padel'},
  {label: 'Pickleball', value: 'Pickleball'},
];

export const equipmentTabs = [
  {
    id: '1',
    iconName: 'racket4',
    name: 'racket',
  },
  {
    id: '2',
    iconName: 'racket3',
    name: 'padel',
  },
  {
    id: '3',
    iconName: 'ball',
    name: 'ball',
  },
  {
    id: '4',
    iconName: 'ball',
    name: 'bag',
  },
];

export const sportsData = [
  {
    id: 1,
    sportsTitle: 'tennis',
    icon: 'tennis',
  },
  {
    id: 2,
    sportsTitle: 'platformTennis',
    icon: 'platform-tennis',
  },
  {
    id: 3,
    sportsTitle: 'pickleball',
    icon: 'pickleball',
  },
  {
    id: 4,
    sportsTitle: 'padel',
    icon: 'padel',
  },
];

export const servicesData = [
  {
    id: 1,
    sportsTitle: 'reStringing',
    icon: 'stringing',
  },
  {
    id: 2,
    sportsTitle: 'reGripping',
    icon: 'gripping',
  },
];

export const myMatchesData = [
  {id: 'calendar', title: 'drawerMyMatches.calendar'},
  {id: 'matchHistory', title: 'drawerMyMatches.matchHistory'},
  {id: 'notifications', title: 'drawerMyMatches.notificationsWidget'},
  {id: 'chat', title: 'drawerMyMatches.chatWidget'},
];

export const communityTabs = [
  {id: 1, label: 'communityScreen.home', description: 'Main community page'},
  {id: 2, label: 'communityScreen.playerConnect', description: 'Connect with other players'},
  {id: 3, label: 'communityScreen.reviews', description: 'Equipment and facility reviews'},
  {id: 4, label: 'communityScreen.groups', description: 'Join community groups'},
  {id: 5, label: 'communityScreen.goLife', description: 'Lifestyle content'},
  {id: 6, label: 'communityScreen.upYourGame', description: 'Improve your skills'},
  {id: 7, label: 'communityScreen.goStream', description: 'Watch live streams'},
];

export const invitePlayerTabs = [
  {id: 1, title: 'invitePlayers.invitePlayerTabs.friends'},
  {id: 2, title: 'invitePlayers.invitePlayerTabs.groups'},
  {id: 3, title: 'invitePlayers.invitePlayerTabs.nearby'},
  {id: 4, title: 'invitePlayers.invitePlayerTabs.invite'},
];

export const communityHomeList = [
  {
    id: 1,
    icon: 'chat',
    iconType: 'MaterialIcons',
    source: Images.personMaker,
    title: 'Find Players Now',
    description:
      'Practice, match, play - or start your own player community with Player Connect. Go!',
    screenName: 'CommunityScreen.playerConnect',
  },
  {
    id: 2,
    icon: 'tennis',
    iconType: 'MaterialCommunityIcons',
    source: Images.review,
    title: 'Review: Dunlop SX 300',
    description: 'Provides awesome spin-end control without compromising power.',
    color: '#F59E0B',
    navigate: () => console.log('Navigate to Review'),
  },
  {
    id: 3,
    icon: 'people',
    iconType: 'MaterialIcons',
    source: Images.conversation,
    title: 'Join or start a conversation',
    description: 'Group convos for all levels on all topics of racquet sports. Go!',
    screenName: 'CommunityScreen.groups',
  },
  {
    id: 4,
    icon: 'play-circle',
    iconType: 'Feather',
    source: Images.goDeeper,
    title: 'Video: GoDeeper to play better',
    description: '5 minutes for 5 perfect service placements, for the win. Go!',
    navigate: () => console.log('Navigate to Video'),
  },
  {
    id: 5,
    icon: 'play-circle',
    iconType: 'Feather',
    source: Images.tips,
    title: 'Tips no one talks about',
    description: 'Roger finally reveals why his game lasted so long. Go!',
    navigate: () => console.log('Navigate to Tips'),
  },
  {
    id: 6,
    icon: 'flash',
    iconType: 'Ionicons',
    source: Images.goFit,
    title: 'GoFit: Play injury-free',
    description: 'Three easy moves to protect your wrists, knees, and ankles. Go!',
    screenName: 'CommunityScreen.goLife',
  },
  {
    id: 7,
    icon: 'tennis-ball',
    iconType: 'MaterialCommunityIcons',
    source: Images.dunlop,
    title: 'Dunlop releases CX 200',
    description: 'Check out Dunlops feature article on the gear',
    color: '#F59E0B',
  },
];

export const findPayerTabs = [
  {id: 1, title: 'findPlayer.findPlayerTabs.schedulePlay'},
  {id: 2, title: 'findPlayer.findPlayerTabs.nearby'},
  {id: 3, title: 'findPlayer.findPlayerTabs.friends'},
  {id: 4, title: 'findPlayer.findPlayerTabs.group'},
  {id: 5, title: 'findPlayer.findPlayerTabs.invite'},
];
export const sportsList = [
  {id: 1, title: 'sportsList.tennis', value: 'tennis'},
  {id: 2, title: 'sportsList.platformTennis', value: 'platform-tennis'},
  {id: 3, title: 'sportsList.pickleball', value: 'pickleball'},
  {id: 4, title: 'sportsList.padel', value: 'padel'},
];
export const groupsList = [
  {id: 1, title: 'groupsList.friends', value: 'friends'},
  {id: 2, title: 'groupsList.invited', value: 'invited'},
  {id: 3, title: 'groupsList.sponsored', value: 'sponsored'},
];

export const courtArr = [
  {label: 'Arthur Ashe Stadium', value: 'arthur_ashe_stadium'},
  {label: 'Louis Armstrong Stadium', value: 'louis_armstrong_stadium'},
  {label: 'Grandstand (USTA BJK NTC)', value: 'grandstand_ustabjkntc'},
  {label: 'The West Side Tennis Club (Forest Hills)', value: 'west_side_tennis_club'},
  {label: 'Forest Hills Stadium', value: 'forest_hills_stadium'},
  {label: 'Vanderbilt Tennis Club (Grand Central)', value: 'vanderbilt_tennis_club'},
  {label: "Sportime Randall's Island", value: 'sportime_randalls_island'},
  {label: 'Central Park Tennis Center', value: 'central_park_tennis_center'},
  {label: 'Prospect Park Tennis Center', value: 'prospect_park_tennis_center'},
  {label: 'Hudson River Park Tennis Courts', value: 'hudson_river_park_tennis_courts'},
  {label: 'Riverside Park Tennis Courts', value: 'riverside_park_tennis_courts'},
  {label: 'McCarren Park Tennis Center', value: 'mccarren_park_tennis_center'},
  {label: 'Astoria Park Tennis Courts', value: 'astoria_park_tennis_courts'},
  {label: 'East River Park Tennis Courts', value: 'east_river_park_tennis_courts'},
  {label: 'Roosevelt Island Tennis Courts', value: 'roosevelt_island_tennis_courts'},
];
export const brandOptions = [
  {label: '2UNDR', value: '2undr'},
  {label: 'adidas', value: 'adidas'},
  {label: 'ASICS', value: 'asics'},
  {label: 'Augusta', value: 'augusta'},
  {label: 'Aer-Flo', value: 'aer_flo'},
  {label: 'Ashaway', value: 'ashaway'},
  {label: 'Babolat', value: 'babolat'},
  {label: 'BloqUV', value: 'bloquv'},
  {label: 'Bolle', value: 'bolle'},
  {label: 'Bullpadel', value: 'bullpadel'},
  {label: 'Carlton', value: 'carlton'},
  {label: 'Clif', value: 'clif'},
  {label: 'courtgirl.', value: 'courtgirl'},
  {label: 'CRBN Pickleball', value: 'crbn_pickleball'},
  {label: 'CRUSH', value: 'crush'},
  {label: 'Diadem', value: 'diadem'},
  {label: 'Dunlop', value: 'dunlop'},
  {label: 'DuraFast', value: 'durafast'},
  {label: 'Edwards', value: 'edwards'},
  {label: 'E-Force', value: 'e_force'},
  {label: 'Electrum Pickleball', value: 'electrum_pickleball'},
  {label: 'Engage Pickleball', value: 'engage_pickleball'},
  {label: 'Faye + Florie', value: 'faye_florie'},
  {label: 'FILA', value: 'fila'},
  {label: 'Franklin', value: 'franklin'},
  {label: 'GAMMA', value: 'gamma'},
  {label: 'Gearbox', value: 'gearbox'},
  {label: 'GLDN PNT', value: 'gldn_pnt'},
  {label: 'GRUVN', value: 'gruvn'},
  {label: 'Har-Tru', value: 'har_tru'},
  {label: 'HEAD', value: 'head'},
  {label: 'Headsweats', value: 'headsweats'},
  {label: 'Hirostar', value: 'hirostar'},
  {label: 'Holloway', value: 'holloway'},
  {label: 'Hyperice', value: 'hyperice'},
  {label: 'Igloo', value: 'igloo'},
  {label: 'JOOLA', value: 'joola'},
  {label: 'K-Swiss', value: 'k_swiss'},
  {label: 'Lacoste', value: 'lacoste'},
  {label: 'Laserfibre', value: 'laserbibre'},
  {label: 'Lasso', value: 'lasso'},
  {label: 'Lobster', value: 'lobster'},
  {label: 'Lotto', value: 'lotto'},
  {label: 'Lucky In Love', value: 'lucky_in_love'},
  {label: 'Luxilon', value: 'luxilon'},
  {label: 'Master Athletics', value: 'master_athletics'},
  {label: 'Nike', value: 'nike'},
  {label: 'New Balance', value: 'new_balance'},
  {label: 'NOX', value: 'nox'},
  {label: 'OnCourt OffCourt', value: 'oncourt_offcourt'},
  {label: 'OOFos', value: 'oofos'},
  {label: 'Onix', value: 'onix'},
  {label: 'OS1st', value: 'os1st'},
  {label: 'Paddletek', value: 'paddletek'},
  {label: 'Penn', value: 'penn'},
  {label: 'Pickleball Tutor', value: 'pickleball_tutor'},
  {label: 'Pro Band', value: 'pro_band'},
  {label: 'Pro Kennex', value: 'pro_kennex'},
  {label: 'Pro-Tec', value: 'pro_tec'},
  {label: 'ProXR', value: 'proxr'},
  {label: 'Putterman', value: 'putterman'},
  {label: 'Racquet Inc.', value: 'racquet_inc'},
  {label: 'Revo', value: 'revo'},
  {label: 'Redvanly', value: 'redvanly'},
  {label: 'Rol-Dri', value: 'rol_dri'},
  {label: 'Sit High Chair Co.', value: 'sit_high_chair_co'},
  {label: 'Six Zero', value: 'six_zero'},
  {label: 'Slinger', value: 'slinger'},
  {label: 'Solinco', value: 'solinco'},
  {label: 'Swiftwick', value: 'swiftwick'},
  {label: 'tasc', value: 'tasc'},
  {label: 'Tecnifibre', value: 'tecnifibre'},
  {label: 'Tennis Tutor', value: 'tennis_tutor'},
  {label: 'Tenn-Tube', value: 'tenn_tube'},
  {label: 'Theragun', value: 'theragun'},
  {label: 'Thorlo', value: 'thorlo'},
  {label: 'Tourna', value: 'tourna'},
  {label: 'Under Armour', value: 'under_armour'},
  {label: 'Viking', value: 'viking'},
  {label: 'Volair', value: 'volair'},
  {label: 'Völkl', value: 'volkl'},
  {label: 'Vulcan', value: 'vulcan'},
  {label: 'Wilson', value: 'wilson'},
  {label: 'Xenon', value: 'xenon'},
  {label: 'Yonex', value: 'yonex'},
  {label: 'Zensah', value: 'zensah'},
];

export const groupTypeOptions = [
  {label: 'Public', value: 'public', id: 3},
  {label: 'Private', value: 'private', id: 2},
  {label: 'Hidden', value: 'hidden', id: 1},
];

export const groupTagsOptions = [
  {label: 'Beginner', value: 'beginner'},
  {label: 'Advanced', value: 'advanced'},
];

export const predefinedTags: string[] = [
  'Tennis',
  'Padel',
  'Pro',
  'Semi-Pro',
  'Beginner',
  'Women-only',
  'Mixed-Doubles',
  '2.0',
  '3.0',
  '4.0',
  '5.0',
];

export const certificationOptions = [
  {id: '1', label: 'getCertifiedScreen.recCoachWorkshop'},
  {id: '2', label: 'getCertifiedScreen.level1'},
  {id: '3', label: 'getCertifiedScreen.level2'},
  {id: '4', label: 'getCertifiedScreen.level3'},
  {id: '5', label: 'getCertifiedScreen.specialtyWorkshops'},
  {id: '6', label: 'getCertifiedScreen.ptrw'},
];
