import {toaster} from '@/utils/commonFunctions';
import api, {handleApiError, tokenStorage} from './api';

export interface updateCoachProfile {
  profile_description: string;
  certifications: string[];
  is_private: boolean;
  public_courts: string[];
  sports_clubs: string[];
  video_coaching: number;
  rate: number;
  other_services: string[];
  skills: string[];
  player_types: string[];
}

// API paths
const API_PATHS = {
  updateCoachProfile: 'update-coach-profile',
};

export const updateCoachProfile = async (data: updateCoachProfile): Promise<any> => {
  try {
    console.log('Updating user profile with data:', data);

    // Get the access token
    const accessToken = tokenStorage.getString('accessToken');

    if (!accessToken) {
      throw new Error('No access token available. Please login again.');
    }

    // Make the API call with the authorization header
    const response = await api.post(API_PATHS.updateCoachProfile, data, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
    });

    console.log('Update user profile response:', response);

    // Check if the response is successful
    if (response.status !== 200) {
      console.log('API returned non-200 status:', response.status);
      throw new Error(
        `API error: ${response.status} - ${response.data?.message || 'Failed to update profile'}`,
      );
    }

    // Check if the response data indicates failure
    if (response.data.status === false) {
      console.log('API returned status=false in data');
      toaster('error', response.data.message, 'top');
      throw new Error(response.data.message || 'Failed to update profile');
    }

    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

export const getCertificateList = async () => {
  try {
    const response = await api.get('/data/certificate-lists');
    if (response.status) {
      return response?.data;
    }
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};
