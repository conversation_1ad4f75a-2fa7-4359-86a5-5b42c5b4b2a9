import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    root: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    container: {
      flex: 1,
    },
    flex1: {
      flex: 1,
    },
    content: {
      flexGrow: 1,
      paddingBottom: 16,
      paddingHorizontal: 16,
    },
    searchInput: {
      color: theme.colors.white,
      borderRadius: 30,
      height: 47,
      marginBottom: -10,
    },
    searchContainer: {
      paddingHorizontal: 16,
      flexDirection: 'row',
      gap: 20,
      alignItems: 'center',
      marginBottom: 16,
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: 10,
      justifyContent: 'space-between',
      marginTop: 20,
    },
  });
