import React, {useC<PERSON>back, useMemo, useState} from 'react';
import {View, FlatList, TouchableWithoutFeedback, ActivityIndicator} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {RouteProp, useRoute} from '@react-navigation/native';
import {createStyles} from './styles';
import CInput from '@/components/CInput';
import PlayerCard from '@/components/PlayerCard';
import CustomModal from '@/components/Modal';
import CButton from '@/components/CButton';
import {Header, NoData, SafeAreaView} from '@/components';
import useTranslation from '@/hooks/useTranslation';
import {useInfiniteQuery, useQueryClient} from '@tanstack/react-query';
import {toaster} from '@/utils/commonFunctions';
import RefreshControl from '@/components/RefreshControl';
import CLoader from '@/components/CLoader';
import useDebounce from '@/hooks/useDebounce';
import {useConfigStore} from '@/store';
import {getRequestsList, useAcceptTheRequest} from '@/hooks/queries/groups';
import {RootStackParamList} from '@/navigation';

interface MembersData {
  id: string;
  group_name: string;
  total_members: number;
  highlighted: boolean;
  type: string;
  locked: boolean;
  group_image: string;
}

interface ApiResponse {
  status: boolean;
  data: MembersData[];
  pagination: {
    total: number;
    totalPages: number;
    page: number;
    perPage: number;
  };
}

interface Member {
  id: string;
  profile_pic?: string;
  [key: string]: any;
}

const RequestList = () => {
  const {setSelectedGroupMembers, getSelectedGroupMembers} = useConfigStore();

  const theme = useThemeStore();
  const styles = createStyles(theme);
  // Fix 1: Initialize search as empty string instead of null
  const [search, setSearch] = React.useState<string>('');
  const [selectedMembers, setSelectedMembers] = React.useState<Member[]>(
    getSelectedGroupMembers() || [],
  );

  const [isModalVisible, setIsModalVisible] = useState(false);

  const [rejectedReason, setRejectedReason] = useState<string>('');

  const {groupId} = useRoute<RouteProp<RootStackParamList, 'RequestList'>>().params;

  const acceptOrRejectTheRequest = useAcceptTheRequest();

  const [selectedItem, setSelectedItem] = useState(null);

  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);

  const {t} = useTranslation();
  const maxMembers = 25;

  const handleSelect = (item: Member) => {
    setSelectedMembers((prev: Member[]) => {
      const isSelected = prev.some(member => member.id === item.id);
      if (isSelected) {
        const newSelection = prev.filter(member => member.id !== item.id);
        setSelectedGroupMembers(newSelection);
        return newSelection;
      } else {
        if (prev.length >= maxMembers) {
          toaster('error', `You can select a maximum of ${maxMembers} members`, 'top');
          return prev;
        }
        const newSelection = [...prev, item];
        setSelectedGroupMembers(newSelection);
        return newSelection;
      }
    });
  };

  const renderMember = ({item}: any) => {
    return (
      <TouchableWithoutFeedback key={item.id}>
        <PlayerCard
          playerData={item}
          onSelect={() => handleSelect(item)}
          isSelected={selectedMembers.some(member => member.id === item.id)}
          borderColor={theme.colors.activeColor}
          showActionButton={true}
          showAdd={false}
          onAcceptPress={() => {
            handleAcceptOrRejectTheRequest(groupId, false, item.user_id, 'accepted', '');
          }}
          onRejectPress={() => {
            setSelectedItem(item);
            setIsModalVisible(true);
          }}
          actionLoading={selectedUserId === item.user_id}
        />
      </TouchableWithoutFeedback>
    );
  };

  // Fix 2: Handle debounced search properly
  const debouncedSearch = useDebounce(search, 500);

  const {
    data: groupData,
    isLoading,
    error,
    refetch,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isRefetching,
  } = useInfiniteQuery<ApiResponse>({
    // Fix 3: Include groupId in queryKey for proper cache management
    queryKey: ['request-users-list', groupId, debouncedSearch],
    gcTime: 0,
    retry: 1, // Add retry limit
    staleTime: 0, // Ensure fresh data

    queryFn: async ({pageParam = 1}) => {
      try {
        // Fix 4: Pass search parameter properly (empty string instead of null)
        const searchParam = debouncedSearch?.trim() || '';
        const response = await getRequestsList(pageParam as number, searchParam, groupId);

        // Ensure the response has the correct structure
        if (!response || !response.data) {
          return {
            status: false,
            data: [],
            pagination: {total: 0, totalPages: 0, page: 1, perPage: 10},
          };
        }

        // Ensure pagination exists and has correct property names
        const pagination = response.pagination || {};
        return {
          ...response,
          pagination: {
            total: pagination.total || 0,
            totalPages: pagination.totalPages || 0,
            page: pagination.page || pagination.currentPage || pageParam,
            perPage: pagination.perPage || 10,
          },
        };
      } catch (error) {
        console.error('Error fetching requests list:', error);
        return {
          status: false,
          data: [],
          pagination: {total: 0, totalPages: 0, page: pageParam, perPage: 10},
        };
      }
    },
    getNextPageParam: lastPage => {
      // Fix: Add null checks and ensure pagination exists
      if (lastPage?.pagination?.page && lastPage?.pagination?.totalPages) {
        if (lastPage.pagination.page < lastPage.pagination.totalPages) {
          return lastPage.pagination.page + 1;
        }
      }
      return undefined;
    },
    initialPageParam: 1,
  });

  console.log('isLoading', isLoading);
  console.log('error', error);
  console.log('groupData', groupData);

  const UsersListData = useMemo(() => {
    if (!groupData?.pages) return [];

    return groupData.pages.flatMap(page => {
      if (!page?.data || !Array.isArray(page.data)) return [];
      return page.data.map((user: any) => ({
        ...user,
        image: user.profile_pic,
      }));
    });
  }, [groupData?.pages]);

  const queryClient = useQueryClient();

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

  const ListFooterComponent = useMemo(() => {
    return hasNextPage || isFetchingNextPage ? (
      <View style={{paddingVertical: 20, alignItems: 'center'}}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    ) : null;
  }, [hasNextPage, isFetchingNextPage, theme.colors.primary]);

  const handleAcceptOrRejectTheRequest = async (
    groupId: string,
    refetch: boolean = true,
    userId: string,
    action: string,
    reason: string,
  ) => {
    try {
      setSelectedUserId(userId);
      const response = await acceptOrRejectTheRequest.mutateAsync({
        groupId: groupId,
        refetch: refetch,
        userId: userId,
        action: action,
        reason: reason,
      });

      if (response?.status === true) {
        toaster('success', response.message, 'top');
        // Fix 5: Update queryKey to match the one used in useInfiniteQuery
        queryClient.setQueryData(
          ['request-users-list', groupId, debouncedSearch],
          (oldData: any) => {
            if (!oldData) return oldData;
            return {
              ...oldData,
              pages: oldData.pages.map((page: any) => ({
                ...page,
                data: page.data.filter((user: any) => user.user_id !== userId),
              })),
            };
          },
        );
        setRejectedReason('');
        setSelectedItem(null);
        setIsModalVisible(false);
        setSelectedUserId(null);
        queryClient.invalidateQueries({queryKey: ['group-details']});
      }
    } catch (error: any) {
      toaster('error', error.message, 'top');
    } finally {
      setSelectedUserId(null);
    }
  };

  return (
    <SafeAreaView includeTop={true} includeBottom={false} style={styles.root}>
      <Header
        leftIcon={{
          name: 'side-menu',
          size: 24,
          color: theme.colors.white,
          containerStyle: theme.colors.primary,
        }}
        rightIcons={[
          {name: 'notification', size: 24, badge: 0},
          {name: 'chat', size: 24, badge: 14},
        ]}
        pageTitle={'Requests member list'}
        backgroundColor="transparent"
      />
      <View style={styles.content}>
        <CInput
          variant="dark"
          inputStyle={styles.searchInput}
          placeholder={t('addMembersScreen.searchPlaceholder')}
          placeholderTextColorStyle={theme.colors.secondary}
          value={search}
          onChangeText={setSearch}
        />
        {isLoading ? (
          <CLoader />
        ) : (
          <FlatList
            data={UsersListData}
            renderItem={renderMember}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.membersList}
            showsVerticalScrollIndicator={false}
            onEndReached={handleEndReached}
            onEndReachedThreshold={0.5}
            refreshControl={
              <RefreshControl refreshing={isRefetching} onRefresh={() => refetch()} />
            }
            ListEmptyComponent={() => <NoData />}
            ListFooterComponent={ListFooterComponent}
            removeClippedSubviews={false}
            maxToRenderPerBatch={20}
            initialNumToRender={20}
            windowSize={5}
          />
        )}
      </View>
      <CustomModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        showCloseButton={true}
        title="Reject Request">
        <View>
          <CInput
            variant="dark"
            inputStyle={styles.searchInput}
            placeholder={t('addMembersScreen.searchPlaceholder')}
            placeholderTextColorStyle={theme.colors.secondary}
            value={rejectedReason}
            onChangeText={setRejectedReason}
          />
          <CButton
            title={t('ParksSearchScreen.submit')}
            onPress={() => {
              handleAcceptOrRejectTheRequest(
                groupId,
                false,
                selectedItem?.user_id,
                'rejected',
                rejectedReason,
              );
            }}
            variant="primary"
            containerStyle={{
              height: 60,
              marginTop: 50,
            }}
            loading={acceptOrRejectTheRequest.isPending}
          />
        </View>
      </CustomModal>
    </SafeAreaView>
  );
};

export default RequestList;
