

<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> screens/Notification/index.tsx</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="styles/style.css">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
             
                <a href="index.html">
                    <h1 class="navbar-item">GoRaqt Documentation</h1>
                </a>
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    Documentation
                </a>
                
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
                <div class="search-wrapper">
                    <input id="search" type="text" placeholder="Search docs..." class="input">
                </div>
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Global</h3><ul><li><a href="global.html#HIDE_TAB_SCREENS">HIDE_TAB_SCREENS</a></li><li><a href="global.html#SENSITIVE_FIELDS">SENSITIVE_FIELDS</a></li><li><a href="global.html#addBreadcrumb">addBreadcrumb</a></li><li><a href="global.html#apiCall">apiCall</a></li><li><a href="global.html#authenticateWithBiometrics">authenticateWithBiometrics</a></li><li><a href="global.html#calculateAge">calculateAge</a></li><li><a href="global.html#checkAuthStatusApi">checkAuthStatusApi</a></li><li><a href="global.html#checkLocationPermission">checkLocationPermission</a></li><li><a href="global.html#cleanMemoryIfNeeded">cleanMemoryIfNeeded</a></li><li><a href="global.html#clearAllData">clearAllData</a></li><li><a href="global.html#configureStatusBar">configureStatusBar</a></li><li><a href="global.html#createHealthService">createHealthService</a></li><li><a href="global.html#deleteFcmTokenFromDB">deleteFcmTokenFromDB</a></li><li><a href="global.html#deleteNotification">deleteNotification</a></li><li><a href="global.html#disableBiometrics">disableBiometrics</a></li><li><a href="global.html#enableBiometrics">enableBiometrics</a></li><li><a href="global.html#forgotPasswordApi">forgotPasswordApi</a></li><li><a href="global.html#formatBirthdate">formatBirthdate</a></li><li><a href="global.html#getAddressFromCoordinates">getAddressFromCoordinates</a></li><li><a href="global.html#getCurrentLocationWithAddress">getCurrentLocationWithAddress</a></li><li><a href="global.html#getCurrentPosition">getCurrentPosition</a></li><li><a href="global.html#getCurrentRegion">getCurrentRegion</a></li><li><a href="global.html#getCurrentUserApi">getCurrentUserApi</a></li><li><a href="global.html#getNotificationsList">getNotificationsList</a></li><li><a href="global.html#getTabBarVisibility">getTabBarVisibility</a></li><li><a href="global.html#getUnreadNotificationsCount">getUnreadNotificationsCount</a></li><li><a href="global.html#getUserDetails">getUserDetails</a></li><li><a href="global.html#getUserProfile">getUserProfile</a></li><li><a href="global.html#handleNotificationNavigation">handleNotificationNavigation</a></li><li><a href="global.html#handleTextureError">handleTextureError</a></li><li><a href="global.html#initSentry">initSentry</a></li><li><a href="global.html#isBiometricsAvailable">isBiometricsAvailable</a></li><li><a href="global.html#isBiometricsEnabled">isBiometricsEnabled</a></li><li><a href="global.html#lastCacheClearTime">lastCacheClearTime</a></li><li><a href="global.html#logError">logError</a></li><li><a href="global.html#logMessage">logMessage</a></li><li><a href="global.html#loginWithEmail">loginWithEmail</a></li><li><a href="global.html#loginWithEmailApi">loginWithEmailApi</a></li><li><a href="global.html#logoutApi">logoutApi</a></li><li><a href="global.html#markAllNotificationsAsRead">markAllNotificationsAsRead</a></li><li><a href="global.html#markNotificationAsRead">markNotificationAsRead</a></li><li><a href="global.html#navigateBackFromExample">navigateBackFromExample</a></li><li><a href="global.html#navigateToOptimizedFlatListExample">navigateToOptimizedFlatListExample</a></li><li><a href="global.html#otpResendApi">otpResendApi</a></li><li><a href="global.html#otpVerifyApi">otpVerifyApi</a></li><li><a href="global.html#prefetchInitialQueries">prefetchInitialQueries</a></li><li><a href="global.html#prefetchPost">prefetchPost</a></li><li><a href="global.html#prefetchUser">prefetchUser</a></li><li><a href="global.html#refreshTokenApi">refreshTokenApi</a></li><li><a href="global.html#registerWithEmail">registerWithEmail</a></li><li><a href="global.html#registerWithEmailApi">registerWithEmailApi</a></li><li><a href="global.html#requestLocationPermission">requestLocationPermission</a></li><li><a href="global.html#resetPasswordApi">resetPasswordApi</a></li><li><a href="global.html#saveFcmTokenInDB">saveFcmTokenInDB</a></li><li><a href="global.html#schema">schema</a></li><li><a href="global.html#scrubObject">scrubObject</a></li><li><a href="global.html#scrubSensitiveData">scrubSensitiveData</a></li><li><a href="global.html#setCustomStatusBar">setCustomStatusBar</a></li><li><a href="global.html#setDarkStatusBar">setDarkStatusBar</a></li><li><a href="global.html#setLightStatusBar">setLightStatusBar</a></li><li><a href="global.html#setNavigationContext">setNavigationContext</a></li><li><a href="global.html#setStatusBarHidden">setStatusBarHidden</a></li><li><a href="global.html#setUserContext">setUserContext</a></li><li><a href="global.html#setupMemoryCleaning">setupMemoryCleaning</a></li><li><a href="global.html#setupTextureErrorHandlers">setupTextureErrorHandlers</a></li><li><a href="global.html#signInWithFacebook">signInWithFacebook</a></li><li><a href="global.html#signInWithGoogle">signInWithGoogle</a></li><li><a href="global.html#startTransaction">startTransaction</a></li><li><a href="global.html#styles">styles</a></li><li><a href="global.html#updateUserProfile">updateUserProfile</a></li><li><a href="global.html#useAuthStatus">useAuthStatus</a></li><li><a href="global.html#useCurrentUser">useCurrentUser</a></li><li><a href="global.html#useLogin">useLogin</a></li><li><a href="global.html#useRegister">useRegister</a></li><li><a href="global.html#useSafeArea">useSafeArea</a></li><li><a href="global.html#useSocialLogin">useSocialLogin</a></li><li><a href="global.html#useUpdateUserProfile">useUpdateUserProfile</a></li><li><a href="global.html#useUserProfile">useUserProfile</a></li></ul></div><div class="category"><h2>Components</h2><h3>Components</h3><ul><li><a href="Advertisement.html">Advertisement</a></li><li><a href="BiometricGate.html">BiometricGate</a></li><li><a href="CButton.html">CButton</a></li><li><a href="CreateGoRaqtProfile.html">CreateGoRaqtProfile</a></li><li><a href="CustomMapView.html">CustomMapView</a></li><li><a href="CustomMarker.html">CustomMarker</a></li><li><a href="NotificationCard.html">NotificationCard</a></li><li><a href="OfferBanner.html">OfferBanner</a></li><li><a href="Subscription.html">Subscription</a></li></ul></div><div class="category"><h2>Interfaces</h2><h3>Interfaces</h3><ul><li><a href="AdvertisementProps.html">AdvertisementProps</a></li><li><a href="ApiResponse.html">ApiResponse</a></li><li><a href="BiometricGateProps.html">BiometricGateProps</a></li><li><a href="CButtonProps.html">CButtonProps</a></li><li><a href="CreateProfileProps.html">CreateProfileProps</a></li><li><a href="CustomMarkerProps.html">CustomMarkerProps</a></li><li><a href="DrawerItemType.html">DrawerItemType</a></li><li><a href="ForgotPasswordScreenProps.html">ForgotPasswordScreenProps</a></li><li><a href="Notification.html">Notification</a></li><li><a href="NotificationCardProps.html">NotificationCardProps</a></li><li><a href="PaginationState.html">PaginationState</a></li><li><a href="PermissionsScreenProps.html">PermissionsScreenProps</a></li><li><a href="RouteParams.html">RouteParams</a></li><li><a href="SentryNavigationContainerProps.html">SentryNavigationContainerProps</a></li><li><a href="SocialRegisterRequest.html">SocialRegisterRequest</a></li><li><a href="SplashScreenProps.html">SplashScreenProps</a></li><li><a href="TermsOfServiceScreenProps.html">TermsOfServiceScreenProps</a></li><li><a href="UseLogoutReturn.html">UseLogoutReturn</a></li></ul><h3><a href="global.html">Global</a></h3></div><div class="category"><h2>Navigation</h2><h3>Components</h3><ul><li><a href="DrawerNavigator.html">DrawerNavigator</a></li><li><a href="SentryNavigationContainer.html">SentryNavigationContainer</a></li></ul></div><div class="category"><h2>Screens</h2><h3>Components</h3><ul><li><a href="CommunityDetails.html">CommunityDetails</a></li><li><a href="DirectFetchTest.html">DirectFetchTest</a></li><li><a href="EmailSignupScreen.html">EmailSignupScreen</a></li><li><a href="ForgotPasswordScreen.html">ForgotPasswordScreen</a></li><li><a href="LoginScreen.html">LoginScreen</a></li><li><a href="MyMatchesScreen.html">MyMatchesScreen</a></li><li><a href="NotificationsListScreen.html">NotificationsListScreen</a></li><li><a href="PermissionsScreen.html">PermissionsScreen</a></li><li><a href="SignupScreen.html">SignupScreen</a></li><li><a href="SplashScreen.html">SplashScreen</a></li><li><a href="TermsOfServiceScreen.html">TermsOfServiceScreen</a></li><li><a href="Verification.html">Verification</a></li></ul></div><div class="category"><h2>Utils</h2><h3>Global</h3><ul><li><a href="global.html#api">api</a></li><li><a href="global.html#socialLoginApi">socialLoginApi</a></li><li><a href="global.html#useLogout">useLogout</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Source</p>
                    <h1>screens/Notification/index.tsx</h1>
                </header>
                



    
    <section>
        <article>
            <pre class="prettyprint source linenums"><code>import React, {useState, useEffect} from 'react';
import {View, TouchableOpacity, ScrollView, ActivityIndicator, RefreshControl} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {CustomModal, Header, Icon, NoData} from '@/components';
import {OfferBanner} from '@/components/common/OfferBanner';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation';
import NotificationCard from '@/components/NotificationCard';
import {Images} from '@/config';
import {SwipeListView} from 'react-native-swipe-list-view';
import Typography from '@/components/Typography';
import {styles as createStyles} from './styles';
import {SafeAreaView} from '@/components/common';
import useTranslation from '@/hooks/useTranslation';
import {
  getNotificationsList,
  markNotificationAsRead,
  deleteNotification,
  markAllNotificationsAsRead,
} from '@/services/notificationsApi';
import DeleteAccountModal, {DeleteAccountModalHandles} from '@/components/DeleteAccountModal';
import {useNotificationStore} from '@/store/notificationStore';
import {useMutation} from '@tanstack/react-query';
import CLoader from '@/components/CLoader';
import {ImageSourcePropType} from 'react-native';

type NavigationProp = StackNavigationProp&lt;RootStackParamList>;

/**
 * @category Interfaces
 * @typedef {Object} Notification
 * @property {string} id - Unique identifier for the notification
 * @property {string} title - Display title of the notification
 * @property {string} type - Type/category of the notification
 * @property {ImageSourcePropType} image - Image source for the notification icon
 * @property {string} highlight - Whether the notification should be highlighted
 * @property {string} created_at - Display time for the notification
 * @property {string} community - Community name associated with the notification
 * @property {boolean} is_read - Whether the notification has been read (1 for read, 0 for unread)
 */
interface Notification {
  id: string;
  title: string;
  type: string;
  image: ImageSourcePropType;
  highlight?: boolean;
  created_at?: string;
  community?: string;
  is_read?: boolean;
}

/**
 * @category Interfaces
 * @typedef {Object} PaginationNotification
 * @property {number} page - Current page number
 * @property {boolean} loadMore - Whether more data can be loaded
 */
interface PaginationState {
  page: number;
  loadMore: boolean;
}

/**
 * @component
 * @category Screens
 *
 * @description
 * Displays a list of notifications with swipe-to-delete functionality,
 * mark as read/unread capabilities, and pagination support.
 *
 * @return {JSX.Element} The notifications list screen component
 */
const NotificationsListScreen = () => {
  const theme = useThemeStore();
  const navigation = useNavigation&lt;NavigationProp>();
  const {t} = useTranslation();
  const [listData, setListData] = useState&lt;Notification[]>([]);
  const styles = createStyles(theme);
  const [openRowKey, setOpenRowKey] = useState&lt;string | null>(null);

  const [loader, setLoader] = useState&lt;boolean>(false);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedNotificationId, setSelectedNotificationId] = useState&lt;string | null>(null);
  const [paginationLoader, setPaginationLoader] = useState(false);
  const [pagination, setPagination] = useState&lt;PaginationState>({
    page: 1,
    loadMore: false,
  });
  const [refreshLoader, setRefreshLoader] = useState&lt;boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteLoader, setDeleteLoader] = useState(false);
  const deleteModalRef = React.useRef&lt;DeleteAccountModalHandles>(null);

  const notificationStore = useNotificationStore();
  const fetchUnreadCount = notificationStore.fetchUnreadCount;

  /**
   * @function markAsReadMutation
   * @memberof NotificationsListScreen
   * @description
   * Mutation for marking a single notification as read
   * Updates local state and refreshes unread count on success
   * @param {string} notificationId - Id of notification
   */
  const markAsReadMutation = useMutation({
    mutationFn: (notificationId: string) => markNotificationAsRead(notificationId),
    onSuccess: (_, notificationId) => {
      setListData(prevData =>
        prevData.map(notification =>
          notification.id === notificationId ? {...notification, is_read: true} : notification,
        ),
      );
      // Update unread count after marking as read
      fetchUnreadCount();
    },
    onError: error => {
      console.error('Error marking notification as read:', error);
    },
  });

  /**
   * @function deleteNotificationMutation
   * @memberof NotificationsListScreen
   * @description
   * Mutation for deleting a notification
   * Removes notification from local state and refreshes unread count on success
   * @param {string} notificationId - Id of notification
   */
  const deleteNotificationMutation = useMutation({
    mutationFn: (notificationId: string) => deleteNotification(notificationId),
    onSuccess: (_, notificationId) => {
      setListData(prevData => prevData.filter(notification => notification.id !== notificationId));
      setIsModalVisible(false);
      setSelectedNotificationId(null);
      deleteModalRef.current?.close();
      fetchUnreadCount();
    },
    onError: error => {
      console.error('Error deleting notification:', error);
    },
  });

  /**
   * @function markAllAsReadMutation
   * @memberof NotificationsListScreen
   * @description
   * Mutation for marking all notifications as read
   * Updates all notifications in local state to read status
   */
  const markAllAsReadMutation = useMutation({
    mutationFn: () => markAllNotificationsAsRead(),
    onSuccess: () => {
      setListData(prevData =>
        prevData.map(notification => ({
          ...notification,
          is_read: true,
        })),
      );
      fetchUnreadCount();
    },
    onError: error => {
      console.error('Error marking all notifications as read:', error);
    },
  });

  /**
   * @function fetchNotifications
   * @memberof NotificationsListScreen
   * @description
   * Fetches notifications from the API with pagination support
   * @param {number} page - The page number to fetch
   * @param {boolean} [isRefresh=false] - Whether this is a refresh operation
   * @param {boolean} [loader=false] - Whether to show the main loader
   * @returns {Promise&lt;void>}
   */
  const fetchNotifications = async (page: number, isRefresh = false, loader = false) => {
    if (loader) {
      setLoader(true);
    }
    try {
      if (!isRefresh) setPaginationLoader(true);
      const response = await getNotificationsList(page, 'all');
      const newNotifications = response?.data || [];

      setListData(prevData => (isRefresh ? newNotifications : [...prevData, ...newNotifications]));
      setPagination(prev => ({
        ...prev,
        page,
        loadMore: newNotifications.length > 0, // Stop loading if no more data
      }));
      setLoader(false);
      // Fetch unread count after fetching notifications
      await fetchUnreadCount();
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setLoader(false);
    } finally {
      setPaginationLoader(false);
      setRefreshLoader(false);
      setLoader(false);
    }
  };

  // Load more data for pagination
  const loadMoreData = () => {
    if (pagination.loadMore &amp;&amp; !paginationLoader) {
      fetchNotifications(pagination.page + 1, false, false);
    }
  };

  // Refresh notifications
  const refreshNotifications = () => {
    setRefreshLoader(true);
    fetchNotifications(1, true, false);
  };

  useEffect(() => {
    fetchNotifications(1, true, true);
  }, []);

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  /**
   * @function handleNotificationNavigation
   * @memberof NotificationsListScreen
   * @description
   * Handles navigation based on notification type and action data
   * @param {Object} notification - The notification object containing navigation data
   */

  const handleNotificationNavigation = (notification: any) => {
    const actionData = JSON.parse(notification?.extra_data);

    if (!notification || !notification.extra_data) return;
    const {action_type, channel_url, group_id} = actionData;

    switch (action_type) {
      case 'chat_detail':
        navigation.navigate('SendBird', {
          channelUrl: channel_url,
          group_id,
        });
        break;
      case 'group_join_request':
        navigation.navigate('RequestList', {groupId: group_id});
        break;
      case 'accepted_group_join_request':
        navigation.navigate('SendBird', {
          channelUrl: channel_url,
          group_id,
        });
        break;
      case 'rejected_group_join_request':
        navigation.navigate('JoinGroupDetails', {id: group_id, from: 'my-groups'});
        break;
      default:
        break;
    }
  };

  /**
   * @function handleNotificationPress
   * @memberof NotificationsListScreen
   * @description
   * Handles notification press events
   * Marks unread notifications as read and navigates to the appropriate screen
   * @param {Object} Notification - The notification that was pressed
   */
  const handleNotificationPress = async (item: Notification) => {
    if (item?.is_read === 1) {
      handleNotificationNavigation(item);
    } else {
      try {
        if (!item.is_read) {
          // Update UI state immediately
          setListData(prevData =>
            prevData.map(notification =>
              notification.id === item.id ? {...notification, is_read: true} : notification,
            ),
          );
          markAsReadMutation.mutate(item.id);
        }
        console.log('item.id=====>>>>>', item.id);
      } catch (error) {
        console.error('Error marking notification as read:', error);
      }
      handleNotificationNavigation(item);
    }
  };

  const handleDeleteNotification = async (notificationId: string) => {
    setDeleteLoader(true);
    try {
      await deleteNotificationMutation.mutateAsync(notificationId);
    } finally {
      setDeleteLoader(false);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsReadMutation.mutateAsync();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const handleDeletePress = (notificationId: string) => {
    setSelectedNotificationId(notificationId);
    setShowDeleteModal(true);
    setIsModalVisible(false);
    setTimeout(() => {
      deleteModalRef.current?.open();
    }, 100);
  };

  const renderItem = ({item}: {item: Notification}) => (
    &lt;View style={[styles.rowFront, openRowKey === item.id &amp;&amp; styles.rowFrontOpen]}>
      &lt;View style={styles.cardContainer}>
        &lt;NotificationCard
          title={item.title}
          type={item.type}
          image={item.image || Images.dunlopRound}
          time={item.created_at}
          community={item.community}
          isRead={item.is_read}
          onNotificationPress={() => handleNotificationPress(item)}
        />
      &lt;/View>
    &lt;/View>
  );

  const renderHiddenItem = (data: {item: Notification}) => (
    &lt;View style={styles.rowBack}>
      {/* &lt;TouchableOpacity
        style={[styles.actionButton, styles.moreBtn]}
        onPress={() => {
          setSelectedNotificationId(data.item.id);
          setIsModalVisible(true);
        }}>
        &lt;Icon name="threeDot" size={28} color={theme.colors.white} />
        &lt;Typography variant="communityBtnText" style={styles.actionText}>
          {t('NotificationScreen.more')}
        &lt;/Typography>
      &lt;/TouchableOpacity> */}
      &lt;TouchableOpacity
        style={[styles.actionButton, styles.trashBtn]}
        activeOpacity={0.7}
        onPress={() => handleDeletePress(data.item.id)}>
        &lt;Icon name="delete" size={30} color={theme.colors.white} />
        &lt;Typography variant="communityBtnText" style={styles.actionText}>
          {t('NotificationScreen.trash')}
        &lt;/Typography>
      &lt;/TouchableOpacity>
    &lt;/View>
  );

  return (
    &lt;SafeAreaView style={styles.container}>
      &lt;Header
        leftComponent={
          &lt;TouchableOpacity onPress={() => navigation.goBack()} activeOpacity={0.7}>
            &lt;Icon name="Left-chevron" size={24} color={theme.colors.white} />
          &lt;/TouchableOpacity>
        }
        leftIconButtonStyle={styles.menuButton}
        onLeftPress={openDrawer}
        // rightIcons={[
        //   {name: 'notification', size: 24, badge: unreadCount},
        //   {name: 'chat', size: 24},
        // ]}
        title={t('NotificationScreen.title')}
        backgroundColor={theme.colors.notificationBg}
        transparent={false}
        showBack={false}
      />
      {listData.length > 0 &amp;&amp; listData?.some(obj => obj.is_read !== 1) &amp;&amp; (
        &lt;View style={styles.markAllAsReadContainer}>
          &lt;TouchableOpacity
            onPress={handleMarkAllAsRead}
            activeOpacity={0.7}
            style={styles.markAllAsReadButton}>
            &lt;Typography variant="bodyMedium" align="right" color={theme.colors.white}>
              {t('NotificationScreen.all_read')}
            &lt;/Typography>
          &lt;/TouchableOpacity>
        &lt;/View>
      )}
      {loader ? (
        &lt;CLoader />
      ) : (
        &lt;SwipeListView
          data={listData}
          renderItem={renderItem}
          renderHiddenItem={renderHiddenItem}
          rightOpenValue={-70}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
          closeOnRowPress={true}
          disableRightSwipe={true}
          showsVerticalScrollIndicator={false}
          friction={100}
          tension={100}
          onEndReachedThreshold={0.5}
          swipeToOpenPercent={30}
          onEndReached={loadMoreData}
          onRowOpen={rowKey => {
            setOpenRowKey(rowKey);
          }}
          onRowClose={() => {
            setOpenRowKey(null);
          }}
          ListEmptyComponent={() => &lt;NoData />}
          ListFooterComponent={() =>
            paginationLoader ? (
              &lt;View style={styles.paginationLoader}>
                &lt;ActivityIndicator size="large" color={theme.colors.primary} />
              &lt;/View>
            ) : null
          }
          refreshControl={
            &lt;RefreshControl
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
              refreshing={refreshLoader}
              onRefresh={refreshNotifications}
            />
          }
        />
      )}

      &lt;View style={styles.footer}>
        &lt;OfferBanner text="Invite friends, get 10% off" />
      &lt;/View>
      &lt;CustomModal
        visible={isModalVisible}
        variant="bottom"
        modalContainerStyle={{
          height: '100%',
          width: '100%',
        }}
        showCloseButton
        onClose={() => {
          setIsModalVisible(false);
          setSelectedNotificationId(null);
        }}>
        &lt;ScrollView contentContainerStyle={styles.modalContent}>
          &lt;View style={styles.modalRow}>
            &lt;TouchableOpacity
              style={styles.modalButton}
              onPress={() => {
                setIsModalVisible(false);
                // Add reply functionality here
              }}>
              &lt;View style={styles.iconContainer}>
                &lt;Icon name="reply" size={30} color={theme.colors.white} />
              &lt;/View>
              &lt;Typography
                variant="notificationText"
                color={theme.colors.white}
                style={{
                  marginTop: 5,
                }}>
                {t('NotificationScreen.reply')}
              &lt;/Typography>
            &lt;/TouchableOpacity>

            &lt;TouchableOpacity
              style={styles.modalButton}
              onPress={() => {
                if (selectedNotificationId) {
                  setIsModalVisible(false);
                  handleDeletePress(selectedNotificationId);
                }
              }}>
              &lt;View style={styles.iconContainer}>
                &lt;Icon name="delete" size={32} color={theme.colors.white} />
              &lt;/View>
              &lt;Typography
                variant="notificationText"
                color={theme.colors.white}
                style={{
                  marginTop: 5,
                }}>
                {t('NotificationScreen.trash')}
              &lt;/Typography>
            &lt;/TouchableOpacity>
          &lt;/View>

          &lt;TouchableOpacity
            style={styles.modalFullButton}
            onPress={() => {
              setIsModalVisible(false);
            }}>
            &lt;Typography variant="notificationText" color={theme.colors.white}>
              {t('NotificationScreen.remindMe')}
            &lt;/Typography>
          &lt;/TouchableOpacity>

          {/* Multiple notification function buttons */}
          {[1, 2, 3, 4].map(item => (
            &lt;TouchableOpacity
              key={`notification-function-${item}`}
              style={styles.modalFullButton}
              onPress={() => {
                setIsModalVisible(false);
                // Add notification function here
              }}>
              &lt;Typography variant="notificationText" color={theme.colors.white}>
                {t('NotificationScreen.notificationFunction')}
              &lt;/Typography>
            &lt;/TouchableOpacity>
          ))}
        &lt;/ScrollView>
      &lt;/CustomModal>
      &lt;DeleteAccountModal
        ref={deleteModalRef}
        type="deleteNotification"
        onCancel={() => {
          deleteModalRef.current?.close();
          setShowDeleteModal(false);
          setSelectedNotificationId(null);
        }}
        onConfirm={() => {
          if (selectedNotificationId) {
            handleDeleteNotification(selectedNotificationId);
          }
        }}
        loader={deleteLoader}
      />
    &lt;/SafeAreaView>
  );
};

export default NotificationsListScreen;
</code></pre>
        </article>
    </section>




            </div>
            
            <!-- Footer removed via patch -->
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

<script src="scripts/search.js"> </script>


</body>
</html>
