import React, {use<PERSON><PERSON>back, useMemo, useEffect} from 'react';
import {View, ScrollView, TouchableOpacity, FlatList} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {createStyles} from './styles';
import Typography from '@/components/Typography';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation';
import GroupCard from '@/components/GroupCard/index';
type NavigationProp = StackNavigationProp<RootStackParamList>;
import {Icon, NoData} from '@/components';
import {Shadow} from 'react-native-shadow-2';
import useTranslation from '@/hooks/useTranslation';
import {useSendbirdChat} from '@sendbird/uikit-react-native';
import {getMyGroups, getPublicGroups, toaster} from '@/utils/commonFunctions';
import {useGroupChannel} from '@sendbird/uikit-chat-hooks';
import {useQ<PERSON>y, useQueryClient} from '@tanstack/react-query';
import CLoader from '@/components/CLoader';
import {useAuthStore} from '@/store';
import {getMessaging} from '@react-native-firebase/messaging';
import {getApp} from '@react-native-firebase/app';

interface GroupsData {
  id: string;
  group_name: string;
  total_members: number;
  highlighted: boolean;
  type: string;
  locked: boolean;
  group_image: string;
  group_type: string;
  channel_url: string;
  unread_count?: number;
}
interface ApiResponse {
  status: boolean;
  data: GroupsData[];
  pagination: {
    total: number;
    totalPages: number;
    currentPage: number;
    perPage: number;
  };
}

const GroupCardWrapper = ({item, showLocation = false}: {item: any; showLocation?: boolean}) => {
  const navigation = useNavigation<NavigationProp>();
  const channelUrl = item?.channel_url;
  const group_id = item?.id;
  const {sdk} = useSendbirdChat();
  const {channel} = useGroupChannel(sdk, channelUrl);

  return (
    <GroupCard
      name={item.group_name}
      members={item.total_members}
      highlighted={item.highlighted}
      image={item.group_image}
      location={showLocation ? item.location : undefined}
      unReadCount={item.unread_count}
      onPress={() => {
        if (channel) {
          navigation.navigate('SendBird', {
            channelUrl: channelUrl,
            group_id,
          });
        } else {
          toaster('error', 'User is not connected with Sendbird', 'top');
        }
      }}
    />
  );
};

const Groups: React.FC = () => {
  const theme = useThemeStore();
  const {t} = useTranslation();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp>();
  const {user} = useAuthStore();
  const queryClient = useQueryClient();

  const {data, isLoading, refetch} = useQuery<ApiResponse>({
    queryKey: ['my-groups'],
    refetchOnMount: true,
    gcTime: 0,
    queryFn: async ({pageParam = 1}) => {
      const response = await getMyGroups('', pageParam as number);
      return response || {data: []};
    },
  });

  const {
    data: favoriteGroups,
    isLoading: loadingFav,
    refetch: refetchFavorite,
  } = useQuery<ApiResponse>({
    queryKey: ['my-groups', 'favorite'],
    refetchOnMount: true,
    gcTime: 0,
    queryFn: async ({pageParam = 1}) => {
      const response = await getMyGroups('favorite', pageParam as number);
      return response || {data: []};
    },
  });

  const {
    data: popularGroups,
    isLoading: loadingPopular,
    refetch: refetchPopular,
  } = useQuery<ApiResponse>({
    queryKey: ['popular-groups', ''],
    refetchOnMount: true,
    gcTime: 0,
    queryFn: async ({pageParam = 1}) => {
      const response = await getPublicGroups(
        '',
        pageParam as number,
        '',
        user?.location_lat,
        user?.location_long,
      );
      return response || {data: []};
    },
  });

  useFocusEffect(
    useCallback(() => {
      // Refetch all group lists when screen comes into focus
      refetch();
      refetchFavorite();
      refetchPopular();
    }, [refetch, refetchFavorite, refetchPopular]),
  );

  useEffect(() => {
    const app = getApp();
    const messaging = getMessaging(app);

    const unsubscribe = messaging.onMessage(async remoteMessage => {
      updateBadgeCount(remoteMessage?.data);
    });

    return unsubscribe;
  }, []);

  const updateBadgeCount = useCallback(
    (data: any) => {
      if (!data?.group_id) return;

      // Update favorite groups
      queryClient.setQueryData(['my-groups', 'favorite'], (oldData: any) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          data: oldData.data.map((group: GroupsData) =>
            group.id === data.group_id ? {...group, unread_count: data.unread_count || 0} : group,
          ),
        };
      });

      // Update all groups
      queryClient.setQueryData(['my-groups'], (oldData: any) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          data: oldData.data.map((group: GroupsData) =>
            group.id === data.group_id ? {...group, unread_count: data.unread_count || 0} : group,
          ),
        };
      });
    },
    [queryClient],
  );

  const cardItems = useMemo(
    () => [
      {
        id: 'create',
        name: t('groupsScreen.createGroup'),
        icon: 'create-group',
        onPress: () => navigation.navigate('NewGroup', {groupName: ''}),
      },
      {
        id: 'my',
        name: t('groupsScreen.myGroups'),
        icon: 'groupicon-1',
        onPress: () => navigation.navigate('MyGroups'),
      },
      {
        id: 'join',
        name: t('groupsScreen.joinGroup'),
        icon: 'join-group',
        onPress: () => navigation.navigate('JoinGroups'),
      },
      {
        id: 'scoreboard',
        name: t('groupsScreen.scoreBoard'),
        icon: 'scoreboard',
        onPress: () => {},
      },
    ],
    [navigation, t],
  );

  // Combined data for FlatList with section headers
  const groupData = [
    {type: 'section', title: 'Favorite Groups', id: 'section-fav'},
    {
      id: '1',
      group_name: 'Sunday Funday - Fort Greene',
      total_members: 7,
      highlighted: false,
      type: 'group',
    },
    {id: '2', group_name: 'Troopers - SOHO', total_members: 7, highlighted: false, type: 'group'},
    {
      id: '3',
      group_name: 'Diadem at Central Park',
      total_members: 34,
      highlighted: true,
      type: 'group',
    },
    {type: 'section', title: 'All Groups', id: 'section-all'},
    {
      id: '4',
      group_name: 'Sunday Clinic - Fort Greene',
      total_members: 7,
      highlighted: false,
      type: 'group',
    },
  ];

  // Split groupData into sections for rendering with separate backgrounds
  const favoriteGroupItems = groupData.filter(
    item => item.type === 'group' && ['1', '2', '3'].includes(item.id),
  );

  const renderItem = ({item}: any, showLocation: boolean = false) => {
    if (item.type === 'section') {
      return (
        <Typography variant="sectionTitle" style={styles.sectionHeader}>
          {item.title}
        </Typography>
      );
    }
    return <GroupCardWrapper item={item} showLocation={showLocation} />;
  };

  const renderCardItem = useCallback(
    (item: (typeof cardItems)[number]) => (
      <TouchableOpacity
        key={item.id}
        style={styles.cardItem}
        onPress={item.onPress}
        activeOpacity={0.8}>
        <Shadow
          distance={1}
          offset={[1, 0.5]}
          startColor="rgba(0,0,0,0.25)"
          containerStyle={styles.shadowWrap}>
          <View style={styles.cardIconContainer}>
            <Icon name={item.icon} size={30} color={theme.colors.white} />
          </View>
        </Shadow>
        <Typography variant="body" style={styles.cardLabel} align="center">
          {item.name}
        </Typography>
      </TouchableOpacity>
    ),
    [styles, theme.colors.white],
  );

  const renderSectionHeader = useCallback(
    (title: string, bool: boolean = true, type?: string) => (
      <View style={styles.sectionHeaderContainer}>
        <View style={styles.flex1}>
          <Typography variant="groupType" style={styles.sectionHeader1} numberOfLines={1}>
            {t(title)}
          </Typography>
        </View>
        {bool && (
          <TouchableOpacity
            onPress={() => {
              if (type === 'public') {
                navigation.navigate('MyGroups');
              } else {
                navigation.navigate('GroupList', {title: t(title), type: type});
              }
            }}
            activeOpacity={0.8}>
            <Typography variant="seeAll" style={styles.seeAll}>
              {t('common.seeAll')}
            </Typography>
          </TouchableOpacity>
        )}
      </View>
    ),
    [styles, t],
  );

  const allGroup = (data?.data && data?.data.slice(0, 3)) || [];
  const favoriteGroup = (favoriteGroups?.data && favoriteGroups?.data.slice(0, 3)) || [];
  const popularGroup = (popularGroups?.data && popularGroups?.data.slice(0, 3)) || [];
  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        {/* Restored Card Row */}
        <View style={styles.cardRow}>{cardItems.map(renderCardItem)}</View>
        {/* Favorite Groups Section */}
        {renderSectionHeader(
          'groupsScreen.favoriteGroups',
          favoriteGroups?.data && favoriteGroups?.data?.length > 3,
          'favorite',
        )}
        <View style={styles.groupListContainer}>
          {loadingFav ? (
            <CLoader />
          ) : (
            <FlatList
              data={favoriteGroup}
              renderItem={props => renderItem(props, false)}
              keyExtractor={item => item.id}
              scrollEnabled={false}
              ListEmptyComponent={<NoData />}
            />
          )}
        </View>

        {/* all groups */}
        {renderSectionHeader(
          'groupsScreen.allGroups',
          data?.data && data?.data?.length > 3,
          'public',
        )}
        <View style={styles.groupListContainer}>
          {isLoading ? (
            <CLoader />
          ) : (
            <FlatList
              data={allGroup}
              renderItem={props => renderItem(props, false)}
              keyExtractor={item => item.id}
              scrollEnabled={false}
              ListEmptyComponent={<NoData />}
            />
          )}
        </View>

        {/* popular groups */}
        {renderSectionHeader(
          'groupsScreen.popularGroupsNearby',
          popularGroups?.data && popularGroups?.data?.length > 3,
          'popular',
        )}
        <View style={styles.groupListContainer}>
          {loadingPopular ? (
            <CLoader />
          ) : (
            <FlatList
              data={popularGroup || []}
              renderItem={props => renderItem(props, true)}
              keyExtractor={item => item.id}
              scrollEnabled={false}
              ListEmptyComponent={<NoData />}
            />
          )}
        </View>
        {/* sponsored groups */}
        {renderSectionHeader('groupsScreen.sponsoredGroups', false)}
        <View style={styles.groupListContainer}>
          <FlatList
            data={favoriteGroupItems}
            renderItem={props => renderItem(props, false)}
            keyExtractor={item => item.id}
            scrollEnabled={false}
            ListEmptyComponent={<NoData />}
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default Groups;
