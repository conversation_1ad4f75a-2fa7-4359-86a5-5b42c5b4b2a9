import React, {useCallback, useMemo, useState, useEffect} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, ScrollView} from 'react-native';
import {createDrawerNavigator} from '@react-navigation/drawer';
import TabNavigator from './TabNavigator';
import {useThemeStore} from '@/store/themeStore';
import {useAuthStore} from '@/store/authStore';
import {CButton, CImage, CustomModal, Icon, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import EditProfileScreen from '@/screens/drawer/EditProfileScreen';
import SearchInput from '@/components/SearchInput';
import OrderReserveEquipment from '@/components/Drawer/AssistantCoach/OrderReserveEquipment';
import InvitePlayers from '@/components/Drawer/AssistantCoach/InvitePlayers';
import CoachOptionsScreen from '@/components/Drawer/AssistantCoach/CoachOptions';
import GetCertified from '@/components/Drawer/AssistantCoach/GetCertified';
import MessageNotificationPreferences from '@/components/Drawer/AssistantCoach/MessageNotificationPreferences';
import WelcomeScreen from '@/components/Drawer/AssistantCoach/Welcome';
import PostAnAd from '@/components/Drawer/AssistantCoach/PostAnAd';
import ManageContent from '@/components/Drawer/AssistantCoach/ManageContent';
import ManageClasses from '@/components/Drawer/AssistantCoach/ManageClasses';
import ManageServiceRequest from '@/components/Drawer/AssistantCoach/ManageServiceRequest';
import CalendarScreen from '@/components/Drawer/AssistantCoach/Calendar';
import ManagePlayerAssetsScreen from '@/components/Drawer/AssistantCoach/ManagePlayerAssets';
import {useConfigStore} from '@/store';
import ProfileQrScreen from '@/screens/ProfileQrScreen';
import useTranslation from '@/hooks/useTranslation';
import {coachDrawerMenuList, drawerMenuList} from '@/config/staticData';
import DateScreen from '@/screens/tabs/SearchScreen/DateScreen';
import EquipmentReservationScreen from '@/screens/tabs/SearchScreen/EquipmentReservationScreen';
import RacquetSelector from '@/screens/tabs/RacquetsScreens/RacquetSelector';
import RacquetSelectorDetail from '@/screens/tabs/RacquetsScreens/RacquetSelectorDetail';
import {toaster} from '@/utils/commonFunctions';
import api from '@/services/api';
import {useConnection} from '@sendbird/uikit-react-native';
import EditCoachProfileScreen from '@/screens/EditCoachProfile';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import CertificateScreen from '@/screens/CertificateScreen';

// Community-related screens moved to CommunityStack

/**
 * @category Interfaces
 * @typedef {Object} DrawerItemType
 * @property {boolean} isVisible - Whether the drawer item should be displayed
 * @property {string} id - Unique identifier for the drawer item
 * @property {string} label - Display label for the drawer item (translation key)
 * @property {string} [screen] - Optional screen name to navigate to
 * @property {Function} [onPress] - Optional custom onPress handler
 */
interface DrawerItemType {
  isVisible: boolean;
  id: string;
  label: string;
  screen?: string;
  onPress?: () => void;
}

/**
 * @category Interfaces
 * @typedef {Object} DrawerParamList
 * @description Type definition for all possible drawer navigation screens and their parameters
 */
export type DrawerParamList = {
  welcome: undefined;
  OrderReserveEquipment: undefined;
  InvitePlayers: undefined;
  CoachOptions: undefined;
  getCertified: undefined;
  MessageNotificationPreferences: undefined;
  Settings: undefined;
  EditProfile: undefined;
  MainTabs: undefined;
  PostAnAd: undefined;
  ManageContent: undefined;
  ManageClasses: undefined;
  ManageServiceRequest: undefined;
  ManagePlayerAssets: undefined;
  ManageCommunity: undefined;
  Orders: undefined;
  Help: undefined;
  Rewards: undefined;
  RecycleBalls: undefined;
  ProfileQrScreen: undefined;
  DateScreen: undefined;
  EquipmentReservationScreen: undefined;
  RacquetCategory: {
    sportsTitle: string;
  };
  RacquetBrands: {
    sportsTitle: string;
    category: string;
  };
  RacquetSelector: undefined;
  RacquetSelectorDetail: undefined;
  PlayerConnectScreen: undefined;
  GoEats: undefined;
  GoTravel: undefined;
  GetCertified: undefined;
  // Community-related screens moved to CommunityStack
};

const Drawer = createDrawerNavigator();

/**
 * @category Function
 * @memberof DrawerNavigator
 * @description
 * Custom drawer content component that renders the drawer UI
 * Displays user profile, search functionality, and navigation menu items
 *
 * @param {Object} props - Component props
 * @param {Object} props.navigation - React Navigation object for screen navigation
 * @returns {JSX.Element} - The custom drawer content,
 */
const CustomDrawerContent = ({navigation}: any) => {
  const theme = useThemeStore();
  const {user} = useAuthStore();

  const {
    coachProfile,
    setCoachProfile,
    drawerMenuItems,
    setCoachAssistantEnabled,
    getCoachAssistantEnabled,
  } = useConfigStore();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const isCoachAssistantEnabled = getCoachAssistantEnabled();
  console.log('isCoachAssistantEnabled=====>>>>>', isCoachAssistantEnabled);
  const [filterData, setFilterData] = useState<DrawerItemType[]>(
    isCoachAssistantEnabled ? coachDrawerMenuList : drawerMenuItems || drawerMenuList,
  );
  const [searchQuery, setSearchQuery] = useState<string>('');

  const [loader, setLoader] = useState(false);

  const {t} = useTranslation();
  const {connect} = useConnection();

  // Define drawer menu items with their sub-items
  const drawerItems = useMemo<DrawerItemType[]>(
    () => (isCoachAssistantEnabled ? coachDrawerMenuList : drawerMenuItems || drawerMenuList),
    [drawerMenuItems, isCoachAssistantEnabled],
  );

  // Update filterData when drawerItems changes
  useEffect(() => {
    setFilterData(drawerItems);
    connect(user?.id);
  }, [drawerItems]);

  /**
   * @category Function
   * @memberof DrawerNavigator
   * @description
   * Renders a drawer menu item with its label and navigation handler
   * @param {DrawerItemType} item - The drawer item configuration
   * @returns {JSX.Element} - The rendered menu item or null if not visible
   */

  // Render a drawer menu item with its label
  const renderMenuItem = (item: DrawerItemType) => {
    if (!item.isVisible) {
      return null;
    }

    /**
     * @category Function
     * @memberof DrawerNavigator
     * @description
     *  Handles the press event for a drawer menu item
     * Closes the drawer and navigates to the appropriate screen
     */
    const handleItemPress = () => {
      // Close the drawer first
      navigation.closeDrawer();

      // Navigate to the corresponding screen based on the item ID
      if (item.id === 'notifications') {
        navigation.navigate('Notifications' as never);
      } else if (item.id === 'refer_friend') {
        navigation.navigate('ReferFriend' as never);
      } else if (item.id === 'my_matches') {
        navigation.navigate('MyMatches' as never);
      } else if (item.id === 'manage_community') {
        navigation.navigate('ManageCommunity' as never);
      } else if (item.id === 'orders') {
        navigation.navigate('Orders' as never);
      } else if (item.id === 'help') {
        navigation.navigate('Help' as never);
      } else if (item.id === 'rewards') {
        navigation.navigate('Rewards' as never);
      } else if (item.id === 'recycleBalls') {
        navigation.navigate('RecycleBalls' as never);
      } else if (item.id === 'edit_coach_profile') {
        navigation.navigate('EditCoachProfile' as never);
      } else if (item.id === 'get_certified') {
        navigation.navigate('GetCertified');
      } else if (item.screen) {
        navigation.navigate(item.screen as never);
      } else if (item.onPress) {
        item.onPress();
      }
    };

    return (
      <View key={item.id} style={styles(theme).menuItemContainer}>
        <TouchableOpacity style={styles(theme).menuItem} onPress={handleItemPress}>
          <Text style={styles(theme).menuItemText}>{t(item.label)}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderModalContent = () => {
    return (
      <WelcomeScreen
        onPress={() => {
          setCoachProfile(true);
          setIsModalVisible(false);
          setCoachAssistantEnabled(true);
        }}
      />
    );
  };

  const handleSearch = useCallback(
    (query: string) => {
      const filtered = query
        ? drawerItems.filter(item => t(item.label).toLowerCase().includes(query.toLowerCase()))
        : drawerItems;
      setFilterData(filtered);
    },
    [drawerItems],
  );
  const clearSearch = () => setSearchQuery('');

  const availableToPlay = async () => {
    setLoader(true);
    try {
      const response = await api.post('/user/available-to-play');
      if (response.data.status) {
        toaster('success', 'You are now available to play');
      } else {
        toaster('error', 'Failed to make yourself available to play');
      }
    } catch (error) {
      console.log('error=====>>>>>', error);
      toaster('error', 'Failed to make yourself available to play');
    } finally {
      setLoader(false);
    }
  };
  const insets = useSafeAreaInsets();

  return (
    <SafeAreaView
      style={[
        styles(theme).container,
        {
          paddingTop: insets.top,
          paddingBottom: insets.bottom,
          paddingLeft: insets.left,
          paddingRight: insets.right,
        },
      ]}>
      <View style={styles(theme).headerContainer}>
        <View style={styles(theme).userInfoContainer}>
          <CImage
            source={
              isCoachAssistantEnabled
                ? user?.coach_profile_pic ||
                  'https://static.vecteezy.com/system/resources/previews/036/280/651/original/default-avatar-profile-icon-social-media-user-image-gray-avatar-icon-blank-profile-silhouette-illustration-vector.jpg'
                : user?.profile_pic ||
                  'https://static.vecteezy.com/system/resources/previews/036/280/651/original/default-avatar-profile-icon-social-media-user-image-gray-avatar-icon-blank-profile-silhouette-illustration-vector.jpg'
            }
            resizeMode="cover"
            style={styles(theme).avatar}
          />
          <TouchableOpacity
            style={styles(theme).closeButton}
            onPress={() => navigation.closeDrawer()}>
            <Icon name="close1-1" size={36} color={theme.colors.crimsonRed} />
          </TouchableOpacity>
        </View>
        <View style={styles(theme).userTextContainer}>
          <Typography color={theme.colors.activeColor} variant="userName">
            {user?.name || user?.display_name}
          </Typography>
          <TouchableOpacity
            activeOpacity={0.8}
            style={styles(theme).emailContainer}
            onPress={() => navigation.navigate('EditProfile')}>
            <Typography variant="bodyMedium" color={theme.colors.activeColor}>
              {user?.email || '<EMAIL>'}
            </Typography>
            <Icon name="editpen" size={20} color={theme.colors.orange} />
          </TouchableOpacity>
          <CButton
            variant="active"
            title={t('drawer.availableToPlay')}
            onPress={() => {
              // availableToPlay();
              toaster('info', 'coming soon', 'top');
            }}
            loading={loader}
            containerStyle={styles(theme).availBtn}
          />
          <TouchableOpacity
            style={styles(theme).coach}
            onPress={() => {
              if (!isCoachAssistantEnabled && coachProfile) {
                setCoachAssistantEnabled(true);
              } else {
                setCoachAssistantEnabled(false);
              }
              if (!coachProfile && !isCoachAssistantEnabled) {
                setIsModalVisible(true);
              }
            }}>
            <Typography variant="tryNow" color={theme.colors.activeColor}>
              {isCoachAssistantEnabled
                ? t('drawer.exitAssistantCoach')
                : t('drawer.assistantCoach')}
            </Typography>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles(theme).searchContainer}>
        <SearchInput
          variant="light"
          placeholder={t('common.search')}
          value={searchQuery}
          onChangeText={setSearchQuery}
          onClear={clearSearch}
          onSearch={handleSearch}
          debounceTime={300}
        />
      </View>

      <ScrollView contentContainerStyle={styles(theme).scrollContainer}>
        {filterData?.map(renderMenuItem)}
      </ScrollView>
      <CustomModal
        animationType="slide"
        variant="bottom"
        visible={isModalVisible}
        onClose={() => {
          setIsModalVisible(false);
        }}
        modalContainerStyle={{height: '100%'}}
        showCloseButtonRight={true}
        imageBg={true}>
        {renderModalContent()}
      </CustomModal>
    </SafeAreaView>
  );
};

/**
 * @component
 * @category Navigation
 * @description
 * Sets up the drawer navigation with custom content and all app screens
 * @returns {JSX.Element} - The configured drawer navigator
 */
const DrawerNavigator = () => {
  const theme = useThemeStore();

  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: theme.colors.black,
          width: '100%',
        },
        drawerType: 'front',
        drawerStatusBarAnimation: 'fade',
        overlayColor: theme.colors.semiTransparentBlack,
        swipeEnabled: true, // Re-enable drawer swipe since we have custom gesture handler
        swipeEdgeWidth: 20, // Small swipe area to minimize conflicts
      }}
      drawerContent={props => <CustomDrawerContent {...props} />}>
      <Drawer.Screen name="MainTabs" component={TabNavigator} />
      <Drawer.Screen name="EditProfile" component={EditProfileScreen} />
      <Drawer.Screen name="welcome" component={WelcomeScreen} />
      <Drawer.Screen name="OrderReserveEquipment" component={OrderReserveEquipment} />
      <Drawer.Screen name="InvitePlayers" component={InvitePlayers} />
      <Drawer.Screen name="CoachOptions" component={CoachOptionsScreen} />
      <Drawer.Screen name="getCertified" component={GetCertified} />
      <Drawer.Screen
        name="MessageNotificationPreferences"
        component={MessageNotificationPreferences}
      />
      <Drawer.Screen name="PostAnAd" component={PostAnAd} />
      <Drawer.Screen name="ManageContent" component={ManageContent} />
      {/* <Drawer.Screen name="EditCoachProfile" component={EditCoachProfile} /> */}
      <Drawer.Screen name="ManageClasses" component={ManageClasses} />
      <Drawer.Screen name="ManageServiceRequest" component={ManageServiceRequest} />
      <Drawer.Screen name="Calendar" component={CalendarScreen} />
      <Drawer.Screen name="ManagePlayerAssets" component={ManagePlayerAssetsScreen} />
      <Drawer.Screen name="ProfileQrScreen" component={ProfileQrScreen} />
      <Drawer.Screen name="DateScreen" component={DateScreen} />
      <Drawer.Screen name="EquipmentReservationScreen" component={EquipmentReservationScreen} />
      <Drawer.Screen name="RacquetSelector" component={RacquetSelector} />
      <Drawer.Screen name="RacquetSelectorDetail" component={RacquetSelectorDetail} />
      <Drawer.Screen name="EditCoachProfile" component={EditCoachProfileScreen} />
      <Drawer.Screen name="GetCertified" component={CertificateScreen} />
    </Drawer.Navigator>
  );
};

// Styles
const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    headerContainer: {
      paddingHorizontal: 16,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Gunmetal,
      backgroundColor: theme.colors.background,
      // marginTop: 45,
    },
    userInfoContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },

    avatar: {
      width: 50,
      height: 50,
      borderRadius: 50,
    },
    userTextContainer: {
      marginTop: 15,
    },

    emailContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
    coach: {
      marginTop: 40,
    },
    userEmail: {
      fontSize: 14,
      color: theme.colors.lime,
      marginRight: 8,
    },
    closeButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.background,
    },
    searchContainer: {
      paddingHorizontal: 16,
      paddingBottom: 12,
    },
    searchBar: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 10,
      backgroundColor: theme.colors.primary,
    },
    searchText: {
      color: theme.colors.white,
      marginLeft: 8,
      fontSize: 16,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingBottom: 30,
    },
    menuItemContainer: {
      marginBottom: 8,
    },
    menuItem: {
      paddingVertical: 12,
      paddingHorizontal: 16,
    },
    menuItemText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.lime,
    },
    availBtn: {
      marginTop: 10,
    },
  });

export default DrawerNavigator;
