import {useCallback, useRef, useState} from 'react';
import {deleteFcmTokenFromDB} from '@/services/notificationsApi';
import {DeleteAccountModalHandles} from '@/components/DeleteAccountModal';
import {clearAllData} from '@/utils/commonFunctions';

/**
 * @category Interfaces
 * @typedef {Object} UseLogoutReturn
 * @property {Function} performLogout - Performs the logout process.
 * @property {Function} handleLogout - Opens the logout modal and sets modal type to 'logout'.
 * @property {React.RefObject<DeleteAccountModalHandles>} deleteAccountModalRef - Ref to control the DeleteAccountModal.
 * @property {'logout' | 'delete' | null} modalType - The type of the currently open modal ('logout', 'delete', or null).
 * @property {React.Dispatch<React.SetStateAction<'logout' | 'delete' | null>>} setModalType - Sets the modal type.
 * @property {Function} closeModal - Closes the modal and resets modal type.
 */
interface UseLogoutReturn {
  performLogout: () => Promise<void>;
  handleLogout: () => void;
  deleteAccountModalRef: React.RefObject<DeleteAccountModalHandles>;
  modalType: 'logout' | 'delete' | null;
  setModalType: React.Dispatch<React.SetStateAction<'logout' | 'delete' | null>>;
  closeModal: () => void;
}

/**
 * @function useLogout
 * @category Utils
 * @description Custom hook to handle user logout and account deletion modal logic.
 *
 * Provides methods to trigger logout, manage modal state, and clear all user-related data.
 *
 * @returns {UseLogoutReturn} Hook API for logout and modal management.
 */

export const useLogout = () => {
  // Ref to control the DeleteAccountModal
  const deleteAccountModalRef = useRef<DeleteAccountModalHandles>(null);
  // State to track which modal is open ('logout' or 'delete')
  const [modalType, setModalType] = useState<'logout' | 'delete' | null>(null);

  /**
   * Performs the logout process:
   * - Deletes FCM token from DB if logging out
   * - Clears all user data
   */
  const performLogout = useCallback(async () => {
    try {
      if (modalType === 'logout') {
        await deleteFcmTokenFromDB();
      }
    } catch (error) {
      // Log any errors during logout
      console.log('🚀 ~ performLogout ~ error:', error);
    } finally {
      clearAllData();
    }
  }, [modalType]);

  /**
   * Opens the logout modal and sets modal type to 'logout'.
   */
  const handleLogout = () => {
    setModalType('logout');
    deleteAccountModalRef.current?.open();
  };

  /**
   * Closes the modal and resets modal type.
   */
  const closeModal = () => {
    deleteAccountModalRef.current?.close();
    setModalType(null);
  };

  return {
    performLogout,
    handleLogout,
    deleteAccountModalRef,
    modalType,
    setModalType,
    closeModal,
  } as UseLogoutReturn;
};
