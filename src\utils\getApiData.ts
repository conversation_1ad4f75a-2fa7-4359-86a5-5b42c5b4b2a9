import axios, {AxiosRequestConfig, AxiosResponse} from 'axios';

// Define the structure of API responses for type safety
interface ApiResponse<T = any> {
  data: T;
  status: number;
  isSuccess: boolean;
  message: string;
}

/**
 * @function apiCall
 * @description
 * Generic API call function for making HTTP requests
 * @param url - The API endpoint URL
 * @param method - HTTP method (GET, POST, PUT, DELETE, etc.)
 * @param data - Request payload data (for POST, PUT, etc.)
 * @param headers - Custom headers to include with the request
 * @returns Promise with the API response data
 */
export const apiCall = async <T = any>(
  url: string,
  method: string = 'GET',
  data?: any,
  headers?: Record<string, string>,
): Promise<ApiResponse<T>> => {
  try {
    // Create request configuration
    const config: AxiosRequestConfig = {
      url,
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers, // Merge custom headers
      },
    };

    // Handle data differently based on HTTP method
    if (data && method.toUpperCase() !== 'GET') {
      // For POST, PUT, etc. add data to request body
      config.data = data;
    } else if (data && method.toUpperCase() === 'GET') {
      // For GET requests, add data as query parameters
      config.params = data;
    }

    // Make the API request
    const response: AxiosResponse<T> = await axios(config);

    // Return standardized successful response
    return {
      data: response.data,
      status: response.status,
      isSuccess: response.status >= 200 && response.status < 300,
      message: 'Success',
    };
  } catch (error: any) {
    // Log the error for debugging
    console.error('API Call Error:', error);

    // Return standardized error response
    return {
      data: {} as T,
      status: error.response?.status || 500,
      isSuccess: false,
      message: error.response?.data?.message || error.message || 'Something went wrong',
    };
  }
};

// =============== USAGE EXAMPLES ===============

/**
 * Example 1: Making a GET request
 *
 * This example demonstrates how to fetch user data with query parameters
 */
// const fetchUserExample = async (userId: string) => {
//   // Define the expected response type using an interface
//   interface User {
//     id: string;
//     name: string;
//     email: string;
//   }

//   try {
//     // GET request with query parameters
//     const response = await apiCall<User>('https://api.example.com/users', 'GET', {id: userId});

//     if (response.isSuccess) {
//       // Process successful response
//       return response.data;
//     } else {
//       // Handle error
//       console.error('Failed to fetch user:', response.message);
//       return null;
//     }
//   } catch (error) {
//     console.error('Unexpected error:', error);
//     return null;
//   }
// };

/**
 * Example 2: Making a POST request with authentication
 *
 * This example demonstrates how to create a resource with a request body
 * and custom authentication headers
 */
// const createResourceExample = async (resourceData: any, token: string) => {
//   try {
//     // POST request with data and authorization header
//     const response = await apiCall('https://api.example.com/resources', 'POST', resourceData, {
//       Authorization: `Bearer ${token}`,
//     });

//     if (response.isSuccess) {
//       // Handle successful creation
//       console.log('Resource created:', response.data);
//       return response.data;
//     } else {
//       // Handle error
//       console.error('Failed to create resource:', response.message);
//       return null;
//     }
//   } catch (error) {
//     console.error('Unexpected error:', error);
//     return null;
//   }
// };
