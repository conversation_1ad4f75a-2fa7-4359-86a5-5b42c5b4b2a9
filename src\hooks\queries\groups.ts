/**
 * create group
 *
 *  */
import api, {handleApiError, tokenStorage} from '@/services/api';
import {toaster} from '@/utils/commonFunctions';
import {useMutation, useQueryClient} from '@tanstack/react-query';

export interface CreateGroupRequest {
  group_name: string;
  group_type: string;
  group_image?: string | null;
  location: string;
  description?: string;
  users?: string[];
  tags?: string[];
}

export const createGroup = async (data: CreateGroupRequest): Promise<any> => {
  console.log('Creating group with data:', data);
  const accessToken = tokenStorage.getString('accessToken');

  if (!accessToken) {
    throw new Error('No access token available. Please login again.');
  }

  try {
    const response = await api.post('/group-chat/create-group', data, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
    });
    console.log('Create group response:', response);
    // Check if the response is successful
    if (response.status !== 200) {
      console.log('API returned non-200 status:', response.status);
      throw new Error(
        `API error: ${response.status} - ${response.data?.message || 'Failed to create group'}`,
      );
    }

    // Check if the response data indicates failure
    if (response.data.status === false) {
      console.log('API returned status=false in data');
      throw new Error(response.data.message || 'Failed to create group');
    }
    console.log('Create group response:', response);
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

export const useCreateUserGroup = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: CreateGroupRequest) => {
      return createGroup(data);
    },
    onSuccess: data => {
      toaster('success', data.message, 'top');
      queryClient.invalidateQueries({queryKey: ['groups']});
    },
    onError: error => {
      toaster('error', error.message, 'top');
    },
  });
};

export const useJoinOrLeaveGroup = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({groupId, refetch}: {groupId: string; refetch?: boolean}) => {
      console.log('groupId=====>>>>>', groupId);
      console.log('refetch=====>>>>>1', refetch);
      const response = await api.get(`/group-chat/join-and-leave-group?group_id=${groupId}`);
      return response.data;
    },
    onSuccess: (data, variables) => {
      const {groupId, refetch = true} = variables;
      console.log('refetch=====>>>>>', refetch);

      if (!data.status) {
        toaster('error', data?.message, 'top');
        return;
      }

      toaster('success', data.message, 'top');

      if (refetch) {
        queryClient.invalidateQueries({queryKey: ['group-details']});
        queryClient.invalidateQueries({queryKey: ['public-groups']});
      }
    },
    onError: (error: Error) => {
      toaster('error', error.message, 'top');
    },
  });
};

export const useRequestToJoinGroup = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({groupId, refetch}: {groupId: string; refetch?: boolean}) => {
      const params = {
        group_id: groupId,
      };
      const response = await api.post(`/group-chat/request-join-group`, params);
      console.log('params=====>>>>>', params);
      return response.data;
    },
    onSuccess: (data, variables) => {
      const {groupId, refetch = true} = variables;
      if (!data.status) {
        toaster('error', data?.message, 'top');
        return;
      }
      console.log('data=====>>>>>', data);
      toaster('success', data.message, 'top');

      if (refetch) {
        queryClient.invalidateQueries({queryKey: ['group-details']});
        queryClient.invalidateQueries({queryKey: ['public-groups']});
      }
    },
    onError: (error: Error) => {
      toaster('error', error.message, 'top');
    },
  });
};

export const useManageFavoriteGroup = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (groupId: string) => {
      const response = await api.get(`/group-chat/manage-favorite-group?group_id=${groupId}`);
      if (!response.status) {
        toaster('error', response.data?.message, 'top');
      }
      return response.data;
    },
    onSuccess: data => {
      toaster('success', data.message, 'top');
      queryClient.invalidateQueries({queryKey: ['my-groups']});
      queryClient.invalidateQueries({queryKey: ['popular-groups']});
    },
    onError: (error: Error) => {
      toaster('error', error.message, 'top');
    },
  });
};

export const getRequestsList = async (pageNo: number = 1, search?: any, group_id?: any) => {
  try {
    const params = {
      ...(search ? {search} : {}),
      ...(group_id ? {group_id} : {}),
      page: pageNo,
    };
    const response = await api.get(`/group-chat/join-group-request-lists`, {
      params: params,
    });
    if (response.status) {
      return response?.data;
    }
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};

export const useAcceptTheRequest = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      groupId,
      refetch,
      userId,
      action,
      reason,
    }: {
      groupId: string;
      refetch?: boolean;
      userId: string;
      action: 'accepted' | 'rejected';
      reason?: string;
    }) => {
      console.log('user_id=====>>>>>', userId);

      const params = {
        group_id: groupId,
        ...(userId ? {requested_user_id: userId} : {}),
        ...(action ? {action} : {}),
        ...(reason ? {reason} : {}),
      };
      const response = await api.post(`/group-chat/accept-reject-group-request`, params);
      console.log('params=====>>>>>', params);
      console.log('response=====>>>>>123', response);
      return response.data;
    },
    onSuccess: (data, variables) => {
      const {groupId, refetch = true} = variables;
      if (!data.status) {
        toaster('error', data?.message, 'top');
        return;
      }
      console.log('data=====>>>>>', data);
      toaster('success', data.message, 'top');

      if (refetch) {
        queryClient.invalidateQueries({queryKey: ['group-details']});
        // queryClient.invalidateQueries({queryKey: ['public-groups']});
      }
    },
    onError: (error: Error) => {
      toaster('error', error.message, 'top');
    },
  });
};
