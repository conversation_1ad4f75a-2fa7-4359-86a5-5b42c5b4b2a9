import CoachWrapper from '@/components/CoachWrapper';
import GetCertified from '@/components/Drawer/AssistantCoach/GetCertified';
import {Images} from '@/config';
import {useAuthStore} from '@/store';
import {DrawerNavigationProp} from '@react-navigation/drawer';
import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {ImageBackground} from 'react-native';

type RootDrawerParamList = {
  welcome: undefined;
  EditCoachProfile: undefined;
  CoachOptions: undefined;
  getCertified: {from: string};
};

type NavigationProp = DrawerNavigationProp<RootDrawerParamList>;

const CertificateScreen = () => {
  const navigation = useNavigation<NavigationProp>();
  const {user} = useAuthStore();

  return (
    <ImageBackground
      source={Images.gradientBg}
      style={{
        flex: 1,
        width: '100%',
        height: '100%',
      }}>
      <CoachWrapper>
        <GetCertified
          onClose={() => {
            navigation.goBack();
          }}
          from="drawer"
          selectedCertifications={user?.coachData?.certifications}
        />
      </CoachWrapper>
    </ImageBackground>
  );
};

export default CertificateScreen;
