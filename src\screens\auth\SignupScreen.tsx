import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, ActivityIndicator} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation/index';
import {useThemeStore} from '@/store/themeStore';
import {useAuthStore} from '@/store/authStore';
import {getGlobalStyles} from '@utils/styleUtils';
import {signInWithGoogle, signInWithFacebook} from '@utils/auth';
import {CImage, SafeAreaView} from '@/components';
import {Images} from '@/config';
import Typography from '@/components/Typography';
import {useTranslationContext} from '@/context/TranslationContext';
import {useSocialLogin} from '@/hooks/queries/useAuth';
import {IOS, toaster} from '@/utils/commonFunctions';
import {logEvent} from '@/utils/GA';

type SignupScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Signup'>;

/**
 * @component
 * @category Screens
 *
 * @description A comprehensive signup screen that provides multiple authentication options including
 * Google, Facebook, and email signup. Features loading states, error handling, and
 * analytics tracking for each authentication method.
 *
 * @see {@link Terms-conditions} - Used to display terms and conditions link and navigate user to Terms and conditions screen.
 * @see {@link Sign-in} - Used to display sign-in link and navigate user to sign-in screen.
 *
 * @return {JSX.Element} The rendered signup screen component
 */
const SignupScreen = () => {
  const navigation = useNavigation<SignupScreenNavigationProp>();

  const theme = useThemeStore();
  const {t} = useTranslationContext();
  const {loginStart, loginFailure} = useAuthStore();
  const globalStyles = getGlobalStyles({theme});

  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [isFacebookLoading, setIsFacebookLoading] = useState(false);

  // Use React Query for login
  const loginMutation = useSocialLogin();

  /**
   * @function handleGoogleSignup
   * @memberof SignupScreen
   * @description
   * Initiates Google sign-in, processes the authentication result,
   * and handles success/error states with proper analytics tracking.
   * @returns {Promise<void>} Promise that resolves when signup process completes
   */
  const handleGoogleSignup = async () => {
    logEvent('signup_started', {
      method: 'google',
    });
    try {
      setIsGoogleLoading(true);
      loginStart();
      const result = await signInWithGoogle();

      console.log('result ===>', result);
      if (result.success && result.data) {
        loginMutation.mutate(
          {
            token: result.data.idToken,
            provider: 'google',
          },
          {
            onSuccess: e => {
              // Navigate to main tabs on successful login
              // toaster('success', e.message, 'top');
              logEvent('signup_completed', {
                method: 'google',
                user_id: e?.data?.user?.id,
              });
              navigation.navigate('MainTabs');
            },
            onError: error => {
              logEvent('signup_failed', {
                method: 'google',
              });
              // Error is already handled by the mutation
              console.log('error ===>', error);
              toaster('error', error.message, 'top');
              loginFailure(error?.message || 'Google sign in failed');
            },
          },
        );
      } else {
        logEvent('signup_failed', {
          method: 'google',
        });
        loginFailure(result.error || 'Google sign in failed');
      }
      setIsGoogleLoading(false);
    } catch (error) {
      logEvent('signup_failed', {
        method: 'google',
      });
      setIsGoogleLoading(false);
      const errorMessage = error instanceof Error ? error.message : 'Google signup error';
      loginFailure(errorMessage);
      console.error('Google signup error:', error);
    }
  };

  /**
   * @function handleFacebookSignup
   * @memberof SignupScreen
   * @description
   * Initiates Facebook sign-in, processes the authentication result with
   * platform-specific token handling (iOS vs Android), and manages
   * success/error states with analytics tracking.
   * @returns {Promise<void>} Promise that resolves when signup process completes
   */
  const handleFacebookSignup = async () => {
    logEvent('signup_started', {
      method: 'facebook',
    });
    try {
      setIsFacebookLoading(true);
      loginStart();
      const result = await signInWithFacebook();

      console.log('signInWithFacebook ===>', result);
      if (result.success && result.data) {
        // login(result.user);
        // navigation.navigate('MainTabs');
        const accessTokenValue = IOS
          ? result?.data?.authenticationToken
          : result?.data?.accessToken || '';
        if (accessTokenValue) {
          loginMutation.mutate(
            {
              token: accessTokenValue,
              provider: 'facebook',
            },
            {
              onSuccess: e => {
                // Navigate to main tabs on successful login
                // toaster('success', e.message, 'top');
                logEvent('signup_completed', {
                  method: 'facebook',
                });
                navigation.navigate('MainTabs');
              },
              onError: error => {
                // Error is already handled by the mutation
                console.log('error ===>', error);
                logEvent('signup_failed', {
                  method: 'facebook',
                });
                toaster('error', error.message, 'top');
                loginFailure(error.message || 'Facebook sign in failed');
              },
            },
          );
        }
      } else {
        logEvent('signup_failed', {
          method: 'facebook',
        });
        loginFailure(result.error || 'Facebook sign in failed');
      }
      setIsFacebookLoading(false);
    } catch (error) {
      logEvent('signup_failed', {
        method: 'facebook',
      });
      setIsFacebookLoading(false);
      const errorMessage = error instanceof Error ? error.message : 'Facebook signup error';
      loginFailure(errorMessage);
      console.error('Facebook signup error:', error);
    }
  };

  /**
   * @function handleEmailSignup
   * @memberof SignupScreen
   * @description Redirect user to email signup screen
   */
  const handleEmailSignup = () => {
    logEvent('signup_started', {
      method: 'goraqt',
    });
    navigation.navigate('EmailSignup');
  };

  const handleTermsPress = () => {
    navigation.navigate('TermsAndConditions');
  };

  const renderButtonContent = (source: string, text: string, isLoading: boolean) => {
    return isLoading ? (
      <ActivityIndicator size="small" color={theme.colors.secondary} />
    ) : (
      <>
        <CImage source={source} style={{width: 30, height: 30}} resizeMode="contain" />
        <Text style={[styles(theme).socialButtonText, {color: theme.colors.black}]}>{text}</Text>
      </>
    );
  };

  return (
    <SafeAreaView includeTop style={[styles(theme).container]}>
      <View style={styles(theme).content}>
        <Typography variant="title" style={[styles(theme).title]}>
          {t('signupScreen.signUpWith')}
        </Typography>

        <View style={styles(theme).buttonsContainer}>
          <TouchableOpacity
            style={[
              styles(theme).socialButton,
              isFacebookLoading && styles(theme).loadingButton,
              {backgroundColor: theme.colors.white},
            ]}
            onPress={handleFacebookSignup}
            disabled={isFacebookLoading}
            activeOpacity={0.7}>
            {renderButtonContent(Images.facebook, t('signupScreen.facebook'), isFacebookLoading)}
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles(theme).socialButton,
              isGoogleLoading && styles(theme).loadingButton,
              {backgroundColor: theme.colors.white},
            ]}
            onPress={handleGoogleSignup}
            disabled={isGoogleLoading}
            activeOpacity={0.7}>
            {renderButtonContent(Images.google, t('signupScreen.google'), isGoogleLoading)}
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles(theme).socialButton, {backgroundColor: theme.colors.white}]}
            onPress={handleEmailSignup}
            activeOpacity={0.7}>
            {renderButtonContent(Images.mail, t('signupScreen.email'), false)}
          </TouchableOpacity>
        </View>

        <View style={styles(theme).footer}>
          <Text style={[globalStyles.text, styles(theme).termsText, {color: theme.colors.text}]}>
            {t('signupScreen.bySigningUpYouAgreeToOur')}{' '}
            <Text
              style={[styles(theme).termsLink, {color: theme.colors.text}]}
              onPress={handleTermsPress}>
              {t('signupScreen.termsAndConditions')}
            </Text>
          </Text>
        </View>
      </View>

      <View style={styles(theme).loginContainer}>
        <Text style={[globalStyles.text, styles(theme).loginText, {color: theme.colors.text}]}>
          {t('signupScreen.alreadyHaveAnAccount')}{' '}
          <Text
            style={[styles(theme).loginLink, {color: theme.colors.text}]}
            onPress={() => navigation.navigate('Login')}>
            {t('signupScreen.signin')}
          </Text>
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    title: {
      color: theme.colors.text,
      marginBottom: 20,
    },
    buttonsContainer: {
      marginBottom: 20,
      width: '100%',
    },
    socialButton: {
      flexDirection: 'row',
      padding: 14,
      borderRadius: 8,
      alignItems: 'center',
      marginBottom: 16,
      width: '100%',
      elevation: 1,
      height: 52,
    },
    loadingButton: {
      opacity: 0.7,
    },
    socialButtonText: {
      fontWeight: '400',
      fontSize: 16,
      marginLeft: 10,
    },
    footer: {
      width: '100%',
      paddingHorizontal: 20,
      marginTop: 'auto',
    },
    termsText: {
      textAlign: 'center',
      fontSize: 12,
      lineHeight: 18,
    },
    termsLink: {
      fontWeight: 'bold',
      textDecorationLine: 'underline',
    },
    loginContainer: {
      paddingBottom: 24,
      alignItems: 'center',
    },
    loginText: {
      fontSize: 14,
    },
    loginLink: {
      fontWeight: 'bold',
      textDecorationLine: 'underline',
    },
  });

export default SignupScreen;
