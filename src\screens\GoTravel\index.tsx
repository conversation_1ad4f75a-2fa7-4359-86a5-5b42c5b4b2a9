import React, {use<PERSON><PERSON>back, useMemo, useState} from 'react';
import {
  FlatList,
  View,
  TouchableOpacity,
  ImageBackground,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import {styles as createStyles} from './styles';
import {useThemeStore} from '@/store/themeStore';
import {Header, Icon, NoData, SafeAreaView} from '@/components';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import Tabs from '@/components/Tabs';
import CommunityCard from '@/components/CommunityCard';
import Typography from '@/components/Typography';
import {Images} from '@/config';
import MediaCard from '@/components/MediaCard';
import {getCommunityData, getItemList, setupCommunityMutation} from '@/utils/commonFunctions';
import {useInfiniteQuery, useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import VideoActionsModal from '@/components/VideoActionsModal';
import CLoader from '@/components/CLoader';

type NavigationProp = StackNavigationProp<CommunityStackParamList>;

interface GoTravelData {
  id: string;
  title: string;
  description: string;
  image: string;
  file_thumbnail: string;
  tags: GoTravelTag[];
  buttonText?: string;
  is_liked: boolean;
  likes_count: number;
  [key: string]: any;
}

interface GoTravelTag {
  id: number;
  name: string;
  title: string;
}

interface ApiResponse {
  status: boolean;
  message: string;
  data: GoTravelData[];
  hasMore: boolean;
  nextPage: number;
}

interface VideoModalState {
  visible: boolean;
  goTravelData: Partial<GoTravelData>;
}

interface TabsApiResponse {
  status: boolean;
  message: string;
  data: GoTravelTag[];
}

const GoTravel = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);

  const navigation = useNavigation<NavigationProp>();

  const goBack = () => {
    navigation.goBack();
  };

  const queryClient = useQueryClient();

  const [activeTab, setActiveTab] = useState('');
  const [isVideoActionsModalVisible, setIsVideoActionsModalVisible] = useState<VideoModalState>({
    visible: false,
    goTravelData: {},
  });
  const {data: tabs} = useQuery<any>({
    queryKey: ['go-travel'],
    gcTime: 0,

    queryFn: async () => {
      const response = await getItemList('go-travel');
      if (response?.data?.length) {
        setActiveTab(response.data[0].name);
      }
      return (response as TabsApiResponse) || {data: []};
    },
  });
  const handleTabPress = (tab: string) => {
    setActiveTab(tab);
  };

  const {data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, refetch, isRefetching} =
    useInfiniteQuery({
      queryKey: ['go-travel', activeTab],
      gcTime: 0,
      queryFn: async ({pageParam = 1}) => {
        const activeTabId = tabs?.data?.find((item: GoTravelTag) => item.name === activeTab)?.id;
        const response = await getCommunityData({
          pageParam,
          type: 'go-travel',
          item_type: activeTabId,
        });
        return response as ApiResponse;
      },
      initialPageParam: 1,
      getNextPageParam: lastPage => {
        return lastPage?.hasMore ? lastPage.nextPage : undefined;
      },
    });

  // Extract the data array from the API response
  const goTravelData = useMemo(() => {
    return data?.pages.flatMap(page => page.data || []) ?? [];
  }, [data]);

  const updateVideoModalState = (id: string, isLiked: boolean) => {
    // Update modal data if it's the same item
    if (isVideoActionsModalVisible.goTravelData?.id === id) {
      setIsVideoActionsModalVisible(prev => ({
        ...prev,
        goTravelData: {
          ...prev.goTravelData,
          is_liked: isLiked,
          likes_count: isLiked
            ? ((prev.goTravelData.likes_count as number) || 0) + 1
            : ((prev.goTravelData.likes_count as number) || 0) - 1,
        },
      }));
    }
  };

  const {mutate: updateLike} = useMutation(
    setupCommunityMutation<GoTravelData>(queryClient, ['go-travel'], updateVideoModalState),
  );

  const handleLikePress = (item: GoTravelData) => {
    updateLike({
      id: item.id,
      is_liked: !item.is_liked,
    });
  };

  const renderComponent = ({item}: {item: GoTravelData}) => {
    return (
      <View>
        <CommunityCard
          data={item}
          onCardPress={() => {
            navigation.navigate('CommunityDetails', {
              from: 'GoTravel',
              title: item.title,
              data: item,
            });
          }}
          onLikePress={() => {}}
          onCommentPress={() => {}}
          onSharePress={() => {}}
          containerStyle={styles.cardContainer}
          variant="tag"
          onMoreIconPress={() => {
            setIsVideoActionsModalVisible({
              visible: true,
              goTravelData: item,
            });
          }}
          onMorePress={() => {
            navigation.navigate('CommunityDetails', {
              from: 'GoTravel',
              title: item.title,
              data: item,
            });
          }}
        />
        {item.buttonText && (
          <TouchableOpacity style={styles.blueButton}>
            <Typography variant="communityBtnText" color={theme.colors.white}>
              {item.buttonText}
            </Typography>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <MediaCard source={Images.goTravelImage} imageContainerStyle={styles.imageContainer} />

      <View style={styles.tabContainer}>
        <Tabs
          variant="square"
          tabs={tabs?.data ? tabs?.data.map((item: GoTravelTag) => item.name) : []}
          activeTab={activeTab}
          onTabPress={handleTabPress}
          tabStyle={styles.tabStyle}
          tabTitleStyle={styles.tabTitleStyle}
          type="inner"
          listContainerStyle={styles.listContainerStyle}
        />
      </View>
    </View>
  );

  const ListFooterComponent = useMemo(() => {
    return hasNextPage || isFetchingNextPage ? (
      <View style={{paddingVertical: 20, alignItems: 'center'}}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    ) : null;
  }, [hasNextPage, isFetchingNextPage, theme.colors.primary]);

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

  return (
    <ImageBackground source={Images.gradientBg} style={styles.backgroundImage}>
      <SafeAreaView includeBottom={false} style={styles.root}>
        <Header
          showBack={false}
          leftComponent={
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Icon name="Left-chevron" size={24} color={theme.colors.white} />
            </TouchableOpacity>
          }
          title="GoTravels"
          backgroundColor="transparent"
          isBackPress={true}
          backNavigation={goBack}
          rightIcons={[
            {name: 'notification', size: 24, badge: 0},
            {name: 'chat', size: 24, badge: 14},
          ]}
        />
        {isLoading ? (
          <CLoader />
        ) : (
          <FlatList
            data={goTravelData}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item, index) => index.toString()}
            renderItem={renderComponent}
            contentContainerStyle={styles.content}
            onEndReachedThreshold={0.5}
            ListHeaderComponent={renderHeader}
            onEndReached={handleEndReached}
            refreshControl={<RefreshControl refreshing={isLoading} onRefresh={() => refetch()} />}
            ListEmptyComponent={
              <NoData
                title={`No ${activeTab} Data`}
                message={`No ${activeTab && activeTab.toLowerCase()} content available at the moment.`}
              />
            }
            ListFooterComponent={ListFooterComponent}
          />
        )}
      </SafeAreaView>
      <VideoActionsModal
        visible={isVideoActionsModalVisible.visible}
        onClose={() =>
          setIsVideoActionsModalVisible({
            visible: false,
            goTravelData: {},
          })
        }
        data={isVideoActionsModalVisible.goTravelData}
        videoTitle="Latest Reviews"
        isLiked={isVideoActionsModalVisible.goTravelData?.is_liked ?? false}
        videoThumbnail="https://picsum.photos/200/300"
        onLike={() => {
          if (isVideoActionsModalVisible.goTravelData?.id) {
            handleLikePress(isVideoActionsModalVisible.goTravelData as GoTravelData);
          }
        }}
        onComment={() => {
          navigation.navigate('CommentScreen', {
            from: 'Latest Reviews',
            title: 'Latest Reviews',
            data: goTravelData?.[0],
          });
          setIsVideoActionsModalVisible({
            visible: false,
            goTravelData: {},
          });
        }}
        onShare={() => {}}
      />
    </ImageBackground>
  );
};

export default GoTravel;
