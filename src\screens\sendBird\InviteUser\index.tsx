import React from 'react';
import {createGroupChannelInviteFragment, useSendbirdChat} from '@sendbird/uikit-react-native';
import {useGroupChannel} from '@sendbird/uikit-chat-hooks';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation';

type NavigationProp = StackNavigationProp<RootStackParamList, 'SendBird'>;

const InviteUser = ({route}: {route: any}) => {
  const GroupChannelInviteFragment = createGroupChannelInviteFragment();

  const {channelUrl} = route.params;
  const {sdk} = useSendbirdChat();
  const {channel} = useGroupChannel(sdk, channelUrl);
  const navigation = useNavigation<NavigationProp>();
  if (!channel) return null;

  return (
    <GroupChannelInviteFragment
      channel={channel}
      onPressHeaderLeft={() => navigation.goBack()}
      onInviteMembers={() => navigation.goBack()}
    />
  );
};

export default InviteUser;
