import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  FlatList,
  TouchableOpacity,
  Text,
  StyleSheet,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
  TextInput,
  Dimensions,
  ScrollView,
} from 'react-native';
import {useThemeStore} from '@/store';
import {CustomModal, Icon} from '@/components';
import CLoader from '@/components/CLoader';
import {getCurrentLocation, getAddressFromCoordinates} from '@/services/geolocationService';
import {Icons} from '@/config/icons';

const GOOGLE_PLACES_API_KEY = 'AIzaSyDcot1Dv43LW27_v-TJrBnELqpSHZE_1Ts'; // TODO: move to env
const {height} = Dimensions.get('window');

interface Prediction {
  place_id: string;
  description: string;
  structured_formatting?: {
    main_text: string;
    secondary_text: string;
  };
}

interface LocationSearchProps {
  handleLocationSelect: (location: string, coordinates?: {lat: number; lng: number}) => void;
  selectedLocation: string;
  label: string;
  placeholder: string;
  locationBtn?: boolean;
  inputStyle?: any;
}

const LocationSearch = (props: LocationSearchProps) => {
  const {
    handleLocationSelect,
    selectedLocation,
    label,
    placeholder,
    locationBtn = true,
    inputStyle,
  } = props;
  const theme = useThemeStore();
  const [modalSearchText, setModalSearchText] = useState('');
  const [predictions, setPredictions] = useState<Prediction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  // Auto scroll to the end when location changes
  useEffect(() => {
    if (selectedLocation && scrollViewRef.current) {
      // Small delay to ensure the text is rendered before scrolling
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({animated: true});
      }, 100);
    }
  }, [selectedLocation]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (modalSearchText.length > 1) {
        fetchPlacePredictions(modalSearchText);
      } else {
        setPredictions([]);
      }
    }, 400);
    return () => clearTimeout(timeoutId);
  }, [modalSearchText]);

  const fetchPlacePredictions = async (input: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(
          input,
        )}&key=${GOOGLE_PLACES_API_KEY}&types=geocode`,
      );
      const data = await response.json();

      if (data.status === 'OK') {
        setPredictions(data.predictions);
      } else {
        console.error('Places API error:', data.status);
        setPredictions([]);
      }
    } catch (error) {
      console.error('Error fetching predictions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchPlaceDetails = async (placeId: string) => {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&fields=name,geometry,formatted_address,place_id&key=${GOOGLE_PLACES_API_KEY}`,
      );

      const data = await response.json();

      if (data.status === 'OK') {
        const place = data.result;
        const locationName = place.formatted_address;
        const coordinates = place.geometry?.location;
        handleLocationSelect(locationName, coordinates);
        setShowModal(false);
        setPredictions([]);
        setModalSearchText('');
        Keyboard.dismiss();

        console.log('Selected Location:', locationName, 'Coordinates:', coordinates);
      } else {
        console.error('Place details error:', data.status);
      }
    } catch (error) {
      console.error('Error fetching place details:', error);
    }
  };

  const handlePlaceSelect = (prediction: Prediction) => {
    fetchPlaceDetails(prediction.place_id);
  };

  const handleGetCurrentLocation = async () => {
    console.log('handleGetCurrentLocation');
    try {
      setIsLoading(true);
      const position = await getCurrentLocation();
      const {latitude, longitude} = position;

      // Get address from coordinates
      const address = await getAddressFromCoordinates(latitude, longitude);
      // Use the formatted address
      handleLocationSelect(address.formattedAddress, {
        lat: latitude,
        lng: longitude,
      });
    } catch (error) {
      console.error('Error getting current location:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles(theme).container}>
      <View style={styles(theme).locationContainer}>
        <View style={styles(theme).locationInputContainer}>
          {/* Label */}
          {label && <Text style={styles(theme).label}>{label}</Text>}

          {/* Scrollable location display */}
          <View style={styles(theme).rowContainer}>
            {!selectedLocation ? (
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => {
                  if (!selectedLocation) {
                    setShowModal(true);
                  }
                }}
                style={[styles(theme).inputContainer, inputStyle]}>
                <ScrollView
                  ref={scrollViewRef}
                  horizontal={true}
                  showsHorizontalScrollIndicator={false}
                  style={styles(theme).scrollView}
                  contentContainerStyle={styles(theme).scrollViewContent}
                  keyboardShouldPersistTaps="handled">
                  <Text
                    style={[
                      styles(theme).locationText,
                      !selectedLocation && styles(theme).placeholderText,
                    ]}>
                    {selectedLocation || placeholder}
                  </Text>
                </ScrollView>

                {/* Search button - opens the modal */}
                <TouchableOpacity
                  style={styles(theme).searchButton}
                  onPress={() => setShowModal(true)}>
                  <Icon name="search-1" size={20} color={theme.colors.white} />
                </TouchableOpacity>
              </TouchableOpacity>
            ) : (
              <View style={styles(theme).inputContainer}>
                <ScrollView
                  ref={scrollViewRef}
                  horizontal={true}
                  showsHorizontalScrollIndicator={false}
                  style={styles(theme).scrollView}
                  contentContainerStyle={styles(theme).scrollViewContent}
                  keyboardShouldPersistTaps="handled">
                  <Text
                    style={[
                      styles(theme).locationText,
                      !selectedLocation && styles(theme).placeholderText,
                    ]}>
                    {selectedLocation || placeholder}
                  </Text>
                </ScrollView>

                {/* Search button - opens the modal */}
                <TouchableOpacity
                  style={styles(theme).searchButton}
                  onPress={() => setShowModal(true)}>
                  <Icon name="search-1" size={20} color={theme.colors.white} />
                </TouchableOpacity>
              </View>
            )}
            {locationBtn && (
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={handleGetCurrentLocation}
                style={styles(theme).locationPinContainer}>
                {/* <Icon name="location-pin" size={30} color={theme.colors.activeColor} /> */}
                <Icons.MaterialIcons
                  name="my-location"
                  size={30}
                  color={theme.colors.activeColor}
                />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>

      <CustomModal
        variant="bottom"
        visible={showModal}
        animationType="slide"
        onClose={() => setShowModal(false)}
        title="Search Location"
        showCloseButtonRight>
        <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
          <View
            style={[
              styles(theme).modalContent,
              {
                height: Platform.OS === 'android' ? 400 : height * 0.85,
              },
            ]}>
            <View style={styles(theme).searchInputContainer}>
              <Icon name="search-1" size={24} color={theme.colors.white} />
              <TextInput
                style={styles(theme).searchInput}
                placeholder="Search location..."
                placeholderTextColor="#999"
                value={modalSearchText}
                onChangeText={text => {
                  setModalSearchText(text);
                }}
                autoFocus
              />
            </View>

            {isLoading ? (
              <CLoader />
            ) : (
              <FlatList
                data={predictions}
                keyExtractor={item => item.place_id}
                style={styles(theme).predictionsList}
                showsVerticalScrollIndicator={false}
                renderItem={({item}) => (
                  <TouchableOpacity
                    style={styles(theme).predictionItem}
                    onPress={() => handlePlaceSelect(item)}>
                    <View style={styles(theme).predictionTextContainer}>
                      <Text style={styles(theme).predictionText}>
                        {item.structured_formatting?.main_text || item.description}
                      </Text>
                      <Text style={styles(theme).predictionSecondaryText}>
                        {item.structured_formatting?.secondary_text || ''}
                      </Text>
                    </View>
                  </TouchableOpacity>
                )}
                ListEmptyComponent={() => (
                  <View style={styles(theme).emptyResultsContainer}>
                    {modalSearchText.length > 0 ? (
                      <Text style={styles(theme).emptyResultsText}>No results found</Text>
                    ) : (
                      <Text style={styles(theme).emptyResultsText}>Start typing to search</Text>
                    )}
                  </View>
                )}
              />
            )}
          </View>
        </KeyboardAvoidingView>
      </CustomModal>
    </View>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    textInput: {
      backgroundColor: theme.colors.TranslucentWhite,
      color: theme.colors.white,
      borderRadius: 16,
      height: 56,
      fontSize: 16,
      borderColor: theme.colors.TranslucentWhite,
    },
    inputContainer: {
      backgroundColor: theme.colors.TranslucentWhite,
      borderRadius: 16,
      height: 56,
      borderColor: theme.colors.TranslucentWhite,
      justifyContent: 'center',
      flexDirection: 'row',
      alignItems: 'center',
      paddingLeft: 16,
      paddingRight: 8,
      gap: 10,
      flex: 1,
    },
    locationText: {
      fontSize: 16,
      color: theme.colors.white,
    },
    placeholderText: {
      color: theme.colors.placeholder,
    },
    scrollView: {
      flex: 1,
    },
    scrollViewContent: {
      paddingVertical: 8,
      flexGrow: 1,
    },
    modalContent: {
      backgroundColor: theme.colors.background,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.white,
    },
    closeButton: {
      padding: 8,
    },
    searchInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.TranslucentWhite,
      borderRadius: 12,
      paddingHorizontal: 12,
      marginBottom: 16,
      gap: 10,
    },
    searchInputIcon: {
      marginRight: 8,
    },
    searchInput: {
      flex: 1,
      height: 50,
      color: theme.colors.white,
      fontSize: 16,
    },
    clearButton: {
      padding: 8,
    },
    predictionsList: {
      flex: 1,
    },
    predictionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.gray,
    },
    predictionIcon: {
      marginRight: 12,
    },
    predictionTextContainer: {
      flex: 1,
    },
    predictionText: {
      fontSize: 16,
      color: theme.colors.white,
    },
    predictionSecondaryText: {
      fontSize: 14,
      color: theme.colors.gray,
      marginTop: 4,
    },
    emptyResultsContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 20,
    },
    emptyResultsText: {
      fontSize: 16,
      color: theme.colors.gray,
    },
    label: {
      color: theme.colors.white,
      fontSize: 18,
      fontWeight: '400',
      marginBottom: 6,
    },
    locationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
    locationInputContainer: {
      flex: 1,
    },
    locationPinContainer: {
      paddingVertical: 16,
      alignItems: 'center',
      justifyContent: 'center',
    },
    searchButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
    },
    rowContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
  });

export default LocationSearch;
