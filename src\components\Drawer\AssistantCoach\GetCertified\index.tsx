import React, {useState} from 'react';
import {View, FlatList, TouchableOpacity} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {CButton, Icon} from '@/components';
import createStyles from './styles';
import Typography from '@/components/Typography';
import PillLabel from '@/components/PillLabel';
import useTranslation from '@/hooks/useTranslation';
import {useQuery} from '@tanstack/react-query';
import {getCertificateList} from '@/services/coachApi';
import CLoader from '@/components/CLoader';
import {toaster} from '@/utils/commonFunctions';
import {SubmitHandler} from 'react-hook-form';
import {useUpdateCoachProfile} from '@/hooks/queries/useCoach';
import {useUserDetails} from '@/hooks/queries/useUser';
import {useAuthStore} from '@/store';

interface OptionItem {
  id: string;
  name: string;
}

interface SelectedOption {
  id: string;
  name: string;
}

interface certificateData {
  id: string;
  name: string;
}

interface ApiResponse {
  status: boolean;
  data: certificateData[];
}

interface CertificateItem {
  name: string;
  id: string;
}

interface FormData {
  playerName: string;
  phoneNumber: string;
  email: string;
  description: string;
  isPrivate: boolean;
  sportsClub: string;
  rate: number;
  certifications: CertificateItem[];
  publicCourses: string[];
  videoCoaching: boolean;
  otherServices: string[];
  skillSets: string[];
  playerTypes: string[];
  profileImage: string;
}

interface CertificateProps {
  onClose?: () => void;
  getCertifications?: () => void;
  selectedCertifications: SelectedOption[];
  from?: string;
}

const GetCertified = (props: CertificateProps) => {
  const {
    onClose = () => {},
    getCertifications = () => {},
    selectedCertifications = [],
    from = '',
  } = props;
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const [selectedOptions, setSelectedOptions] = useState<SelectedOption[]>(selectedCertifications);

  const {t} = useTranslation();

  const updateCoachProfile = useUpdateCoachProfile();
  const userDetails = useUserDetails();
  const {user} = useAuthStore();

  // Update selectedOptions when selectedCertifications prop changes
  React.useEffect(() => {
    setSelectedOptions(selectedCertifications);
  }, [selectedCertifications]);

  const toggleOption = (option: OptionItem) => {
    setSelectedOptions(prev => {
      const newSelectedOptions = prev.some(item => item.id === option.id)
        ? prev.filter(item => item.id !== option.id)
        : [...prev, {id: option.id, name: option.name}];
      getCertifications(newSelectedOptions);
      return newSelectedOptions;
    });
  };

  const renderItem = ({item}: {item: OptionItem}) => {
    const isSelected = selectedOptions.some(selected => selected.id === item.id);
    return (
      <PillLabel
        textStyle={{fontWeight: isSelected ? '700' : '300'}}
        label={item.name}
        backgroundColor={isSelected ? theme.colors.activeColor : theme.colors.dimGray}
        textColor={isSelected ? theme.colors.black : theme.colors.text}
        onPress={() => toggleOption(item)}
        triangle={false}
        containerStyle={{
          height: 104,
          paddingRight: 10,
        }}
      />
    );
  };

  const {data, isLoading} = useQuery<ApiResponse>({
    queryKey: ['certificate-list'],
    gcTime: 0,
    queryFn: async () => {
      const response = await getCertificateList();
      return response;
    },
  });

  const onSubmit: SubmitHandler<FormData> = () => {
    const labelArray = selectedOptions?.map(item => item.id);
    const updateData = {
      description: user?.coachData?.profile_description || '',
      certifications: labelArray || [],
      isPrivate: user?.coachData?.is_private || false,
      publicCourses: user?.coachData?.public_courts?.split(',') || [],
      sportsClub: user?.coachData?.sports_clubs || '',
      videoCoaching: user?.coachData?.video_coaching || false,
      rate: user?.coachData?.rate || undefined,
      otherServices: user?.coachData?.other_services?.split(',') || [],
      skillSets: user?.coachData?.skills?.split(',') || [],
      playerTypes: user?.coachData?.player_types?.split(',') || [],
      profileImage: user?.coach_profile_pic || '',
    };

    // navigation.navigate('CoachOptions');

    updateCoachProfile.mutate(updateData, {
      onSuccess: data => {
        userDetails.mutate();
        toaster('success', data.message, 'top');
      },
      onError: error => {
        toaster('error', error.message || 'Failed to update profile', 'top');
      },
    });
  };

  return (
    <View style={styles.contentInnerView}>
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => onClose(selectedOptions)}>
          <Icon name="Left-chevron" size={24} color={theme.colors.white} />
        </TouchableOpacity>
        <Typography variant="invitePlayersTitle" color={theme.colors.white}>
          {t('getCertifiedScreen.title')}
        </Typography>
      </View>
      {isLoading ? (
        <CLoader />
      ) : (
        <>
          <FlatList
            data={data?.data}
            renderItem={renderItem}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.pillContainer}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
          />

          <CButton
            title={'Add'}
            onPress={() => {
              if (from === 'drawer') {
                onSubmit();
              } else {
                onClose(selectedOptions);
              }
            }}
            containerStyle={styles.submitButton}
            textStyle={styles.submitButtonText}
            loading={updateCoachProfile.isPending}
          />
        </>
      )}
    </View>
  );
};

export default GetCertified;
