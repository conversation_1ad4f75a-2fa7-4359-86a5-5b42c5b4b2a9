import {getFCMToken, storeFCMToken} from '@/components/common/PushNotification';
import api, {handleApiError, tokenStorage} from './api';
import {toaster} from '@/utils/commonFunctions';
import messaging from '@react-native-firebase/messaging';

// API paths
const API_PATHS = {
  saveFcmToken: 'user/save-fcm-token',
  deleteFcmToken: 'user/delete-fcm-token',
  getNotifications: 'notifications',
  markAsRead: 'notifications/read',
  markAllAsRead: 'notifications/read-all',
  deleteNotification: 'notifications',
  unreadCount: 'notifications/unread-count',
};

/**
 * save FCM token
 * @returns Promise<void>
 */
export const saveFcmTokenInDB = async (): Promise<void> => {
  const permission = await messaging().hasPermission();
  if (permission === messaging.AuthorizationStatus.DENIED) {
    console.warn('Notification permission denied');
    return;
  }
  try {
    const accessToken = tokenStorage.getString('accessToken');
    console.log('🚀 ~ saveFcmTokenInDB ~ accessToken:', accessToken);
    if (!accessToken) throw new Error('No access token. Login required.');

    const fcmToken = await getFCMToken();
    const body = {fcm_token: fcmToken || ''};

    const response = await api.post(API_PATHS.saveFcmToken, body, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (response.data.status === false || response.status !== 200) {
      throw new Error(response.data.message || 'Failed to save FCM token');
    }
    storeFCMToken(fcmToken || '');
    console.log('FCM token saved successfully', response);
  } catch (error) {
    console.error('Error saving FCM token:', handleApiError(error));
    throw new Error(handleApiError(error));
  }
};

/**
 * Delete FCM token
 * @returns Promise<void>
 */
export const deleteFcmTokenFromDB = async (): Promise<void> => {
  const permission = await messaging().hasPermission();
  if (permission === messaging.AuthorizationStatus.DENIED) {
    console.warn('Notification permission denied');
    return;
  }
  try {
    const accessToken = tokenStorage.getString('accessToken');
    if (!accessToken) throw new Error('No access token. Login required.');
    const fcmToken = await getFCMToken();
    const body = {fcm_token: fcmToken || ''};

    const response = await api.post(API_PATHS.deleteFcmToken, body, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (response.data.status === false || response.status !== 200) {
      throw new Error(response.data.message || 'Failed to delete FCM token');
    }
    storeFCMToken('');
    console.log('FCM token deleted successfully', response);
  } catch (error) {
    console.error('Error deleting FCM token:', handleApiError(error));
    throw new Error(handleApiError(error));
  }
};

/**
 * Get notifications list
 * @param pageNo Page number for pagination
 * @param readStatus Filter by read status ('read' | 'unread' | 'all')
 * @returns Promise<any>
 */
export const getNotificationsList = async (
  pageNo: number = 1,
  readStatus: 'read' | 'unread' | 'all' = 'unread',
): Promise<any> => {
  try {
    const accessToken = tokenStorage.getString('accessToken');
    if (!accessToken) throw new Error('No access token. Login required.');

    const response = await api.get(API_PATHS.getNotifications, {
      params: {
        PageNo: pageNo,
        read_status: readStatus,
      },
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });
    console.log('🚀 ~ response:', response);

    if (response.status !== 200 || response.data.status === false) {
      throw new Error(response.data.message || 'Failed to fetch notifications');
    }

    return response.data;
  } catch (error) {
    console.error('Error fetching notifications:', handleApiError(error));
    throw new Error(handleApiError(error));
  }
};

/**
 * Mark notification as read
 * @param notificationId ID of the notification to mark as read
 * @returns Promise<any>
 */
export const markNotificationAsRead = async (notificationId: string): Promise<any> => {
  try {
    const accessToken = tokenStorage.getString('accessToken');
    if (!accessToken) throw new Error('No access token. Login required.');

    const response = await api.post(
      API_PATHS.markAsRead,
      {notification_id: notificationId, is_read: 1},
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
    if (response.status !== 200 || response.data.status === false) {
      throw new Error(response.data.message || 'Failed to mark notification as read');
    }

    return response.data;
  } catch (error) {
    console.error('Error marking notification as read:', handleApiError(error));
    throw new Error(handleApiError(error));
  }
};

/**
 * Delete a notification
 * @param notificationId ID of the notification to delete
 * @returns Promise<any>
 */
export const deleteNotification = async (notificationId: string): Promise<any> => {
  try {
    const accessToken = tokenStorage.getString('accessToken');
    if (!accessToken) throw new Error('No access token. Login required.');

    const response = await api.delete(`${API_PATHS.deleteNotification}/${notificationId}`, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (response.status !== 200 || response.data.status === false) {
      toaster('error', response.data.message, 'top');
      throw new Error(response.data.message || 'Failed to delete notification');
    }

    if (response.data.status === true && response.status === 200) {
      toaster('success', response.data.message, 'top');
    }

    return response.data;
  } catch (error) {
    console.error('Error deleting notification:', handleApiError(error));
    throw new Error(handleApiError(error));
  }
};

/**
 * Mark all notifications as read
 * @returns Promise<any>
 */
export const markAllNotificationsAsRead = async (): Promise<any> => {
  try {
    const accessToken = tokenStorage.getString('accessToken');
    if (!accessToken) throw new Error('No access token. Login required.');

    const response = await api.post(
      API_PATHS.markAllAsRead,
      {},
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );

    if (response.status !== 200 || response.data.status === false) {
      throw new Error(response.data.message || 'Failed to mark all notifications as read');
    }

    return response.data;
  } catch (error) {
    console.error('Error marking all notifications as read:', handleApiError(error));
    throw new Error(handleApiError(error));
  }
};

/**
 * Get unread notifications count
 * @returns Promise<number>
 */
export const getUnreadNotificationsCount = async (): Promise<number> => {
  try {
    const accessToken = tokenStorage.getString('accessToken');
    if (!accessToken) throw new Error('No access token. Login required.');

    const response = await api.get('notifications/unread-count', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });
    console.log('response=====>>>>>', response);
    if (response.status !== 200 || response.data.status === false) {
      throw new Error(response.data.message || 'Failed to fetch unread count');
    }
    return response.data.count;
  } catch (error) {
    console.error('Error fetching unread count:', handleApiError(error));
    throw new Error(handleApiError(error));
  }
};
