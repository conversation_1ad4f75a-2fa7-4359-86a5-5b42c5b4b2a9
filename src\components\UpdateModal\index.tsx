import React from 'react';
import CustomModal from '../Modal';
import {View} from 'react-native';
import {createStyles} from './styles';
import {useThemeStore} from '@/store';
import Typography from '../Typography';
import useTranslation from '@/hooks/useTranslation';
import CButton from '../CButton';
import CImage from '../CImage';
import {Images} from '@/config';

interface UpdateModalProps {
  isVisibleModal: boolean;
  updateButtonPress: () => void;
}

const UpdateModal = (props: UpdateModalProps) => {
  const {isVisibleModal = false, updateButtonPress = () => {}} = props;

  const theme = useThemeStore();

  const {t} = useTranslation();

  const styles = createStyles(theme);

  return (
    <CustomModal
      visible={isVisibleModal}
      //   onClose={() => setIsModalVisible(false)}
      variant="bottom"
      showCloseButton={false}
      title="">
      <View style={styles.root}>
        <CImage source={Images.logo} style={styles.image} resizeMode="contain" />
        <Typography color={theme.colors.text} variant="subTitle3" align="center">
          {t('updateModal.title')}{' '}
        </Typography>
        <Typography variant="subtitle" color={theme.colors.white} style={styles.des} align="center">
          {t('updateModal.description')}
        </Typography>
        <CButton
          title={t('updateModal.restartApp')}
          onPress={() => {
            updateButtonPress();
          }}
          variant="primary"
          containerStyle={styles.updateBtn}
        />
      </View>
    </CustomModal>
  );
};

export default UpdateModal;
