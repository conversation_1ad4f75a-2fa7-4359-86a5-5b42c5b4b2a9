

<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> utils/sentryUtils.ts</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="styles/style.css">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
             
                <a href="index.html">
                    <h1 class="navbar-item">GoRaqt Documentation</h1>
                </a>
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    Documentation
                </a>
                
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
                <div class="search-wrapper">
                    <input id="search" type="text" placeholder="Search docs..." class="input">
                </div>
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Global</h3><ul><li><a href="global.html#HIDE_TAB_SCREENS">HIDE_TAB_SCREENS</a></li><li><a href="global.html#SENSITIVE_FIELDS">SENSITIVE_FIELDS</a></li><li><a href="global.html#addBreadcrumb">addBreadcrumb</a></li><li><a href="global.html#apiCall">apiCall</a></li><li><a href="global.html#authenticateWithBiometrics">authenticateWithBiometrics</a></li><li><a href="global.html#calculateAge">calculateAge</a></li><li><a href="global.html#checkAuthStatusApi">checkAuthStatusApi</a></li><li><a href="global.html#checkLocationPermission">checkLocationPermission</a></li><li><a href="global.html#cleanMemoryIfNeeded">cleanMemoryIfNeeded</a></li><li><a href="global.html#clearAllData">clearAllData</a></li><li><a href="global.html#configureStatusBar">configureStatusBar</a></li><li><a href="global.html#createHealthService">createHealthService</a></li><li><a href="global.html#deleteFcmTokenFromDB">deleteFcmTokenFromDB</a></li><li><a href="global.html#deleteNotification">deleteNotification</a></li><li><a href="global.html#disableBiometrics">disableBiometrics</a></li><li><a href="global.html#enableBiometrics">enableBiometrics</a></li><li><a href="global.html#forgotPasswordApi">forgotPasswordApi</a></li><li><a href="global.html#formatBirthdate">formatBirthdate</a></li><li><a href="global.html#getAddressFromCoordinates">getAddressFromCoordinates</a></li><li><a href="global.html#getCurrentLocationWithAddress">getCurrentLocationWithAddress</a></li><li><a href="global.html#getCurrentPosition">getCurrentPosition</a></li><li><a href="global.html#getCurrentRegion">getCurrentRegion</a></li><li><a href="global.html#getCurrentUserApi">getCurrentUserApi</a></li><li><a href="global.html#getNotificationsList">getNotificationsList</a></li><li><a href="global.html#getTabBarVisibility">getTabBarVisibility</a></li><li><a href="global.html#getUnreadNotificationsCount">getUnreadNotificationsCount</a></li><li><a href="global.html#getUserDetails">getUserDetails</a></li><li><a href="global.html#getUserProfile">getUserProfile</a></li><li><a href="global.html#handleNotificationNavigation">handleNotificationNavigation</a></li><li><a href="global.html#handleTextureError">handleTextureError</a></li><li><a href="global.html#initSentry">initSentry</a></li><li><a href="global.html#isBiometricsAvailable">isBiometricsAvailable</a></li><li><a href="global.html#isBiometricsEnabled">isBiometricsEnabled</a></li><li><a href="global.html#lastCacheClearTime">lastCacheClearTime</a></li><li><a href="global.html#logError">logError</a></li><li><a href="global.html#logMessage">logMessage</a></li><li><a href="global.html#loginWithEmail">loginWithEmail</a></li><li><a href="global.html#loginWithEmailApi">loginWithEmailApi</a></li><li><a href="global.html#logoutApi">logoutApi</a></li><li><a href="global.html#markAllNotificationsAsRead">markAllNotificationsAsRead</a></li><li><a href="global.html#markNotificationAsRead">markNotificationAsRead</a></li><li><a href="global.html#navigateBackFromExample">navigateBackFromExample</a></li><li><a href="global.html#navigateToOptimizedFlatListExample">navigateToOptimizedFlatListExample</a></li><li><a href="global.html#otpResendApi">otpResendApi</a></li><li><a href="global.html#otpVerifyApi">otpVerifyApi</a></li><li><a href="global.html#prefetchInitialQueries">prefetchInitialQueries</a></li><li><a href="global.html#prefetchPost">prefetchPost</a></li><li><a href="global.html#prefetchUser">prefetchUser</a></li><li><a href="global.html#refreshTokenApi">refreshTokenApi</a></li><li><a href="global.html#registerWithEmail">registerWithEmail</a></li><li><a href="global.html#registerWithEmailApi">registerWithEmailApi</a></li><li><a href="global.html#requestLocationPermission">requestLocationPermission</a></li><li><a href="global.html#resetPasswordApi">resetPasswordApi</a></li><li><a href="global.html#saveFcmTokenInDB">saveFcmTokenInDB</a></li><li><a href="global.html#schema">schema</a></li><li><a href="global.html#scrubObject">scrubObject</a></li><li><a href="global.html#scrubSensitiveData">scrubSensitiveData</a></li><li><a href="global.html#setCustomStatusBar">setCustomStatusBar</a></li><li><a href="global.html#setDarkStatusBar">setDarkStatusBar</a></li><li><a href="global.html#setLightStatusBar">setLightStatusBar</a></li><li><a href="global.html#setNavigationContext">setNavigationContext</a></li><li><a href="global.html#setStatusBarHidden">setStatusBarHidden</a></li><li><a href="global.html#setUserContext">setUserContext</a></li><li><a href="global.html#setupMemoryCleaning">setupMemoryCleaning</a></li><li><a href="global.html#setupTextureErrorHandlers">setupTextureErrorHandlers</a></li><li><a href="global.html#signInWithFacebook">signInWithFacebook</a></li><li><a href="global.html#signInWithGoogle">signInWithGoogle</a></li><li><a href="global.html#startTransaction">startTransaction</a></li><li><a href="global.html#styles">styles</a></li><li><a href="global.html#updateUserProfile">updateUserProfile</a></li><li><a href="global.html#useAuthStatus">useAuthStatus</a></li><li><a href="global.html#useCurrentUser">useCurrentUser</a></li><li><a href="global.html#useLogin">useLogin</a></li><li><a href="global.html#useRegister">useRegister</a></li><li><a href="global.html#useSafeArea">useSafeArea</a></li><li><a href="global.html#useSocialLogin">useSocialLogin</a></li><li><a href="global.html#useUpdateUserProfile">useUpdateUserProfile</a></li><li><a href="global.html#useUserProfile">useUserProfile</a></li></ul></div><div class="category"><h2>Components</h2><h3>Components</h3><ul><li><a href="Advertisement.html">Advertisement</a></li><li><a href="BiometricGate.html">BiometricGate</a></li><li><a href="CButton.html">CButton</a></li><li><a href="CreateGoRaqtProfile.html">CreateGoRaqtProfile</a></li><li><a href="CustomMapView.html">CustomMapView</a></li><li><a href="CustomMarker.html">CustomMarker</a></li><li><a href="NotificationCard.html">NotificationCard</a></li><li><a href="OfferBanner.html">OfferBanner</a></li><li><a href="Subscription.html">Subscription</a></li></ul></div><div class="category"><h2>Interfaces</h2><h3>Interfaces</h3><ul><li><a href="AdvertisementProps.html">AdvertisementProps</a></li><li><a href="ApiResponse.html">ApiResponse</a></li><li><a href="BiometricGateProps.html">BiometricGateProps</a></li><li><a href="CButtonProps.html">CButtonProps</a></li><li><a href="CreateProfileProps.html">CreateProfileProps</a></li><li><a href="CustomMarkerProps.html">CustomMarkerProps</a></li><li><a href="DrawerItemType.html">DrawerItemType</a></li><li><a href="ForgotPasswordScreenProps.html">ForgotPasswordScreenProps</a></li><li><a href="Notification.html">Notification</a></li><li><a href="NotificationCardProps.html">NotificationCardProps</a></li><li><a href="PaginationState.html">PaginationState</a></li><li><a href="PermissionsScreenProps.html">PermissionsScreenProps</a></li><li><a href="RouteParams.html">RouteParams</a></li><li><a href="SentryNavigationContainerProps.html">SentryNavigationContainerProps</a></li><li><a href="SocialRegisterRequest.html">SocialRegisterRequest</a></li><li><a href="SplashScreenProps.html">SplashScreenProps</a></li><li><a href="TermsOfServiceScreenProps.html">TermsOfServiceScreenProps</a></li><li><a href="UseLogoutReturn.html">UseLogoutReturn</a></li></ul><h3><a href="global.html">Global</a></h3></div><div class="category"><h2>Navigation</h2><h3>Components</h3><ul><li><a href="DrawerNavigator.html">DrawerNavigator</a></li><li><a href="SentryNavigationContainer.html">SentryNavigationContainer</a></li></ul></div><div class="category"><h2>Screens</h2><h3>Components</h3><ul><li><a href="CommunityDetails.html">CommunityDetails</a></li><li><a href="DirectFetchTest.html">DirectFetchTest</a></li><li><a href="EmailSignupScreen.html">EmailSignupScreen</a></li><li><a href="ForgotPasswordScreen.html">ForgotPasswordScreen</a></li><li><a href="LoginScreen.html">LoginScreen</a></li><li><a href="MyMatchesScreen.html">MyMatchesScreen</a></li><li><a href="NotificationsListScreen.html">NotificationsListScreen</a></li><li><a href="PermissionsScreen.html">PermissionsScreen</a></li><li><a href="SignupScreen.html">SignupScreen</a></li><li><a href="SplashScreen.html">SplashScreen</a></li><li><a href="TermsOfServiceScreen.html">TermsOfServiceScreen</a></li><li><a href="Verification.html">Verification</a></li></ul></div><div class="category"><h2>Utils</h2><h3>Global</h3><ul><li><a href="global.html#api">api</a></li><li><a href="global.html#socialLoginApi">socialLoginApi</a></li><li><a href="global.html#useLogout">useLogout</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Source</p>
                    <h1>utils/sentryUtils.ts</h1>
                </header>
                



    
    <section>
        <article>
            <pre class="prettyprint source linenums"><code>import * as Sentry from '@sentry/react-native';
import {User} from '@/store/authStore';
import {Platform} from 'react-native';

// Define types for Sentry that might be missing
type SeverityLevel = 'fatal' | 'error' | 'warning' | 'log' | 'info' | 'debug';

// Define a type for the event
interface SentryEvent {
  [key: string]: unknown;
  request?: {
    data?: unknown;
    [key: string]: unknown;
  };
  extra?: Record&lt;string, unknown>;
  contexts?: Record&lt;string, unknown>;
}

// Use our own severity type since Sentry.Severity might not be available
type Severity = SeverityLevel;

/**
 * Sensitive fields that should be scrubbed before sending to Sentry
 */
export const SENSITIVE_FIELDS = [
  // Personal information
  'email',
  'phone',
  'phoneNumber',
  'password',
  'birthdate',
  'birthYear',
  'birthyear',
  'ssn',
  'socialSecurity',
  'address',
  'location',
  'latitude',
  'longitude',
  'gps',

  // Authentication
  'token',
  'accessToken',
  'refreshToken',
  'idToken',
  'jwt',
  'apiKey',
  'secret',

  // Payment information
  'creditCard',
  'cardNumber',
  'cvv',
  'cvc',
  'expiryDate',
  'cardholderName',
  'bankAccount',
  'routingNumber',
  'accountNumber',
  'paymentMethod',
];

/**
 * Initialize Sentry with proper configuration
 */
export const initSentry = () => {
  Sentry.init({
    dsn: 'https://<EMAIL>/****************',

    // Set environment based on __DEV__ flag
    environment: __DEV__ ? 'development' : 'production',

    // Enable debug mode in development
    debug: __DEV__,

    // Disable in development
    enabled: !__DEV__,

    // Configure data scrubbing
    beforeSend: event => {
      try {
        return scrubSensitiveData(event) as any;
      } catch (err) {
        console.warn('Error in beforeSend:', err);
        return event;
      }
    },

    // Set tracing sample rate (adjust as needed)
    tracesSampleRate: __DEV__ ? 0.2 : 1.0,

    // Enable auto session tracking
    enableAutoSessionTracking: true,

    // Set session timeout
    sessionTrackingIntervalMillis: 30000,

    // Add tags to all events
    initialScope: {
      tags: {
        platform: Platform.OS,
        platformVersion: String(Platform.Version),
      },
    },

    // Enable automatic breadcrumbs
    enableNativeCrashHandling: true,
    enableNativeNagger: true,

    // Add more context data to events (IP address, cookies, user, etc.)
    // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
    sendDefaultPii: true,
  });
};

/**
 * Set user context in Sentry
 * @param user User object from auth store
 */
export const setUserContext = (user: User | null) => {
  if (user) {
    // Set user context with minimal identifiable information
    Sentry.setUser({
      // Use a unique identifier that's available in the User object
      // If user.id doesn't exist, use another unique field or generate one
      id: user?.id || user.email || 'unknown-user', // Using name as a fallback
      username: user.name,
      // Explicitly NOT including email or other PII
    });
  } else {
    // Clear user context when logged out
    Sentry.setUser(null);
  }
};

/**
 * Set navigation context in Sentry
 * @param routeName Current route name
 */
export const setNavigationContext = (routeName: string) => {
  Sentry.addBreadcrumb({
    category: 'navigation',
    message: `Navigated to ${routeName}`,
    level: 'info',
  });

  // Set the current screen as a tag
  Sentry.setTag('screen', routeName);
};

/**
 * Scrub sensitive data from Sentry events
 * @param event Sentry event
 * @returns Scrubbed event
 */
export const scrubSensitiveData = (event: unknown): unknown => {
  try {
    // Deep clone the event to avoid modifying the original
    const scrubbedEvent = JSON.parse(JSON.stringify(event)) as SentryEvent;

    // Scrub request data if present
    if (scrubbedEvent.request?.data) {
      scrubbedEvent.request.data = scrubObject(scrubbedEvent.request.data);
    }

    // Scrub extra data if present
    if (scrubbedEvent.extra) {
      scrubbedEvent.extra = scrubObject(scrubbedEvent.extra) as Record&lt;string, unknown>;
    }

    // Scrub contexts if present
    if (scrubbedEvent.contexts) {
      scrubbedEvent.contexts = scrubObject(scrubbedEvent.contexts) as Record&lt;string, unknown>;
    }

    return scrubbedEvent;
  } catch (error) {
    // If scrubbing fails, return the original event
    console.error('Error scrubbing Sentry event:', error);
    return event;
  }
};

/**
 * Recursively scrub sensitive data from an object
 * @param obj Object to scrub
 * @returns Scrubbed object
 */
export const scrubObject = (obj: unknown): unknown => {
  if (!obj) return obj;

  // If it's an array, scrub each item
  if (Array.isArray(obj)) {
    return obj.map(item => scrubObject(item));
  }

  // If it's not an object, return as is
  if (typeof obj !== 'object') {
    return obj;
  }

  // Create a new object to avoid modifying the original
  const scrubbedObj = {...(obj as Record&lt;string, unknown>)};

  // Scrub each property
  for (const key in scrubbedObj) {
    // Check if the key is sensitive
    if (SENSITIVE_FIELDS.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
      scrubbedObj[key] = '[REDACTED]';
    }
    // Recursively scrub nested objects
    else if (typeof scrubbedObj[key] === 'object') {
      scrubbedObj[key] = scrubObject(scrubbedObj[key]);
    }
  }

  return scrubbedObj;
};

/**
 * Log an error to Sentry with additional context
 * @param error Error to log
 * @param context Additional context
 */
export const logError = (error: Error, context: Record&lt;string, unknown> = {}) => {
  Sentry.withScope(scope => {
    // Add context to the scope
    for (const [key, value] of Object.entries(context)) {
      scope.setExtra(key, value);
    }

    // Capture the error
    Sentry.captureException(error);
  });
};

/**
 * Log a message to Sentry
 * @param message Message to log
 * @param level Log level
 * @param context Additional context
 */
export const logMessage = (
  message: string,
  level: SeverityLevel = 'info',
  context: Record&lt;string, unknown> = {},
) => {
  Sentry.withScope(scope => {
    // Add context to the scope
    for (const [key, value] of Object.entries(context)) {
      scope.setExtra(key, value);
    }

    // Capture the message
    Sentry.captureMessage(message, level as any);
  });
};

/**
 * Create a performance transaction - DISABLED
 * This function is disabled because the Sentry version doesn't support transactions.
 * Instead, it adds a breadcrumb to track the operation.
 *
 * @param name Transaction name
 * @param operation Operation type
 * @returns undefined
 */
export const startTransaction = (name: string, operation: string) => {
  // Instead of starting a transaction, just add a breadcrumb
  Sentry.addBreadcrumb({
    category: 'performance',
    message: `Operation: ${name} (${operation})`,
    level: 'info' as any,
    data: {
      name,
      op: operation,
      timestamp: new Date().toISOString(),
    },
  });

  // Return a dummy object that has the expected methods but does nothing
  return {
    setData: () => {},
    setStatus: () => {},
    finish: () => {},
  };
};

/**
 * Add a breadcrumb to the current scope
 * @param message Breadcrumb message
 * @param category Breadcrumb category
 * @param level Breadcrumb level
 * @param data Additional data
 */
export const addBreadcrumb = (
  message: string,
  category: string,
  level: SeverityLevel = 'info',
  data: Record&lt;string, unknown> = {},
) => {
  Sentry.addBreadcrumb({
    message,
    category,
    level: level as any,
    data: scrubObject(data) as Record&lt;string, unknown>,
  });
};
</code></pre>
        </article>
    </section>




            </div>
            
            <!-- Footer removed via patch -->
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

<script src="scripts/search.js"> </script>


</body>
</html>
