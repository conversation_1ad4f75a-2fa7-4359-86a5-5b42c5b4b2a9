export interface HealthData {
  steps: {
    count: number;
    goal: number;
    date: string;
    loading: boolean;
    error: string | null;
  };
  heartRate: {
    value: number;
    unit: string;
    date: string;
    loading: boolean;
    error: string | null;
  };
}

export interface HealthServiceInterface {
  /**
   * Initialize the health service
   */
  initialize(): Promise<boolean>;

  /**
   * Request permissions for health data
   */
  requestPermissions(): Promise<boolean>;

  /**
   * Check if permissions are granted
   */
  hasPermissions(): Promise<boolean>;

  /**
   * Get step count for a specific date
   * @param date Date to get step count for (default: today)
   */
  getStepCount(date?: Date): Promise<{count: number; date: string}>;

  /**
   * Get heart rate for a specific date
   * @param date Date to get heart rate for (default: today)
   */
  getHeartRate(date?: Date): Promise<{value: number; unit: string; date: string}>;

  /**
   * Get step count goal
   */
  getStepCountGoal(): Promise<number>;

  /**
   * Set step count goal
   * @param goal New step count goal
   */
  setStepCountGoal(goal: number): Promise<boolean>;
}
