// Define the item type for our list
export interface ListItem {
  id: string;
  title: string;
  description: string;
  image: string;
  timestamp: number;
}

// Generate a random image URL with a specific size and ID
const getRandomImageUrl = (id: number): string => {
  return `https://picsum.photos/id/${(id % 100) + 1}/200/200`;
};

// Generate a single list item with random data
const generateListItem = (index: number): ListItem => {
  return {
    id: `item-${index}`,
    title: `Item ${index}`,
    description: `This is the description for item ${index}. It contains some text to demonstrate a multi-line item in our optimized FlatList example.`,
    image: getRandomImageUrl(index),
    timestamp: Date.now() - Math.floor(Math.random() * 10000000),
  };
};

// Generate a list of sample data with the specified length
export const generateSampleData = (count: number): ListItem[] => {
  return Array.from({length: count}, (_, index) => generateListItem(index));
};

// Pre-generate a large dataset that can be used for examples
export const SAMPLE_DATA = generateSampleData(1000);
