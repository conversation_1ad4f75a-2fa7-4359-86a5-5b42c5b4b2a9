import React, {memo, useCallback, useMemo} from 'react';
import {StyleProp, TouchableOpacity, View, ViewStyle, FlatList, Text} from 'react-native';
import {styles as createStyles} from './styles';
import {useThemeStore} from '@/store/themeStore';
import Typography from '../Typography';
import Icon from '@/components/CIcon';
import RenderHtml from 'react-native-render-html';
import CImage from '@/components/CImage';
import {Images} from '@/config';

interface CommunityCardProps {
  data: GoFitData;
  onMorePress?: () => void;
  onMoreIconPress?: () => void;
  onLikePress?: () => void;
  onCommentPress?: () => void;
  onSharePress?: () => void;
  onCardPress?: () => void;
  showMore?: boolean;
  variant?: 'default' | 'tag';
  containerStyle?: StyleProp<ViewStyle>;
  key?: string;
}

interface GoFitData {
  title?: string;
  description?: string;
  image?: string;
  file_thumbnail?: string;
  tags?: GoFitTag[];
}

interface GoFitTag {
  id: number;
  name: string;
  title: string;
  color?: string;
}

const CommunityCard = memo((props: CommunityCardProps) => {
  const {
    data,
    onMorePress = () => {},
    onMoreIconPress = () => {},
    onLikePress = () => {},
    onCommentPress = () => {},
    onSharePress = () => {},
    onCardPress = () => {},
    showMore = true,
    variant = 'default',
    containerStyle,
    key = '',
  } = props;

  const maxDescriptionLength = 50;

  const theme = useThemeStore();
  const styles = createStyles(theme);

  // Memoize the plainTextDescription and isLongDescription calculations
  const {plainTextDescription, isLongDescription} = useMemo(() => {
    const plainText = data?.description?.replace(/<[^>]+>/g, '') || '';
    return {
      plainTextDescription: plainText,
      isLongDescription: plainText.length > maxDescriptionLength,
    };
  }, [data?.description, maxDescriptionLength]);

  // Memoize the HTML source for RenderHtml
  const htmlSource = useMemo(
    () => ({
      html: data?.description || '',
    }),
    [data?.description],
  );

  // Memoize the card press handler
  const handleCardPress = useCallback(() => {
    onCardPress();
  }, [onCardPress]);

  // Memoize the more press handler
  const handleMorePress = useCallback(() => {
    onMorePress();
  }, [onMorePress]);

  // Memoize the more icon press handler
  const handleMoreIconPress = useCallback(() => {
    onMoreIconPress();
  }, [onMoreIconPress]);

  // Memoize the keyExtractor function
  const keyExtractor = useCallback((item: GoFitTag) => item.id.toString(), []);

  // Memoize the renderItem function
  const renderItem = useCallback(
    ({item}: {item: GoFitTag}) => (
      <TouchableOpacity
        activeOpacity={0.8}
        style={[
          styles.tag,
          {
            backgroundColor: item?.color || theme.colors.richSkyBlue,
          },
        ]}>
        <Typography variant="tagTitle" color={theme.colors.white}>
          {item?.name}
        </Typography>
      </TouchableOpacity>
    ),
    [styles.tag, theme.colors.richSkyBlue, theme.colors.white],
  );

  // Memoize the thumbnail source
  const thumbnailSource = useMemo(
    () =>
      data?.file_thumbnail && data?.file_thumbnail?.startsWith('http')
        ? {uri: data?.file_thumbnail}
        : Images.thumbnail,
    [data?.file_thumbnail],
  );

  return (
    <TouchableOpacity
      key={key}
      activeOpacity={0.8}
      onPress={handleCardPress}
      style={[styles.root, containerStyle]}>
      <View style={styles.contentContainer}>
        <Typography variant="subTitle4" style={styles.title}>
          {data?.title}
        </Typography>

        <View style={styles.descriptionContainer}>
          {!isLongDescription ? (
            <View style={{flex: 1}}>
              <RenderHtml contentWidth={20} source={htmlSource} baseStyle={styles.description} />
            </View>
          ) : (
            <View style={{flex: 1, flexDirection: 'row', flexWrap: 'wrap'}}>
              <Typography style={styles.description}>
                {plainTextDescription.slice(0, maxDescriptionLength)}{' '}
                <TouchableOpacity onPress={handleMorePress}>
                  <Typography variant="moreText" style={styles.moreText}>
                    {'more >>'}
                  </Typography>
                </TouchableOpacity>
              </Typography>
            </View>
          )}
        </View>

        <View>
          {variant === 'tag' && (
            <View style={styles.tagsContainer}>
              {data?.tags && data?.tags?.length > 0 && (
                <FlatList
                  data={data?.tags || []}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  keyExtractor={keyExtractor}
                  contentContainerStyle={{gap: 7}}
                  renderItem={renderItem}
                />
              )}
              <TouchableOpacity onPress={handleMoreIconPress} style={styles.moreIconContainer}>
                <Icon name="threeDot" color={theme.colors.white} size={18} />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
      <View style={styles.thumbnailContainer}>
        <CImage source={thumbnailSource} style={styles.thumbnail} />
      </View>
    </TouchableOpacity>
  );
});

export default memo(CommunityCard);

CommunityCard.displayName = 'CommunityCard';
