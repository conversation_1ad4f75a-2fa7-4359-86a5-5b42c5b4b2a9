import React, {useState} from 'react';
import {View, Text, StyleSheet, Button, ScrollView} from 'react-native';
import LoginTest from './LoginTest';
import ReactQueryLoginTest from './ReactQueryLoginTest';
import DirectFetchTest from './DirectFetchTest';

const TestScreen = () => {
  const [testType, setTestType] = useState<'direct' | 'react-query' | 'fetch' | null>(null);

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>API Testing Screen</Text>

      <View style={styles.buttonContainer}>
        <Button title="Axios Test" onPress={() => setTestType('direct')} />
        <View style={styles.buttonSpacer} />
        <Button title="React Query" onPress={() => setTestType('react-query')} />
        <View style={styles.buttonSpacer} />
        <Button title="Fetch Test" onPress={() => setTestType('fetch')} />
      </View>

      {testType === 'direct' && (
        <View style={styles.testContainer}>
          <Text style={styles.subtitle}>Direct Axios Test</Text>
          <LoginTest />
        </View>
      )}

      {testType === 'react-query' && (
        <View style={styles.testContainer}>
          <Text style={styles.subtitle}>React Query Test</Text>
          <ReactQueryLoginTest />
        </View>
      )}

      {testType === 'fetch' && (
        <View style={styles.testContainer}>
          <Text style={styles.subtitle}>Direct Fetch Test</Text>
          <DirectFetchTest />
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
  },
  buttonSpacer: {
    width: 16,
  },
  testContainer: {
    flex: 1,
    borderTopWidth: 1,
    borderTopColor: '#ccc',
    paddingTop: 20,
  },
});

export default TestScreen;
