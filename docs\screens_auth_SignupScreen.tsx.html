

<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> screens/auth/SignupScreen.tsx</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="styles/style.css">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
             
                <a href="index.html">
                    <h1 class="navbar-item">GoRaqt Documentation</h1>
                </a>
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    Documentation
                </a>
                
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
                <div class="search-wrapper">
                    <input id="search" type="text" placeholder="Search docs..." class="input">
                </div>
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Global</h3><ul><li><a href="global.html#HIDE_TAB_SCREENS">HIDE_TAB_SCREENS</a></li><li><a href="global.html#SENSITIVE_FIELDS">SENSITIVE_FIELDS</a></li><li><a href="global.html#addBreadcrumb">addBreadcrumb</a></li><li><a href="global.html#apiCall">apiCall</a></li><li><a href="global.html#authenticateWithBiometrics">authenticateWithBiometrics</a></li><li><a href="global.html#calculateAge">calculateAge</a></li><li><a href="global.html#checkAuthStatusApi">checkAuthStatusApi</a></li><li><a href="global.html#checkLocationPermission">checkLocationPermission</a></li><li><a href="global.html#cleanMemoryIfNeeded">cleanMemoryIfNeeded</a></li><li><a href="global.html#clearAllData">clearAllData</a></li><li><a href="global.html#configureStatusBar">configureStatusBar</a></li><li><a href="global.html#createHealthService">createHealthService</a></li><li><a href="global.html#deleteFcmTokenFromDB">deleteFcmTokenFromDB</a></li><li><a href="global.html#deleteNotification">deleteNotification</a></li><li><a href="global.html#disableBiometrics">disableBiometrics</a></li><li><a href="global.html#enableBiometrics">enableBiometrics</a></li><li><a href="global.html#forgotPasswordApi">forgotPasswordApi</a></li><li><a href="global.html#formatBirthdate">formatBirthdate</a></li><li><a href="global.html#getAddressFromCoordinates">getAddressFromCoordinates</a></li><li><a href="global.html#getCurrentLocationWithAddress">getCurrentLocationWithAddress</a></li><li><a href="global.html#getCurrentPosition">getCurrentPosition</a></li><li><a href="global.html#getCurrentRegion">getCurrentRegion</a></li><li><a href="global.html#getCurrentUserApi">getCurrentUserApi</a></li><li><a href="global.html#getNotificationsList">getNotificationsList</a></li><li><a href="global.html#getTabBarVisibility">getTabBarVisibility</a></li><li><a href="global.html#getUnreadNotificationsCount">getUnreadNotificationsCount</a></li><li><a href="global.html#getUserDetails">getUserDetails</a></li><li><a href="global.html#getUserProfile">getUserProfile</a></li><li><a href="global.html#handleNotificationNavigation">handleNotificationNavigation</a></li><li><a href="global.html#handleTextureError">handleTextureError</a></li><li><a href="global.html#initSentry">initSentry</a></li><li><a href="global.html#isBiometricsAvailable">isBiometricsAvailable</a></li><li><a href="global.html#isBiometricsEnabled">isBiometricsEnabled</a></li><li><a href="global.html#lastCacheClearTime">lastCacheClearTime</a></li><li><a href="global.html#logError">logError</a></li><li><a href="global.html#logMessage">logMessage</a></li><li><a href="global.html#loginWithEmail">loginWithEmail</a></li><li><a href="global.html#loginWithEmailApi">loginWithEmailApi</a></li><li><a href="global.html#logoutApi">logoutApi</a></li><li><a href="global.html#markAllNotificationsAsRead">markAllNotificationsAsRead</a></li><li><a href="global.html#markNotificationAsRead">markNotificationAsRead</a></li><li><a href="global.html#navigateBackFromExample">navigateBackFromExample</a></li><li><a href="global.html#navigateToOptimizedFlatListExample">navigateToOptimizedFlatListExample</a></li><li><a href="global.html#otpResendApi">otpResendApi</a></li><li><a href="global.html#otpVerifyApi">otpVerifyApi</a></li><li><a href="global.html#prefetchInitialQueries">prefetchInitialQueries</a></li><li><a href="global.html#prefetchPost">prefetchPost</a></li><li><a href="global.html#prefetchUser">prefetchUser</a></li><li><a href="global.html#refreshTokenApi">refreshTokenApi</a></li><li><a href="global.html#registerWithEmail">registerWithEmail</a></li><li><a href="global.html#registerWithEmailApi">registerWithEmailApi</a></li><li><a href="global.html#requestLocationPermission">requestLocationPermission</a></li><li><a href="global.html#resetPasswordApi">resetPasswordApi</a></li><li><a href="global.html#saveFcmTokenInDB">saveFcmTokenInDB</a></li><li><a href="global.html#schema">schema</a></li><li><a href="global.html#scrubObject">scrubObject</a></li><li><a href="global.html#scrubSensitiveData">scrubSensitiveData</a></li><li><a href="global.html#setCustomStatusBar">setCustomStatusBar</a></li><li><a href="global.html#setDarkStatusBar">setDarkStatusBar</a></li><li><a href="global.html#setLightStatusBar">setLightStatusBar</a></li><li><a href="global.html#setNavigationContext">setNavigationContext</a></li><li><a href="global.html#setStatusBarHidden">setStatusBarHidden</a></li><li><a href="global.html#setUserContext">setUserContext</a></li><li><a href="global.html#setupMemoryCleaning">setupMemoryCleaning</a></li><li><a href="global.html#setupTextureErrorHandlers">setupTextureErrorHandlers</a></li><li><a href="global.html#signInWithFacebook">signInWithFacebook</a></li><li><a href="global.html#signInWithGoogle">signInWithGoogle</a></li><li><a href="global.html#startTransaction">startTransaction</a></li><li><a href="global.html#styles">styles</a></li><li><a href="global.html#updateUserProfile">updateUserProfile</a></li><li><a href="global.html#useAuthStatus">useAuthStatus</a></li><li><a href="global.html#useCurrentUser">useCurrentUser</a></li><li><a href="global.html#useLogin">useLogin</a></li><li><a href="global.html#useRegister">useRegister</a></li><li><a href="global.html#useSafeArea">useSafeArea</a></li><li><a href="global.html#useSocialLogin">useSocialLogin</a></li><li><a href="global.html#useUpdateUserProfile">useUpdateUserProfile</a></li><li><a href="global.html#useUserProfile">useUserProfile</a></li></ul></div><div class="category"><h2>Components</h2><h3>Components</h3><ul><li><a href="Advertisement.html">Advertisement</a></li><li><a href="BiometricGate.html">BiometricGate</a></li><li><a href="CButton.html">CButton</a></li><li><a href="CreateGoRaqtProfile.html">CreateGoRaqtProfile</a></li><li><a href="CustomMapView.html">CustomMapView</a></li><li><a href="CustomMarker.html">CustomMarker</a></li><li><a href="NotificationCard.html">NotificationCard</a></li><li><a href="OfferBanner.html">OfferBanner</a></li><li><a href="Subscription.html">Subscription</a></li></ul></div><div class="category"><h2>Interfaces</h2><h3>Interfaces</h3><ul><li><a href="AdvertisementProps.html">AdvertisementProps</a></li><li><a href="ApiResponse.html">ApiResponse</a></li><li><a href="BiometricGateProps.html">BiometricGateProps</a></li><li><a href="CButtonProps.html">CButtonProps</a></li><li><a href="CreateProfileProps.html">CreateProfileProps</a></li><li><a href="CustomMarkerProps.html">CustomMarkerProps</a></li><li><a href="DrawerItemType.html">DrawerItemType</a></li><li><a href="ForgotPasswordScreenProps.html">ForgotPasswordScreenProps</a></li><li><a href="Notification.html">Notification</a></li><li><a href="NotificationCardProps.html">NotificationCardProps</a></li><li><a href="PaginationState.html">PaginationState</a></li><li><a href="PermissionsScreenProps.html">PermissionsScreenProps</a></li><li><a href="RouteParams.html">RouteParams</a></li><li><a href="SentryNavigationContainerProps.html">SentryNavigationContainerProps</a></li><li><a href="SocialRegisterRequest.html">SocialRegisterRequest</a></li><li><a href="SplashScreenProps.html">SplashScreenProps</a></li><li><a href="TermsOfServiceScreenProps.html">TermsOfServiceScreenProps</a></li><li><a href="UseLogoutReturn.html">UseLogoutReturn</a></li></ul><h3><a href="global.html">Global</a></h3></div><div class="category"><h2>Navigation</h2><h3>Components</h3><ul><li><a href="DrawerNavigator.html">DrawerNavigator</a></li><li><a href="SentryNavigationContainer.html">SentryNavigationContainer</a></li></ul></div><div class="category"><h2>Screens</h2><h3>Components</h3><ul><li><a href="CommunityDetails.html">CommunityDetails</a></li><li><a href="DirectFetchTest.html">DirectFetchTest</a></li><li><a href="EmailSignupScreen.html">EmailSignupScreen</a></li><li><a href="ForgotPasswordScreen.html">ForgotPasswordScreen</a></li><li><a href="LoginScreen.html">LoginScreen</a></li><li><a href="MyMatchesScreen.html">MyMatchesScreen</a></li><li><a href="NotificationsListScreen.html">NotificationsListScreen</a></li><li><a href="PermissionsScreen.html">PermissionsScreen</a></li><li><a href="SignupScreen.html">SignupScreen</a></li><li><a href="SplashScreen.html">SplashScreen</a></li><li><a href="TermsOfServiceScreen.html">TermsOfServiceScreen</a></li><li><a href="Verification.html">Verification</a></li></ul></div><div class="category"><h2>Utils</h2><h3>Global</h3><ul><li><a href="global.html#api">api</a></li><li><a href="global.html#socialLoginApi">socialLoginApi</a></li><li><a href="global.html#useLogout">useLogout</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Source</p>
                    <h1>screens/auth/SignupScreen.tsx</h1>
                </header>
                



    
    <section>
        <article>
            <pre class="prettyprint source linenums"><code>import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, ActivityIndicator} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation/index';
import {useThemeStore} from '@/store/themeStore';
import {useAuthStore} from '@/store/authStore';
import {getGlobalStyles} from '@utils/styleUtils';
import {signInWithGoogle, signInWithFacebook} from '@utils/auth';
import {CImage, SafeAreaView} from '@/components';
import {Images} from '@/config';
import Typography from '@/components/Typography';
import {useTranslationContext} from '@/context/TranslationContext';
import {useSocialLogin} from '@/hooks/queries/useAuth';
import {IOS, toaster} from '@/utils/commonFunctions';
import {logEvent} from '@/utils/GA';

type SignupScreenNavigationProp = StackNavigationProp&lt;RootStackParamList, 'Signup'>;

/**
 * @component
 * @category Screens
 *
 * @description A comprehensive signup screen that provides multiple authentication options including
 * Google, Facebook, and email signup. Features loading states, error handling, and
 * analytics tracking for each authentication method.
 *
 * @see {@link Terms-conditions} - Used to display terms and conditions link and navigate user to Terms and conditions screen.
 * @see {@link Sign-in} - Used to display sign-in link and navigate user to sign-in screen.
 *
 * @return {JSX.Element} The rendered signup screen component
 */
const SignupScreen = () => {
  const navigation = useNavigation&lt;SignupScreenNavigationProp>();

  const theme = useThemeStore();
  const {t} = useTranslationContext();
  const {loginStart, loginFailure} = useAuthStore();
  const globalStyles = getGlobalStyles({theme});

  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [isFacebookLoading, setIsFacebookLoading] = useState(false);

  // Use React Query for login
  const loginMutation = useSocialLogin();

  /**
   * @function handleGoogleSignup
   * @memberof SignupScreen
   * @description
   * Initiates Google sign-in, processes the authentication result,
   * and handles success/error states with proper analytics tracking.
   * @returns {Promise&lt;void>} Promise that resolves when signup process completes
   */
  const handleGoogleSignup = async () => {
    logEvent('signup_started', {
      method: 'google',
    });
    try {
      setIsGoogleLoading(true);
      loginStart();
      const result = await signInWithGoogle();

      console.log('result ===>', result);
      if (result.success &amp;&amp; result.data) {
        loginMutation.mutate(
          {
            token: result.data.idToken,
            provider: 'google',
          },
          {
            onSuccess: e => {
              // Navigate to main tabs on successful login
              // toaster('success', e.message, 'top');
              logEvent('signup_completed', {
                method: 'google',
                user_id: e?.data?.user?.id,
              });
              navigation.navigate('MainTabs');
            },
            onError: error => {
              logEvent('signup_failed', {
                method: 'google',
              });
              // Error is already handled by the mutation
              console.log('error ===>', error);
              toaster('error', error.message, 'top');
              loginFailure(error?.message || 'Google sign in failed');
            },
          },
        );
      } else {
        logEvent('signup_failed', {
          method: 'google',
        });
        loginFailure(result.error || 'Google sign in failed');
      }
      setIsGoogleLoading(false);
    } catch (error) {
      logEvent('signup_failed', {
        method: 'google',
      });
      setIsGoogleLoading(false);
      const errorMessage = error instanceof Error ? error.message : 'Google signup error';
      loginFailure(errorMessage);
      console.error('Google signup error:', error);
    }
  };

  /**
   * @function handleFacebookSignup
   * @memberof SignupScreen
   * @description
   * Initiates Facebook sign-in, processes the authentication result with
   * platform-specific token handling (iOS vs Android), and manages
   * success/error states with analytics tracking.
   * @returns {Promise&lt;void>} Promise that resolves when signup process completes
   */
  const handleFacebookSignup = async () => {
    logEvent('signup_started', {
      method: 'facebook',
    });
    try {
      setIsFacebookLoading(true);
      loginStart();
      const result = await signInWithFacebook();

      console.log('signInWithFacebook ===>', result);
      if (result.success &amp;&amp; result.data) {
        // login(result.user);
        // navigation.navigate('MainTabs');
        const accessTokenValue = IOS
          ? result?.data?.authenticationToken
          : result?.data?.accessToken || '';
        if (accessTokenValue) {
          loginMutation.mutate(
            {
              token: accessTokenValue,
              provider: 'facebook',
            },
            {
              onSuccess: e => {
                // Navigate to main tabs on successful login
                // toaster('success', e.message, 'top');
                logEvent('signup_completed', {
                  method: 'facebook',
                });
                navigation.navigate('MainTabs');
              },
              onError: error => {
                // Error is already handled by the mutation
                console.log('error ===>', error);
                logEvent('signup_failed', {
                  method: 'facebook',
                });
                toaster('error', error.message, 'top');
                loginFailure(error.message || 'Facebook sign in failed');
              },
            },
          );
        }
      } else {
        logEvent('signup_failed', {
          method: 'facebook',
        });
        loginFailure(result.error || 'Facebook sign in failed');
      }
      setIsFacebookLoading(false);
    } catch (error) {
      logEvent('signup_failed', {
        method: 'facebook',
      });
      setIsFacebookLoading(false);
      const errorMessage = error instanceof Error ? error.message : 'Facebook signup error';
      loginFailure(errorMessage);
      console.error('Facebook signup error:', error);
    }
  };

  /**
   * @function handleEmailSignup
   * @memberof SignupScreen
   * @description Redirect user to email signup screen
   */
  const handleEmailSignup = () => {
    logEvent('signup_started', {
      method: 'goraqt',
    });
    navigation.navigate('EmailSignup');
  };

  const handleTermsPress = () => {
    navigation.navigate('TermsAndConditions');
  };

  const renderButtonContent = (source: string, text: string, isLoading: boolean) => {
    return isLoading ? (
      &lt;ActivityIndicator size="small" color={theme.colors.secondary} />
    ) : (
      &lt;>
        &lt;CImage source={source} style={{width: 30, height: 30}} resizeMode="contain" />
        &lt;Text style={[styles(theme).socialButtonText, {color: theme.colors.black}]}>{text}&lt;/Text>
      &lt;/>
    );
  };

  return (
    &lt;SafeAreaView includeTop style={[styles(theme).container]}>
      &lt;View style={styles(theme).content}>
        &lt;Typography variant="title" style={[styles(theme).title]}>
          {t('signupScreen.signUpWith')}
        &lt;/Typography>

        &lt;View style={styles(theme).buttonsContainer}>
          &lt;TouchableOpacity
            style={[
              styles(theme).socialButton,
              isFacebookLoading &amp;&amp; styles(theme).loadingButton,
              {backgroundColor: theme.colors.white},
            ]}
            onPress={handleFacebookSignup}
            disabled={isFacebookLoading}
            activeOpacity={0.7}>
            {renderButtonContent(Images.facebook, t('signupScreen.facebook'), isFacebookLoading)}
          &lt;/TouchableOpacity>

          &lt;TouchableOpacity
            style={[
              styles(theme).socialButton,
              isGoogleLoading &amp;&amp; styles(theme).loadingButton,
              {backgroundColor: theme.colors.white},
            ]}
            onPress={handleGoogleSignup}
            disabled={isGoogleLoading}
            activeOpacity={0.7}>
            {renderButtonContent(Images.google, t('signupScreen.google'), isGoogleLoading)}
          &lt;/TouchableOpacity>

          &lt;TouchableOpacity
            style={[styles(theme).socialButton, {backgroundColor: theme.colors.white}]}
            onPress={handleEmailSignup}
            activeOpacity={0.7}>
            {renderButtonContent(Images.mail, t('signupScreen.email'), false)}
          &lt;/TouchableOpacity>
        &lt;/View>

        &lt;View style={styles(theme).footer}>
          &lt;Text style={[globalStyles.text, styles(theme).termsText, {color: theme.colors.text}]}>
            {t('signupScreen.bySigningUpYouAgreeToOur')}{' '}
            &lt;Text
              style={[styles(theme).termsLink, {color: theme.colors.text}]}
              onPress={handleTermsPress}>
              {t('signupScreen.termsAndConditions')}
            &lt;/Text>
          &lt;/Text>
        &lt;/View>
      &lt;/View>

      &lt;View style={styles(theme).loginContainer}>
        &lt;Text style={[globalStyles.text, styles(theme).loginText, {color: theme.colors.text}]}>
          {t('signupScreen.alreadyHaveAnAccount')}{' '}
          &lt;Text
            style={[styles(theme).loginLink, {color: theme.colors.text}]}
            onPress={() => navigation.navigate('Login')}>
            {t('signupScreen.signin')}
          &lt;/Text>
        &lt;/Text>
      &lt;/View>
    &lt;/SafeAreaView>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    title: {
      color: theme.colors.text,
      marginBottom: 20,
    },
    buttonsContainer: {
      marginBottom: 20,
      width: '100%',
    },
    socialButton: {
      flexDirection: 'row',
      padding: 14,
      borderRadius: 8,
      alignItems: 'center',
      marginBottom: 16,
      width: '100%',
      elevation: 1,
      height: 52,
    },
    loadingButton: {
      opacity: 0.7,
    },
    socialButtonText: {
      fontWeight: '400',
      fontSize: 16,
      marginLeft: 10,
    },
    footer: {
      width: '100%',
      paddingHorizontal: 20,
      marginTop: 'auto',
    },
    termsText: {
      textAlign: 'center',
      fontSize: 12,
      lineHeight: 18,
    },
    termsLink: {
      fontWeight: 'bold',
      textDecorationLine: 'underline',
    },
    loginContainer: {
      paddingBottom: 24,
      alignItems: 'center',
    },
    loginText: {
      fontSize: 14,
    },
    loginLink: {
      fontWeight: 'bold',
      textDecorationLine: 'underline',
    },
  });

export default SignupScreen;
</code></pre>
        </article>
    </section>




            </div>
            
            <!-- Footer removed via patch -->
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

<script src="scripts/search.js"> </script>


</body>
</html>
