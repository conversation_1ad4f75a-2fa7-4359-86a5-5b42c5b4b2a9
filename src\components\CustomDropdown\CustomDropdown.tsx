import {Dropdown, MultiSelect} from 'react-native-element-dropdown';
import React, {useMemo} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {View, StyleSheet, StyleProp, ViewStyle, TextStyle, TouchableOpacity} from 'react-native';
import Typography from '../Typography';
import {Text} from 'react-native';

interface SportItem {
  label: string;
  value: string;
}

interface CustomDropdownProps {
  sportsData: SportItem[];
  initialValue?: string;
  value?: string;
  onChangeValue?: (value: string) => void;
  containerStyle?: StyleProp<ViewStyle>;
  mainStyles?: StyleProp<ViewStyle>;
  contentContainerStyle?: StyleProp<ViewStyle>;
  label?: string;
  labelStyle?: TextStyle;
  placeholder?: string;
  multiselect?: boolean;
  error?: string;
  placeholderStyle?: TextStyle;
}

const CustomDropdown = ({
  // Smaple Data Example
  sportsData = [
    {label: 'Tennis', value: 'Tennis'},
    {label: 'Basketball', value: 'Basketball'},
    {label: 'Soccer', value: 'Soccer'},
    {label: 'Volleyball', value: 'Volleyball'},
    {label: 'Baseball', value: 'Baseball'},
    {label: 'Football', value: 'Football'},
    {label: 'Golf', value: 'Golf'},
    {label: 'Cricket', value: 'Cricket'},
    {label: 'Hockey', value: 'Hockey'},
    {label: 'Rugby', value: 'Rugby'},
  ],
  initialValue = '',
  onChangeValue,
  value,
  containerStyle,
  mainStyles,
  contentContainerStyle,
  label = '',
  labelStyle,
  placeholder = 'Select sport',
  multiselect = false,
  error = '',
  placeholderStyle,
}: CustomDropdownProps) => {
  const theme = useThemeStore();
  // const [selectedSport, setSelectedSport] = useState<string>(initialValue);

  const styles = useMemo(() => createStyles(theme), [theme]);

  const handleChange = (item: SportItem) => {
    // setSelectedSport(item.value);
    if (onChangeValue) {
      onChangeValue(multiselect ? item : item.value);
    }
  };

  const renderItem = item => {
    return (
      <View style={[styles.itemContainer, {padding: 20}]}>
        <Text style={styles.itemText}>{item.label}</Text>
      </View>
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Typography style={[labelStyle, {marginBottom: 6}]} variant="body">
          {label}
        </Typography>
      )}
      {multiselect ? (
        <MultiSelect
          style={[styles.dropdown, mainStyles]}
          placeholderStyle={[styles.placeholderText, placeholderStyle]}
          inputSearchStyle={styles.inputSearchStyle}
          containerStyle={[styles.dropdownContainer, contentContainerStyle]}
          iconStyle={styles.icon}
          activeColor={`${theme.colors.primary}20`}
          data={sportsData}
          labelField="label"
          valueField="value"
          value={value}
          search
          placeholder={placeholder}
          maxHeight={400}
          searchPlaceholder="Search..."
          // onChange={val => console.log(val)}
          onChange={handleChange}
          renderItem={renderItem}
          renderSelectedItem={(item, unSelect) => (
            <TouchableOpacity onPress={() => unSelect && unSelect(item)}>
              <View style={styles.selectedStyle}>
                <Text style={styles.itemText}>{item.label}</Text>
              </View>
            </TouchableOpacity>
          )}
        />
      ) : (
        <Dropdown
          data={sportsData}
          labelField="label"
          valueField="value"
          value={value}
          onChange={handleChange}
          placeholder={placeholder}
          style={[styles.dropdown, mainStyles]}
          placeholderStyle={[styles.placeholderText, placeholderStyle]}
          selectedTextStyle={styles.selectedText}
          iconStyle={styles.icon}
          maxHeight={250}
          containerStyle={[styles.dropdownContainer, contentContainerStyle]}
          itemContainerStyle={[styles.itemContainer]}
          itemTextStyle={styles.itemText}
          activeColor={`${theme.colors.primary}20`}
        />
      )}
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      width: '100%',
    },
    dropdown: {
      borderWidth: 1,
      borderColor: theme.colors.primary,
      backgroundColor: 'transparent',
      borderRadius: 50,
      paddingHorizontal: 17,
      paddingVertical: 8,
    },
    placeholderText: {
      color: theme.colors.text,
    },
    inputSearchStyle: {borderRadius: 12, margin: 12, height: 50, color: theme.colors.text},
    selectedText: {
      color: theme.colors.text,
      fontWeight: 'bold',
    },
    icon: {
      tintColor: theme.colors.text,
    },
    dropdownContainer: {
      borderRadius: 20,
      borderWidth: 1,
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.background,
    },
    itemContainer: {
      borderBottomColor: theme.colors.border,
      borderRadius: 20,
      borderBottomWidth: 0.5,
    },
    itemText: {
      color: theme.colors.text,
      fontSize: theme.fontSize.medium,
    },

    selectedStyle: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 8,
      backgroundColor: theme.colors.primary,
      marginVertical: 8,
      marginRight: 10,
      paddingHorizontal: 12,
      paddingVertical: 8,
    },
    errorText: {
      color: theme.colors.coralRed,
      fontSize: theme.fontSize.small,
      marginTop: -6,
    },
  });

export default React.memo(CustomDropdown);
