import React, {useState, useMemo, useCallback} from 'react';
import {FlatList, View, ActivityIndicator, Platform, TouchableOpacity} from 'react-native';
import {styles as createStyles} from './styles';
import {useThemeStore} from '@/store/themeStore';
import {
  CButton,
  CInput,
  CustomModal,
  Header,
  NoData,
  RadioSelect,
  SafeAreaView,
} from '@/components';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import GroupCard from '@/components/GroupCard';
import useTranslation from '@/hooks/useTranslation';
import {useInfiniteQuery, useQueryClient} from '@tanstack/react-query';
import {getPublicGroups, toaster} from '@/utils/commonFunctions';
import CLoader from '@/components/CLoader';
import {useJoinOrLeaveGroup, useRequestToJoinGroup} from '@/hooks/queries/groups';
import debounce from 'lodash/debounce';
import Typography from '@/components/Typography';
import CIcon from '@/components/CIcon';
import {predefinedTags} from '@/config/staticData';

type NavigationProp = StackNavigationProp<CommunityStackParamList>;

interface GroupsData {
  id: string;
  group_name: string;
  total_members: number;
  location: string;
  highlighted: boolean;
  type: string;
  locked: boolean;
  is_member?: boolean;
  login_user_exist?: boolean;
  group_type: string;
  has_user_requested?: boolean;
}

interface ApiResponse {
  status: boolean;
  data: GroupsData[];
  pagination: {
    total: number;
    totalPages: number;
    currentPage: number;
    perPage: number;
  };
}

const JoinGroups = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const queryClient = useQueryClient();

  const navigation = useNavigation<NavigationProp>();
  const joinOrLeaveMutation = useJoinOrLeaveGroup();
  const requestToJoinGroupMutation = useRequestToJoinGroup();

  const [inputValue, setInputValue] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [joiningGroupId, setJoiningGroupId] = useState<string | null>(null);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedValues, setSelectedValues] = React.useState<string[]>([]);
  const [appliedFilters, setAppliedFilters] = useState<string[]>([]);

  const {t} = useTranslation();

  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchQuery(value);
      }, 500),
    [],
  );

  const handleSearchChange = useCallback(
    (value: string) => {
      setInputValue(value);
      debouncedSearch(value);
    },
    [debouncedSearch],
  );

  const handleJoinGroup = async (groupId: string) => {
    try {
      setJoiningGroupId(groupId);

      const response = await joinOrLeaveMutation.mutateAsync({groupId: groupId, refetch: false});

      // Check if response is successful, then remove the group from the list
      if (response?.status === true) {
        queryClient.setQueryData(
          ['public-groups', searchQuery, filterSelectedVal],
          (oldData: any) => {
            if (!oldData) return oldData;

            return {
              ...oldData,
              pages: oldData.pages.map((page: any) => ({
                ...page,
                data: page.data.filter((group: GroupsData) => group.id !== groupId),
              })),
            };
          },
        );
      }
    } catch (error: any) {
      toaster('error', error.message, 'top');
    } finally {
      setJoiningGroupId(null);
    }
  };

  const handleRequestToJoinGroup = async (groupId: string) => {
    try {
      setJoiningGroupId(groupId);

      const response = await requestToJoinGroupMutation.mutateAsync({
        groupId: groupId,
        refetch: false,
      });

      if (response?.status === true) {
        toaster('success', response.message, 'top');
        queryClient.setQueryData(
          ['public-groups', searchQuery, filterSelectedVal],
          (oldData: any) => {
            if (!oldData) return oldData;
            return {
              ...oldData,
              pages: oldData.pages.map((page: any) => ({
                ...page,
                data: page.data.map((group: GroupsData) =>
                  group.id === groupId ? {...group, has_user_requested: true} : group,
                ),
              })),
            };
          },
        );
      }
    } catch (error: any) {
      toaster('error', error.message, 'top');
    } finally {
      setJoiningGroupId(null);
    }
  };

  const renderComponent = ({item}: {item: GroupsData}) => {
    return (
      <GroupCard
        name={item.group_name}
        members={item.total_members}
        highlighted={item.highlighted}
        locked={item.group_type === 'private'}
        location={item.location}
        showJoinGroup={item.group_type !== 'private'}
        showMore={false}
        onSelect={() => {
          handleRequestToJoinGroup(item.id);
        }}
        isMember={item.login_user_exist}
        containerStyle={{
          borderWidth: 1,
          borderColor: theme.colors.divider,
          marginVertical: 8,
        }}
        onPress={() => navigation.navigate('JoinGroupDetails', {id: item.id})}
        onJoinGroup={() => {
          handleJoinGroup(item.id);
        }}
        requestedForJoin={item.has_user_requested}
        isLoading={joiningGroupId === item.id}
      />
    );
  };

  const filterSelectedVal = appliedFilters.join(',');

  const {data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage, refetch} =
    useInfiniteQuery<ApiResponse>({
      queryKey: ['public-groups', searchQuery, filterSelectedVal],
      queryFn: async ({pageParam = 1}) => {
        const response = await getPublicGroups(searchQuery, pageParam as number, filterSelectedVal);
        return (
          response || {data: [], pagination: {total: 0, totalPages: 0, currentPage: 1, perPage: 10}}
        );
      },
      getNextPageParam: lastPage => {
        if (lastPage.pagination.currentPage < lastPage.pagination.totalPages) {
          return lastPage.pagination.currentPage + 1;
        }
        return undefined;
      },
      initialPageParam: 1,
    });

  const publicGroupsData = useMemo(() => {
    return data?.pages.flatMap(page => page.data) || [];
  }, [data?.pages]);
  console.log('publicGroupsData=====>>>>>', publicGroupsData);

  const FooterComponent = () => {
    if (!hasNextPage) {
      return null;
    }
    return <ActivityIndicator color={theme.colors.activeColor} size="small" />;
  };

  const toggleSelection = (value: string) => {
    setSelectedValues(prev =>
      prev.includes(value) ? prev.filter(v => v !== value) : [...prev, value],
    );
  };

  return (
    <SafeAreaView includeBottom={false} style={styles.root}>
      <Header
        leftIcon={{
          name: 'side-menu',
          size: 24,
        }}
        rightIcons={[
          {name: 'notification', size: 24, badge: 0},
          {name: 'chat', size: 24, badge: 14},
        ]}
        pageTitle={t('joinGroupsScreen.title')}
        backgroundColor="transparent"
      />
      <View style={styles.searchContainer}>
        <View style={styles.flex}>
          <CInput
            inputStyle={styles.searchInput}
            placeholder={t('joinGroupsScreen.searchPlaceholder')}
            placeholderTextColorStyle={theme.colors.secondary}
            value={inputValue}
            onChangeText={handleSearchChange}
          />
        </View>
        <TouchableOpacity onPress={() => setIsModalVisible(true)}>
          <CIcon name="filter" size={28} color={theme.colors.white} />
        </TouchableOpacity>
      </View>

      {isLoading ? (
        <CLoader />
      ) : (
        <FlatList
          data={publicGroupsData}
          showsVerticalScrollIndicator={false}
          keyExtractor={item => item.id}
          renderItem={renderComponent}
          contentContainerStyle={styles.content}
          onEndReachedThreshold={0.5}
          onEndReached={() => {
            if (hasNextPage && !isFetchingNextPage) {
              fetchNextPage();
            }
          }}
          removeClippedSubviews={Platform.OS === 'android'}
          maxToRenderPerBatch={10}
          updateCellsBatchingPeriod={50}
          initialNumToRender={10}
          windowSize={21}
          ListFooterComponent={FooterComponent}
          ListEmptyComponent={
            !isLoading ? (
              <NoData
                title={t('joinGroupsScreen.noGroups')}
                message={t('joinGroupsScreen.noGroupsMessage')}
              />
            ) : null
          }
        />
      )}
      <CustomModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        variant="bottom"
        showCloseButton={true}
        title="Filter results">
        <View>
          <Typography variant="subtitle" style={{color: theme.colors.offWhite}}>
            {t('addMembersScreen.show')}
          </Typography>
          {predefinedTags.map(option => (
            <RadioSelect
              key={option}
              label={option}
              selected={selectedValues.includes(option)}
              onPress={() => toggleSelection(option)}
            />
          ))}

          <View style={styles.buttonContainer}>
            <CButton
              title={t('addMembersScreen.results')}
              onPress={() => {
                setAppliedFilters(selectedValues);
                setIsModalVisible(false);
                refetch();
              }}
              variant="primary"
              containerStyle={styles.resultBtn}
            />
            <CButton
              title={t('common.clearFilters')}
              onPress={() => {
                setAppliedFilters([]);
                setSelectedValues([]);
                if (appliedFilters.length > 0 && selectedValues.length > 0) {
                  refetch();
                }
                setIsModalVisible(false);
              }}
              variant="outline"
              containerStyle={styles.clearBtn}
            />
          </View>
        </View>
      </CustomModal>
    </SafeAreaView>
  );
};

export default JoinGroups;
