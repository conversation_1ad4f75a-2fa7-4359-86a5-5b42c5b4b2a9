

<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> screens/auth/Verification.tsx</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="styles/style.css">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
             
                <a href="index.html">
                    <h1 class="navbar-item">GoRaqt Documentation</h1>
                </a>
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    Documentation
                </a>
                
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
                <div class="search-wrapper">
                    <input id="search" type="text" placeholder="Search docs..." class="input">
                </div>
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Global</h3><ul><li><a href="global.html#HIDE_TAB_SCREENS">HIDE_TAB_SCREENS</a></li><li><a href="global.html#SENSITIVE_FIELDS">SENSITIVE_FIELDS</a></li><li><a href="global.html#addBreadcrumb">addBreadcrumb</a></li><li><a href="global.html#apiCall">apiCall</a></li><li><a href="global.html#authenticateWithBiometrics">authenticateWithBiometrics</a></li><li><a href="global.html#calculateAge">calculateAge</a></li><li><a href="global.html#checkAuthStatusApi">checkAuthStatusApi</a></li><li><a href="global.html#checkLocationPermission">checkLocationPermission</a></li><li><a href="global.html#cleanMemoryIfNeeded">cleanMemoryIfNeeded</a></li><li><a href="global.html#clearAllData">clearAllData</a></li><li><a href="global.html#configureStatusBar">configureStatusBar</a></li><li><a href="global.html#createHealthService">createHealthService</a></li><li><a href="global.html#deleteFcmTokenFromDB">deleteFcmTokenFromDB</a></li><li><a href="global.html#deleteNotification">deleteNotification</a></li><li><a href="global.html#disableBiometrics">disableBiometrics</a></li><li><a href="global.html#enableBiometrics">enableBiometrics</a></li><li><a href="global.html#forgotPasswordApi">forgotPasswordApi</a></li><li><a href="global.html#formatBirthdate">formatBirthdate</a></li><li><a href="global.html#getAddressFromCoordinates">getAddressFromCoordinates</a></li><li><a href="global.html#getCurrentLocationWithAddress">getCurrentLocationWithAddress</a></li><li><a href="global.html#getCurrentPosition">getCurrentPosition</a></li><li><a href="global.html#getCurrentRegion">getCurrentRegion</a></li><li><a href="global.html#getCurrentUserApi">getCurrentUserApi</a></li><li><a href="global.html#getNotificationsList">getNotificationsList</a></li><li><a href="global.html#getTabBarVisibility">getTabBarVisibility</a></li><li><a href="global.html#getUnreadNotificationsCount">getUnreadNotificationsCount</a></li><li><a href="global.html#getUserDetails">getUserDetails</a></li><li><a href="global.html#getUserProfile">getUserProfile</a></li><li><a href="global.html#handleNotificationNavigation">handleNotificationNavigation</a></li><li><a href="global.html#handleTextureError">handleTextureError</a></li><li><a href="global.html#initSentry">initSentry</a></li><li><a href="global.html#isBiometricsAvailable">isBiometricsAvailable</a></li><li><a href="global.html#isBiometricsEnabled">isBiometricsEnabled</a></li><li><a href="global.html#lastCacheClearTime">lastCacheClearTime</a></li><li><a href="global.html#logError">logError</a></li><li><a href="global.html#logMessage">logMessage</a></li><li><a href="global.html#loginWithEmail">loginWithEmail</a></li><li><a href="global.html#loginWithEmailApi">loginWithEmailApi</a></li><li><a href="global.html#logoutApi">logoutApi</a></li><li><a href="global.html#markAllNotificationsAsRead">markAllNotificationsAsRead</a></li><li><a href="global.html#markNotificationAsRead">markNotificationAsRead</a></li><li><a href="global.html#navigateBackFromExample">navigateBackFromExample</a></li><li><a href="global.html#navigateToOptimizedFlatListExample">navigateToOptimizedFlatListExample</a></li><li><a href="global.html#otpResendApi">otpResendApi</a></li><li><a href="global.html#otpVerifyApi">otpVerifyApi</a></li><li><a href="global.html#prefetchInitialQueries">prefetchInitialQueries</a></li><li><a href="global.html#prefetchPost">prefetchPost</a></li><li><a href="global.html#prefetchUser">prefetchUser</a></li><li><a href="global.html#refreshTokenApi">refreshTokenApi</a></li><li><a href="global.html#registerWithEmail">registerWithEmail</a></li><li><a href="global.html#registerWithEmailApi">registerWithEmailApi</a></li><li><a href="global.html#requestLocationPermission">requestLocationPermission</a></li><li><a href="global.html#resetPasswordApi">resetPasswordApi</a></li><li><a href="global.html#saveFcmTokenInDB">saveFcmTokenInDB</a></li><li><a href="global.html#schema">schema</a></li><li><a href="global.html#scrubObject">scrubObject</a></li><li><a href="global.html#scrubSensitiveData">scrubSensitiveData</a></li><li><a href="global.html#setCustomStatusBar">setCustomStatusBar</a></li><li><a href="global.html#setDarkStatusBar">setDarkStatusBar</a></li><li><a href="global.html#setLightStatusBar">setLightStatusBar</a></li><li><a href="global.html#setNavigationContext">setNavigationContext</a></li><li><a href="global.html#setStatusBarHidden">setStatusBarHidden</a></li><li><a href="global.html#setUserContext">setUserContext</a></li><li><a href="global.html#setupMemoryCleaning">setupMemoryCleaning</a></li><li><a href="global.html#setupTextureErrorHandlers">setupTextureErrorHandlers</a></li><li><a href="global.html#signInWithFacebook">signInWithFacebook</a></li><li><a href="global.html#signInWithGoogle">signInWithGoogle</a></li><li><a href="global.html#startTransaction">startTransaction</a></li><li><a href="global.html#styles">styles</a></li><li><a href="global.html#updateUserProfile">updateUserProfile</a></li><li><a href="global.html#useAuthStatus">useAuthStatus</a></li><li><a href="global.html#useCurrentUser">useCurrentUser</a></li><li><a href="global.html#useLogin">useLogin</a></li><li><a href="global.html#useRegister">useRegister</a></li><li><a href="global.html#useSafeArea">useSafeArea</a></li><li><a href="global.html#useSocialLogin">useSocialLogin</a></li><li><a href="global.html#useUpdateUserProfile">useUpdateUserProfile</a></li><li><a href="global.html#useUserProfile">useUserProfile</a></li></ul></div><div class="category"><h2>Components</h2><h3>Components</h3><ul><li><a href="Advertisement.html">Advertisement</a></li><li><a href="BiometricGate.html">BiometricGate</a></li><li><a href="CButton.html">CButton</a></li><li><a href="CreateGoRaqtProfile.html">CreateGoRaqtProfile</a></li><li><a href="CustomMapView.html">CustomMapView</a></li><li><a href="CustomMarker.html">CustomMarker</a></li><li><a href="NotificationCard.html">NotificationCard</a></li><li><a href="OfferBanner.html">OfferBanner</a></li><li><a href="Subscription.html">Subscription</a></li></ul></div><div class="category"><h2>Interfaces</h2><h3>Interfaces</h3><ul><li><a href="AdvertisementProps.html">AdvertisementProps</a></li><li><a href="ApiResponse.html">ApiResponse</a></li><li><a href="BiometricGateProps.html">BiometricGateProps</a></li><li><a href="CButtonProps.html">CButtonProps</a></li><li><a href="CreateProfileProps.html">CreateProfileProps</a></li><li><a href="CustomMarkerProps.html">CustomMarkerProps</a></li><li><a href="DrawerItemType.html">DrawerItemType</a></li><li><a href="ForgotPasswordScreenProps.html">ForgotPasswordScreenProps</a></li><li><a href="Notification.html">Notification</a></li><li><a href="NotificationCardProps.html">NotificationCardProps</a></li><li><a href="PaginationState.html">PaginationState</a></li><li><a href="PermissionsScreenProps.html">PermissionsScreenProps</a></li><li><a href="RouteParams.html">RouteParams</a></li><li><a href="SentryNavigationContainerProps.html">SentryNavigationContainerProps</a></li><li><a href="SocialRegisterRequest.html">SocialRegisterRequest</a></li><li><a href="SplashScreenProps.html">SplashScreenProps</a></li><li><a href="TermsOfServiceScreenProps.html">TermsOfServiceScreenProps</a></li><li><a href="UseLogoutReturn.html">UseLogoutReturn</a></li></ul><h3><a href="global.html">Global</a></h3></div><div class="category"><h2>Navigation</h2><h3>Components</h3><ul><li><a href="DrawerNavigator.html">DrawerNavigator</a></li><li><a href="SentryNavigationContainer.html">SentryNavigationContainer</a></li></ul></div><div class="category"><h2>Screens</h2><h3>Components</h3><ul><li><a href="CommunityDetails.html">CommunityDetails</a></li><li><a href="DirectFetchTest.html">DirectFetchTest</a></li><li><a href="EmailSignupScreen.html">EmailSignupScreen</a></li><li><a href="ForgotPasswordScreen.html">ForgotPasswordScreen</a></li><li><a href="LoginScreen.html">LoginScreen</a></li><li><a href="MyMatchesScreen.html">MyMatchesScreen</a></li><li><a href="NotificationsListScreen.html">NotificationsListScreen</a></li><li><a href="PermissionsScreen.html">PermissionsScreen</a></li><li><a href="SignupScreen.html">SignupScreen</a></li><li><a href="SplashScreen.html">SplashScreen</a></li><li><a href="TermsOfServiceScreen.html">TermsOfServiceScreen</a></li><li><a href="Verification.html">Verification</a></li></ul></div><div class="category"><h2>Utils</h2><h3>Global</h3><ul><li><a href="global.html#api">api</a></li><li><a href="global.html#socialLoginApi">socialLoginApi</a></li><li><a href="global.html#useLogout">useLogout</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Source</p>
                    <h1>screens/auth/Verification.tsx</h1>
                </header>
                



    
    <section>
        <article>
            <pre class="prettyprint source linenums"><code>import React, {useEffect, useState, useMemo} from 'react';
import {Text, TouchableOpacity, View, StyleSheet} from 'react-native';
import {useAuthStore, useThemeStore} from '@/store';
import {CButton, Icon, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import {useNavigation} from '@react-navigation/native';
import type {StackNavigationProp} from '@react-navigation/stack';
import {useLogin, useResendOtp, useVerify} from '@/hooks/queries/useAuth';
import {OtpInput} from 'react-native-otp-entry';
import {toaster} from '@/utils/commonFunctions';
import useTranslation from '@/hooks/useTranslation';
import {logEvent} from '@/utils/GA';

export const OTP_SCREEN_TIMEOUT = 5 * 60; // 5 minutes

// Move formatTime outside component to prevent recreation on each render
const formatTime = (seconds: number) => {
  const m = Math.floor(seconds / 60);
  const s = seconds % 60;
  return `${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
};

/**
 * @component
 * @category Screens
 * @description
 * This screen is used to display OTP verification. OTP will be sent on the registered email and then user need to verify for further process.
 *
 * The screen receives the following props from the route:
 *   - `email`: The email of the user.
 *   - `password`: The password of the user.
 *   - `from`: From which screen user come here.
 *
 * @param {{from: string, email: string, password: string}} route.params
 * @returns {JSX.Element}
 */

const Verification = ({route}: {route: any}) => {
  const {email, password, from} = route.params;
  const navigation = useNavigation&lt;StackNavigationProp&lt;any>>();
  const theme = useThemeStore();
  const {isApiStatus} = useAuthStore();
  const {t} = useTranslation();
  const inputSpacing = 10;

  const verifyOtpApi = useVerify();
  const resendOtpApi = useResendOtp();
  const loginMutation = useLogin();

  const [timer, setTimer] = useState(OTP_SCREEN_TIMEOUT);
  const [isResendDisabled, setIsResendDisabled] = useState(true);
  const [otp, setOtp] = useState('');

  // Memoize the formatted time to prevent unnecessary re-renders
  const formattedTime = useMemo(() => formatTime(timer), [timer]);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (isResendDisabled) {
      interval = setInterval(() => {
        setTimer(prev => {
          if (prev &lt;= 1) {
            clearInterval(interval!);
            setIsResendDisabled(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isResendDisabled]);

  /**
   * @function onSubmit
   * @memberof Verification
   * @description
   * Handles form submission and OTP verification.
   * After successful OTP verification user process further.
   * @param {FormData} data - Form data containing email and OTP code.
   */
  const onSubmit = (code: string) => {
    if (isApiStatus) {
      verifyOtpApi.mutate(
        {email: email, code: code},
        {
          onSuccess: response => {
            if (response?.status) {
              if (from === 'login') {
                logEvent('otp_verification_success', {
                  email: email,
                  method: 'Goraqt',
                });
                loginMutation.mutate(
                  {
                    email: email,
                    password: password,
                  },
                  {
                    onSuccess: loginResponse => {
                      toaster('success', loginResponse.message, 'top');
                      navigation.replace('MainTabs');
                    },
                    onError: error => {
                      toaster('error', error.message, 'top');
                    },
                  },
                );
              } else {
                toaster('success', response.message, 'top');
                navigation.replace('Login', {
                  email: response?.data?.email || email,
                });
              }
            } else {
              logEvent('otp_verification_failed', {
                email: email,
                method: 'Goraqt',
              });
              toaster('error', response?.message, 'top');
            }
          },
          onError: error => {
            toaster('error', error.message, 'top');
          },
        },
      );
    } else {
      const mockUserData = {
        id: '123',
        name: 'Test User',
        email: '<EMAIL>',
      };
      useAuthStore.getState().login({user: mockUserData});
    }
  };

  const handleResend = () => {
    setIsResendDisabled(true);
    setOtp('');
    if (timer === 0) {
      resendOtp();
    }
  };

  /**
   * @function resendOtp
   * @memberof Verification
   * @description
   * Handles resend OTP functionality after OTP expiration.
   */
  const resendOtp = () => {
    if (isApiStatus) {
      resendOtpApi.mutate(
        {email: email},
        {
          onSuccess: response => {
            if (response?.status) {
              setTimer(OTP_SCREEN_TIMEOUT);
              toaster('success', response?.message, 'top');
            } else {
              toaster('error', response?.message, 'top');
            }
          },
          onError: error => {
            toaster('error', error.message, 'top');
          },
        },
      );
    }
  };

  return (
    &lt;SafeAreaView includeTop style={[styles.container, {backgroundColor: theme.colors.background}]}>
      {/* Header */}
      &lt;View style={styles.header}>
        &lt;TouchableOpacity
          onPress={() => navigation.popToTop()}
          style={styles.backButton}
          activeOpacity={0.7}
          hitSlop={{top: 10, right: 10, bottom: 10, left: 10}}>
          &lt;Icon name="Left-chevron" size={22} color={theme.colors.gray} />
        &lt;/TouchableOpacity>
        &lt;Typography
          variant="subtitle"
          style={[
            styles.titleText,
            {
              color: theme.colors.text,
              fontSize: theme.fontSize[theme.fontSize.default] + 8,
            },
          ]}>
          {t('verificationScreen.title')}
        &lt;/Typography>
      &lt;/View>

      {/* Title */}
      &lt;Typography
        variant="title"
        style={[
          styles.subtitle,
          {
            color: theme.colors.white,
            fontSize: theme.fontSize.xxlarge,
          },
        ]}>
        {t('verificationScreen.enterCode')}
      &lt;/Typography>

      {/* OTP Inputs */}
      &lt;View style={[styles.otpRow, {gap: inputSpacing}]}>
        &lt;OtpInput
          // ref={otpRef}
          numberOfDigits={6}
          focusColor={theme.colors.activeColor}
          autoFocus={true}
          blurOnFilled={true}
          disabled={false}
          type="numeric"
          secureTextEntry={false}
          focusStickBlinkingDuration={500}
          onTextChange={text => setOtp(text)}
          onFilled={text => onSubmit(text)}
          theme={{
            pinCodeContainerStyle: {
              borderColor: theme.colors.activeColor,
              width: 50,
            },
            pinCodeTextStyle: {color: theme.colors.activeColor},
          }}
        />
      &lt;/View>

      {/* Resend */}
      &lt;TouchableOpacity
        onPress={handleResend}
        disabled={isResendDisabled}
        style={styles.resendContainer}>
        &lt;Text
          style={{
            color: isResendDisabled ? theme.colors.white2 : theme.colors.primary,
            fontSize: theme.fontSize.medium,
          }}>
          {isResendDisabled
            ? `${t('verificationScreen.resendCodeIn')} ${formattedTime}`
            : t('verificationScreen.resendCode')}
        &lt;/Text>
      &lt;/TouchableOpacity>

      {/* Confirm Button */}
      &lt;CButton
        title={t('verificationScreen.verify')}
        variant="primary"
        onPress={() => onSubmit(otp)}
        isDisabled={otp.length &lt; 6}
        loading={verifyOtpApi.isPending || loginMutation.isPending}
      />
    &lt;/SafeAreaView>
  );
};

export default Verification;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    marginBottom: 30,
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingTop: 20,
    gap: 10,
  },
  backButton: {
    padding: 5,
  },
  titleText: {
    fontWeight: 'bold',
  },
  subtitle: {
    marginBottom: 10,
  },
  otpRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  otpInput: {
    borderWidth: 1,
    borderRadius: 10,
    textAlign: 'center',
    fontSize: 20,
  },
  resendContainer: {
    alignSelf: 'flex-end',
    marginBottom: 10,
    padding: 2,
  },
});
</code></pre>
        </article>
    </section>




            </div>
            
            <!-- Footer removed via patch -->
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

<script src="scripts/search.js"> </script>


</body>
</html>
