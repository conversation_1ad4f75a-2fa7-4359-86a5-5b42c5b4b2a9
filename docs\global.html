<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> Global</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="styles/style.css">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
             
                <a href="index.html">
                    <h1 class="navbar-item">GoRaqt Documentation</h1>
                </a>
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    Documentation
                </a>
                
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
                <div class="search-wrapper">
                    <input id="search" type="text" placeholder="Search docs..." class="input">
                </div>
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Global</h3><ul><li><a href="global.html#HIDE_TAB_SCREENS">HIDE_TAB_SCREENS</a></li><li><a href="global.html#SENSITIVE_FIELDS">SENSITIVE_FIELDS</a></li><li><a href="global.html#addBreadcrumb">addBreadcrumb</a></li><li><a href="global.html#apiCall">apiCall</a></li><li><a href="global.html#authenticateWithBiometrics">authenticateWithBiometrics</a></li><li><a href="global.html#calculateAge">calculateAge</a></li><li><a href="global.html#checkAuthStatusApi">checkAuthStatusApi</a></li><li><a href="global.html#checkLocationPermission">checkLocationPermission</a></li><li><a href="global.html#cleanMemoryIfNeeded">cleanMemoryIfNeeded</a></li><li><a href="global.html#clearAllData">clearAllData</a></li><li><a href="global.html#configureStatusBar">configureStatusBar</a></li><li><a href="global.html#createHealthService">createHealthService</a></li><li><a href="global.html#deleteFcmTokenFromDB">deleteFcmTokenFromDB</a></li><li><a href="global.html#deleteNotification">deleteNotification</a></li><li><a href="global.html#disableBiometrics">disableBiometrics</a></li><li><a href="global.html#enableBiometrics">enableBiometrics</a></li><li><a href="global.html#forgotPasswordApi">forgotPasswordApi</a></li><li><a href="global.html#formatBirthdate">formatBirthdate</a></li><li><a href="global.html#getAddressFromCoordinates">getAddressFromCoordinates</a></li><li><a href="global.html#getCurrentLocationWithAddress">getCurrentLocationWithAddress</a></li><li><a href="global.html#getCurrentPosition">getCurrentPosition</a></li><li><a href="global.html#getCurrentRegion">getCurrentRegion</a></li><li><a href="global.html#getCurrentUserApi">getCurrentUserApi</a></li><li><a href="global.html#getNotificationsList">getNotificationsList</a></li><li><a href="global.html#getTabBarVisibility">getTabBarVisibility</a></li><li><a href="global.html#getUnreadNotificationsCount">getUnreadNotificationsCount</a></li><li><a href="global.html#getUserDetails">getUserDetails</a></li><li><a href="global.html#getUserProfile">getUserProfile</a></li><li><a href="global.html#handleNotificationNavigation">handleNotificationNavigation</a></li><li><a href="global.html#handleTextureError">handleTextureError</a></li><li><a href="global.html#initSentry">initSentry</a></li><li><a href="global.html#isBiometricsAvailable">isBiometricsAvailable</a></li><li><a href="global.html#isBiometricsEnabled">isBiometricsEnabled</a></li><li><a href="global.html#lastCacheClearTime">lastCacheClearTime</a></li><li><a href="global.html#logError">logError</a></li><li><a href="global.html#logMessage">logMessage</a></li><li><a href="global.html#loginWithEmail">loginWithEmail</a></li><li><a href="global.html#loginWithEmailApi">loginWithEmailApi</a></li><li><a href="global.html#logoutApi">logoutApi</a></li><li><a href="global.html#markAllNotificationsAsRead">markAllNotificationsAsRead</a></li><li><a href="global.html#markNotificationAsRead">markNotificationAsRead</a></li><li><a href="global.html#navigateBackFromExample">navigateBackFromExample</a></li><li><a href="global.html#navigateToOptimizedFlatListExample">navigateToOptimizedFlatListExample</a></li><li><a href="global.html#otpResendApi">otpResendApi</a></li><li><a href="global.html#otpVerifyApi">otpVerifyApi</a></li><li><a href="global.html#prefetchInitialQueries">prefetchInitialQueries</a></li><li><a href="global.html#prefetchPost">prefetchPost</a></li><li><a href="global.html#prefetchUser">prefetchUser</a></li><li><a href="global.html#refreshTokenApi">refreshTokenApi</a></li><li><a href="global.html#registerWithEmail">registerWithEmail</a></li><li><a href="global.html#registerWithEmailApi">registerWithEmailApi</a></li><li><a href="global.html#requestLocationPermission">requestLocationPermission</a></li><li><a href="global.html#resetPasswordApi">resetPasswordApi</a></li><li><a href="global.html#saveFcmTokenInDB">saveFcmTokenInDB</a></li><li><a href="global.html#schema">schema</a></li><li><a href="global.html#scrubObject">scrubObject</a></li><li><a href="global.html#scrubSensitiveData">scrubSensitiveData</a></li><li><a href="global.html#setCustomStatusBar">setCustomStatusBar</a></li><li><a href="global.html#setDarkStatusBar">setDarkStatusBar</a></li><li><a href="global.html#setLightStatusBar">setLightStatusBar</a></li><li><a href="global.html#setNavigationContext">setNavigationContext</a></li><li><a href="global.html#setStatusBarHidden">setStatusBarHidden</a></li><li><a href="global.html#setUserContext">setUserContext</a></li><li><a href="global.html#setupMemoryCleaning">setupMemoryCleaning</a></li><li><a href="global.html#setupTextureErrorHandlers">setupTextureErrorHandlers</a></li><li><a href="global.html#signInWithFacebook">signInWithFacebook</a></li><li><a href="global.html#signInWithGoogle">signInWithGoogle</a></li><li><a href="global.html#startTransaction">startTransaction</a></li><li><a href="global.html#styles">styles</a></li><li><a href="global.html#updateUserProfile">updateUserProfile</a></li><li><a href="global.html#useAuthStatus">useAuthStatus</a></li><li><a href="global.html#useCurrentUser">useCurrentUser</a></li><li><a href="global.html#useLogin">useLogin</a></li><li><a href="global.html#useRegister">useRegister</a></li><li><a href="global.html#useSafeArea">useSafeArea</a></li><li><a href="global.html#useSocialLogin">useSocialLogin</a></li><li><a href="global.html#useUpdateUserProfile">useUpdateUserProfile</a></li><li><a href="global.html#useUserProfile">useUserProfile</a></li></ul></div><div class="category"><h2>Components</h2><h3>Components</h3><ul><li><a href="Advertisement.html">Advertisement</a></li><li><a href="BiometricGate.html">BiometricGate</a></li><li><a href="CButton.html">CButton</a></li><li><a href="CreateGoRaqtProfile.html">CreateGoRaqtProfile</a></li><li><a href="CustomMapView.html">CustomMapView</a></li><li><a href="CustomMarker.html">CustomMarker</a></li><li><a href="NotificationCard.html">NotificationCard</a></li><li><a href="OfferBanner.html">OfferBanner</a></li><li><a href="Subscription.html">Subscription</a></li></ul></div><div class="category"><h2>Interfaces</h2><h3>Interfaces</h3><ul><li><a href="AdvertisementProps.html">AdvertisementProps</a></li><li><a href="ApiResponse.html">ApiResponse</a></li><li><a href="BiometricGateProps.html">BiometricGateProps</a></li><li><a href="CButtonProps.html">CButtonProps</a></li><li><a href="CreateProfileProps.html">CreateProfileProps</a></li><li><a href="CustomMarkerProps.html">CustomMarkerProps</a></li><li><a href="DrawerItemType.html">DrawerItemType</a></li><li><a href="ForgotPasswordScreenProps.html">ForgotPasswordScreenProps</a></li><li><a href="Notification.html">Notification</a></li><li><a href="NotificationCardProps.html">NotificationCardProps</a></li><li><a href="PaginationState.html">PaginationState</a></li><li><a href="PermissionsScreenProps.html">PermissionsScreenProps</a></li><li><a href="RouteParams.html">RouteParams</a></li><li><a href="SentryNavigationContainerProps.html">SentryNavigationContainerProps</a></li><li><a href="SocialRegisterRequest.html">SocialRegisterRequest</a></li><li><a href="SplashScreenProps.html">SplashScreenProps</a></li><li><a href="TermsOfServiceScreenProps.html">TermsOfServiceScreenProps</a></li><li><a href="UseLogoutReturn.html">UseLogoutReturn</a></li></ul><h3><a href="global.html">Global</a></h3></div><div class="category"><h2>Navigation</h2><h3>Components</h3><ul><li><a href="DrawerNavigator.html">DrawerNavigator</a></li><li><a href="SentryNavigationContainer.html">SentryNavigationContainer</a></li></ul></div><div class="category"><h2>Screens</h2><h3>Components</h3><ul><li><a href="CommunityDetails.html">CommunityDetails</a></li><li><a href="DirectFetchTest.html">DirectFetchTest</a></li><li><a href="EmailSignupScreen.html">EmailSignupScreen</a></li><li><a href="ForgotPasswordScreen.html">ForgotPasswordScreen</a></li><li><a href="LoginScreen.html">LoginScreen</a></li><li><a href="MyMatchesScreen.html">MyMatchesScreen</a></li><li><a href="NotificationsListScreen.html">NotificationsListScreen</a></li><li><a href="PermissionsScreen.html">PermissionsScreen</a></li><li><a href="SignupScreen.html">SignupScreen</a></li><li><a href="SplashScreen.html">SplashScreen</a></li><li><a href="TermsOfServiceScreen.html">TermsOfServiceScreen</a></li><li><a href="Verification.html">Verification</a></li></ul></div><div class="category"><h2>Utils</h2><h3>Global</h3><ul><li><a href="global.html#api">api</a></li><li><a href="global.html#socialLoginApi">socialLoginApi</a></li><li><a href="global.html#useLogout">useLogout</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p></p>
                    <h1>Global</h1>
                </header>
                




<section>

<header>
    
        <h2></h2>
        
    
</header>

<article>
    <div class="container-overview">
    
        

        


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
</dl>


        
    
    </div>
    
    

    

    

    

    

    

    
        <div class='vertical-section'>
            <h1>Members</h1>
            <div class="members">
            
                <div class="member">

<h4 class="name" id="HIDE_TAB_SCREENS">
    <a class="href-link" href="#HIDE_TAB_SCREENS">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        HIDE_TAB_SCREENS
    </span>
    
</h4>




<div class="description">
    <p>Predefined lists of screens where the tab bar should be hidden</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_navigationUtils.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_navigationUtils.ts.html">utils/navigationUtils.ts</a>, <a href="utils_navigationUtils.ts.html#line33">line 33</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="SENSITIVE_FIELDS">
    <a class="href-link" href="#SENSITIVE_FIELDS">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        SENSITIVE_FIELDS
    </span>
    
</h4>




<div class="description">
    <p>Sensitive fields that should be scrubbed before sending to Sentry</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_sentryUtils.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_sentryUtils.ts.html">utils/sentryUtils.ts</a>, <a href="utils_sentryUtils.ts.html#line7">line 7</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="addBreadcrumb">
    <a class="href-link" href="#addBreadcrumb">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        addBreadcrumb
    </span>
    
</h4>




<div class="description">
    <p>Add a breadcrumb to the current scope</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_sentryUtils.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_sentryUtils.ts.html">utils/sentryUtils.ts</a>, <a href="utils_sentryUtils.ts.html#line245">line 245</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="api">
    <a class="href-link" href="#api">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        api
    </span>
    
</h4>




<div class="description">
    <p>This module provides a configured Axios instance for making API requests.
It includes JWT handling, request/response interceptors, and error handling.
It also checks for internet connectivity before making requests.
The API base URL is set to a development environment.
The client supports JWT authentication with automatic token refresh.
It uses MMKV for secure token storage and retrieval.
The client handles token expiration and refreshes tokens as needed.
It provides a utility function to handle API errors and return standardized error messages.
The client is designed to be used throughout the application for consistent API interactions.</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_api.ts.html" class="button">View Source</a>
            <span>
                <a href="services_api.ts.html">services/api.ts</a>, <a href="services_api.ts.html#line38">line 38</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="authenticateWithBiometrics">
    <a class="href-link" href="#authenticateWithBiometrics">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        authenticateWithBiometrics
    </span>
    
</h4>




<div class="description">
    <p>Authenticate the user using biometrics</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_biometrics.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_biometrics.ts.html">utils/biometrics.ts</a>, <a href="utils_biometrics.ts.html#line81">line 81</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="calculateAge">
    <a class="href-link" href="#calculateAge">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        calculateAge
    </span>
    
</h4>




<div class="description">
    <p>Calculate age from birthdate</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_userApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_userApi.ts.html">services/userApi.ts</a>, <a href="services_userApi.ts.html#line77">line 77</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="checkAuthStatusApi">
    <a class="href-link" href="#checkAuthStatusApi">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        checkAuthStatusApi
    </span>
    
</h4>




<div class="description">
    <p>Check if user is authenticated by verifying token</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_authApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_authApi.ts.html">services/authApi.ts</a>, <a href="services_authApi.ts.html#line246">line 246</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="checkLocationPermission">
    <a class="href-link" href="#checkLocationPermission">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        checkLocationPermission
    </span>
    
</h4>




<div class="description">
    <p>Check if location permission is granted</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_locationService.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_locationService.ts.html">utils/locationService.ts</a>, <a href="utils_locationService.ts.html#line26">line 26</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="cleanMemoryIfNeeded">
    <a class="href-link" href="#cleanMemoryIfNeeded">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        cleanMemoryIfNeeded
    </span>
    
</h4>




<div class="description">
    <p>Clears image caches if needed based on time elapsed or memory pressure</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_memoryManager.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_memoryManager.ts.html">utils/memoryManager.ts</a>, <a href="utils_memoryManager.ts.html#line16">line 16</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="clearAllData">
    <a class="href-link" href="#clearAllData">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        clearAllData
    </span>
    
</h4>




<div class="description">
    <p>Clears all user-related data from stores and logs out the user.</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_commonFunctions.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_commonFunctions.ts.html">utils/commonFunctions.ts</a>, <a href="utils_commonFunctions.ts.html#line240">line 240</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="configureStatusBar">
    <a class="href-link" href="#configureStatusBar">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        configureStatusBar
    </span>
    
</h4>




<div class="description">
    <p>Configure status bar for Android</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_safeAreaUtils.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_safeAreaUtils.ts.html">utils/safeAreaUtils.ts</a>, <a href="utils_safeAreaUtils.ts.html#line23">line 23</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="createHealthService">
    <a class="href-link" href="#createHealthService">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        createHealthService
    </span>
    
</h4>




<div class="description">
    <p>Factory function to create the appropriate health service based on platform</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_health_HealthService.ts.html" class="button">View Source</a>
            <span>
                <a href="services_health_HealthService.ts.html">services/health/HealthService.ts</a>, <a href="services_health_HealthService.ts.html#line8">line 8</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="deleteFcmTokenFromDB">
    <a class="href-link" href="#deleteFcmTokenFromDB">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        deleteFcmTokenFromDB
    </span>
    
</h4>




<div class="description">
    <p>Delete FCM token</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_notificationsApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_notificationsApi.ts.html">services/notificationsApi.ts</a>, <a href="services_notificationsApi.ts.html#line54">line 54</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="deleteNotification">
    <a class="href-link" href="#deleteNotification">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        deleteNotification
    </span>
    
</h4>




<div class="description">
    <p>Delete a notification</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_notificationsApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_notificationsApi.ts.html">services/notificationsApi.ts</a>, <a href="services_notificationsApi.ts.html#line143">line 143</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="disableBiometrics">
    <a class="href-link" href="#disableBiometrics">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        disableBiometrics
    </span>
    
</h4>




<div class="description">
    <p>Disable biometric authentication for the app</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_biometrics.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_biometrics.ts.html">utils/biometrics.ts</a>, <a href="utils_biometrics.ts.html#line49">line 49</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="enableBiometrics">
    <a class="href-link" href="#enableBiometrics">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        enableBiometrics
    </span>
    
</h4>




<div class="description">
    <p>Enable biometric authentication for the app</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_biometrics.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_biometrics.ts.html">utils/biometrics.ts</a>, <a href="utils_biometrics.ts.html#line24">line 24</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="forgotPasswordApi">
    <a class="href-link" href="#forgotPasswordApi">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        forgotPasswordApi
    </span>
    
</h4>




<div class="description">
    <p>Forgot password</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_authApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_authApi.ts.html">services/authApi.ts</a>, <a href="services_authApi.ts.html#line193">line 193</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="formatBirthdate">
    <a class="href-link" href="#formatBirthdate">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        formatBirthdate
    </span>
    
</h4>




<div class="description">
    <p>Format birthdate from age</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_userApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_userApi.ts.html">services/userApi.ts</a>, <a href="services_userApi.ts.html#line90">line 90</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="getAddressFromCoordinates">
    <a class="href-link" href="#getAddressFromCoordinates">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        getAddressFromCoordinates
    </span>
    
</h4>




<div class="description">
    <p>Get address from coordinates using OpenStreetMap's Nominatim API (no API key required)</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_geolocationService.ts.html" class="button">View Source</a>
            <span>
                <a href="services_geolocationService.ts.html">services/geolocationService.ts</a>, <a href="services_geolocationService.ts.html#line69">line 69</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="getCurrentLocationWithAddress">
    <a class="href-link" href="#getCurrentLocationWithAddress">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        getCurrentLocationWithAddress
    </span>
    
</h4>




<div class="description">
    <p>Get current location and convert to address</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_geolocationService.ts.html" class="button">View Source</a>
            <span>
                <a href="services_geolocationService.ts.html">services/geolocationService.ts</a>, <a href="services_geolocationService.ts.html#line105">line 105</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="getCurrentPosition">
    <a class="href-link" href="#getCurrentPosition">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        getCurrentPosition
    </span>
    
</h4>




<div class="description">
    <p>Get the current position of the user
This implementation uses the built-in Geolocation API from React Native</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_locationService.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_locationService.ts.html">utils/locationService.ts</a>, <a href="utils_locationService.ts.html#line67">line 67</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="getCurrentRegion">
    <a class="href-link" href="#getCurrentRegion">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        getCurrentRegion
    </span>
    
</h4>




<div class="description">
    <p>Get the current region for the map based on user's location</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_locationService.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_locationService.ts.html">utils/locationService.ts</a>, <a href="utils_locationService.ts.html#line116">line 116</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="getCurrentUserApi">
    <a class="href-link" href="#getCurrentUserApi">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        getCurrentUserApi
    </span>
    
</h4>




<div class="description">
    <p>Get current user profile</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_authApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_authApi.ts.html">services/authApi.ts</a>, <a href="services_authApi.ts.html#line267">line 267</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="getNotificationsList">
    <a class="href-link" href="#getNotificationsList">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        getNotificationsList
    </span>
    
</h4>




<div class="description">
    <p>Get notifications list</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_notificationsApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_notificationsApi.ts.html">services/notificationsApi.ts</a>, <a href="services_notificationsApi.ts.html#line88">line 88</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="getTabBarVisibility">
    <a class="href-link" href="#getTabBarVisibility">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        getTabBarVisibility
    </span>
    
</h4>




<div class="description">
    <p>Determines if the tab bar should be hidden based on the current route</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_navigationUtils.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_navigationUtils.ts.html">utils/navigationUtils.ts</a>, <a href="utils_navigationUtils.ts.html#line12">line 12</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="getUnreadNotificationsCount">
    <a class="href-link" href="#getUnreadNotificationsCount">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        getUnreadNotificationsCount
    </span>
    
</h4>




<div class="description">
    <p>Get unread notifications count</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_notificationsApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_notificationsApi.ts.html">services/notificationsApi.ts</a>, <a href="services_notificationsApi.ts.html#line195">line 195</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="getUserDetails">
    <a class="href-link" href="#getUserDetails">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        getUserDetails
    </span>
    
</h4>




<div class="description">
    <p>Get user details</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_userApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_userApi.ts.html">services/userApi.ts</a>, <a href="services_userApi.ts.html#line98">line 98</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="getUserProfile">
    <a class="href-link" href="#getUserProfile">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        getUserProfile
    </span>
    
</h4>




<div class="description">
    <p>Get user profile</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_userApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_userApi.ts.html">services/userApi.ts</a>, <a href="services_userApi.ts.html#line47">line 47</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="handleNotificationNavigation">
    <a class="href-link" href="#handleNotificationNavigation">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        handleNotificationNavigation
    </span>
    
</h4>




<div class="description">
    <p>Handles navigation when a notification is clicked.</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="components_common_NotificationHelper.tsx.html" class="button">View Source</a>
            <span>
                <a href="components_common_NotificationHelper.tsx.html">components/common/NotificationHelper.tsx</a>, <a href="components_common_NotificationHelper.tsx.html#line79">line 79</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="handleTextureError">
    <a class="href-link" href="#handleTextureError">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        handleTextureError
    </span>
    
</h4>




<div class="description">
    <p>Utility to handle texture allocation errors
Call this when catching texture-related errors</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_memoryManager.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_memoryManager.ts.html">utils/memoryManager.ts</a>, <a href="utils_memoryManager.ts.html#line45">line 45</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="initSentry">
    <a class="href-link" href="#initSentry">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        initSentry
    </span>
    
</h4>




<div class="description">
    <p>Initialize Sentry with proper configuration</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_sentryUtils.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_sentryUtils.ts.html">utils/sentryUtils.ts</a>, <a href="utils_sentryUtils.ts.html#line46">line 46</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="isBiometricsAvailable">
    <a class="href-link" href="#isBiometricsAvailable">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        isBiometricsAvailable
    </span>
    
</h4>




<div class="description">
    <p>Check if the device supports biometric authentication</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_biometrics.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_biometrics.ts.html">utils/biometrics.ts</a>, <a href="utils_biometrics.ts.html#line10">line 10</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="isBiometricsEnabled">
    <a class="href-link" href="#isBiometricsEnabled">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        isBiometricsEnabled
    </span>
    
</h4>




<div class="description">
    <p>Check if biometric authentication is enabled</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_biometrics.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_biometrics.ts.html">utils/biometrics.ts</a>, <a href="utils_biometrics.ts.html#line66">line 66</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="lastCacheClearTime">
    <a class="href-link" href="#lastCacheClearTime">#</a>
    
    <span class="code-name">
        lastCacheClearTime
    </span>
    
</h4>




<div class="description">
    <p>Memory Manager Utility</p>
<p>This utility provides functions to help manage memory usage in the app,
particularly for texture-intensive components like maps and images.</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_memoryManager.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_memoryManager.ts.html">utils/memoryManager.ts</a>, <a href="utils_memoryManager.ts.html#line11">line 11</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="logError">
    <a class="href-link" href="#logError">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        logError
    </span>
    
</h4>




<div class="description">
    <p>Log an error to Sentry with additional context</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_sentryUtils.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_sentryUtils.ts.html">utils/sentryUtils.ts</a>, <a href="utils_sentryUtils.ts.html#line184">line 184</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="logMessage">
    <a class="href-link" href="#logMessage">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        logMessage
    </span>
    
</h4>




<div class="description">
    <p>Log a message to Sentry</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_sentryUtils.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_sentryUtils.ts.html">utils/sentryUtils.ts</a>, <a href="utils_sentryUtils.ts.html#line200">line 200</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="loginWithEmail">
    <a class="href-link" href="#loginWithEmail">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        loginWithEmail
    </span>
    
</h4>




<div class="description">
    <p>Helper function to handle email login</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_auth.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_auth.ts.html">utils/auth.ts</a>, <a href="utils_auth.ts.html#line207">line 207</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="loginWithEmailApi">
    <a class="href-link" href="#loginWithEmailApi">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        loginWithEmailApi
    </span>
    
</h4>




<div class="description">
    <p>Login with email and password</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_authApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_authApi.ts.html">services/authApi.ts</a>, <a href="services_authApi.ts.html#line21">line 21</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="logoutApi">
    <a class="href-link" href="#logoutApi">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        logoutApi
    </span>
    
</h4>




<div class="description">
    <p>Logout - clear tokens and all authentication-related storage</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_authApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_authApi.ts.html">services/authApi.ts</a>, <a href="services_authApi.ts.html#line205">line 205</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="markAllNotificationsAsRead">
    <a class="href-link" href="#markAllNotificationsAsRead">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        markAllNotificationsAsRead
    </span>
    
</h4>




<div class="description">
    <p>Mark all notifications as read</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_notificationsApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_notificationsApi.ts.html">services/notificationsApi.ts</a>, <a href="services_notificationsApi.ts.html#line171">line 171</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="markNotificationAsRead">
    <a class="href-link" href="#markNotificationAsRead">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        markNotificationAsRead
    </span>
    
</h4>




<div class="description">
    <p>Mark notification as read</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_notificationsApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_notificationsApi.ts.html">services/notificationsApi.ts</a>, <a href="services_notificationsApi.ts.html#line118">line 118</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="navigateBackFromExample">
    <a class="href-link" href="#navigateBackFromExample">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        navigateBackFromExample
    </span>
    
</h4>




<div class="description">
    <p>Navigate back from an example screen</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_examplesNavigator.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_examplesNavigator.ts.html">utils/examplesNavigator.ts</a>, <a href="utils_examplesNavigator.ts.html#line25">line 25</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="navigateToOptimizedFlatListExample">
    <a class="href-link" href="#navigateToOptimizedFlatListExample">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        navigateToOptimizedFlatListExample
    </span>
    
</h4>




<div class="description">
    <p>Navigate to the OptimizedFlatList example screen</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_examplesNavigator.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_examplesNavigator.ts.html">utils/examplesNavigator.ts</a>, <a href="utils_examplesNavigator.ts.html#line10">line 10</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="otpResendApi">
    <a class="href-link" href="#otpResendApi">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        otpResendApi
    </span>
    
</h4>




<div class="description">
    <p>Resend OTP code</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_authApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_authApi.ts.html">services/authApi.ts</a>, <a href="services_authApi.ts.html#line181">line 181</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="otpVerifyApi">
    <a class="href-link" href="#otpVerifyApi">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        otpVerifyApi
    </span>
    
</h4>




<div class="description">
    <p>Verify OTP code</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_authApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_authApi.ts.html">services/authApi.ts</a>, <a href="services_authApi.ts.html#line157">line 157</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="prefetchInitialQueries">
    <a class="href-link" href="#prefetchInitialQueries">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        prefetchInitialQueries
    </span>
    
</h4>




<div class="description">
    <p>Prefetch important data when the app starts or when the user logs in
This can improve the user experience by loading data in the background</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_prefetchQueries.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_prefetchQueries.ts.html">utils/prefetchQueries.ts</a>, <a href="utils_prefetchQueries.ts.html#line10">line 10</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="prefetchPost">
    <a class="href-link" href="#prefetchPost">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        prefetchPost
    </span>
    
</h4>




<div class="description">
    <p>Prefetch a specific post's data
Useful when you know the user will navigate to a post detail screen</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_prefetchQueries.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_prefetchQueries.ts.html">utils/prefetchQueries.ts</a>, <a href="utils_prefetchQueries.ts.html#line58">line 58</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="prefetchUser">
    <a class="href-link" href="#prefetchUser">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        prefetchUser
    </span>
    
</h4>




<div class="description">
    <p>Prefetch a specific user's data
Useful when you know the user will navigate to a user detail screen</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_prefetchQueries.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_prefetchQueries.ts.html">utils/prefetchQueries.ts</a>, <a href="utils_prefetchQueries.ts.html#line39">line 39</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="refreshTokenApi">
    <a class="href-link" href="#refreshTokenApi">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        refreshTokenApi
    </span>
    
</h4>




<div class="description">
    <p>Refresh token</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_authApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_authApi.ts.html">services/authApi.ts</a>, <a href="services_authApi.ts.html#line227">line 227</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="registerWithEmail">
    <a class="href-link" href="#registerWithEmail">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        registerWithEmail
    </span>
    
</h4>




<div class="description">
    <p>Helper function to handle email registration</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_auth.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_auth.ts.html">utils/auth.ts</a>, <a href="utils_auth.ts.html#line180">line 180</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="registerWithEmailApi">
    <a class="href-link" href="#registerWithEmailApi">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        registerWithEmailApi
    </span>
    
</h4>




<div class="description">
    <p>Register with email, password, and name</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_authApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_authApi.ts.html">services/authApi.ts</a>, <a href="services_authApi.ts.html#line135">line 135</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="requestLocationPermission">
    <a class="href-link" href="#requestLocationPermission">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        requestLocationPermission
    </span>
    
</h4>




<div class="description">
    <p>Request location permission</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_locationService.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_locationService.ts.html">utils/locationService.ts</a>, <a href="utils_locationService.ts.html#line46">line 46</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="resetPasswordApi">
    <a class="href-link" href="#resetPasswordApi">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        resetPasswordApi
    </span>
    
</h4>




<div class="description">
    <p>Verify OTP code</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_authApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_authApi.ts.html">services/authApi.ts</a>, <a href="services_authApi.ts.html#line169">line 169</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="saveFcmTokenInDB">
    <a class="href-link" href="#saveFcmTokenInDB">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        saveFcmTokenInDB
    </span>
    
</h4>




<div class="description">
    <p>save FCM token</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_notificationsApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_notificationsApi.ts.html">services/notificationsApi.ts</a>, <a href="services_notificationsApi.ts.html#line20">line 20</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="schema">
    <a class="href-link" href="#schema">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        schema
    </span>
    
</h4>




<div class="description">
    <p>Yup validation schema for login form</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="screens_auth_Login_index.tsx.html" class="button">View Source</a>
            <span>
                <a href="screens_auth_Login_index.tsx.html">screens/auth/Login/index.tsx</a>, <a href="screens_auth_Login_index.tsx.html#line24">line 24</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="scrubObject">
    <a class="href-link" href="#scrubObject">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        scrubObject
    </span>
    
</h4>




<div class="description">
    <p>Recursively scrub sensitive data from an object</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_sentryUtils.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_sentryUtils.ts.html">utils/sentryUtils.ts</a>, <a href="utils_sentryUtils.ts.html#line153">line 153</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="scrubSensitiveData">
    <a class="href-link" href="#scrubSensitiveData">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        scrubSensitiveData
    </span>
    
</h4>




<div class="description">
    <p>Scrub sensitive data from Sentry events</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_sentryUtils.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_sentryUtils.ts.html">utils/sentryUtils.ts</a>, <a href="utils_sentryUtils.ts.html#line124">line 124</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="setCustomStatusBar">
    <a class="href-link" href="#setCustomStatusBar">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        setCustomStatusBar
    </span>
    
</h4>




<div class="description">
    <p>Configure status bar with custom color</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_statusBarConfig.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_statusBarConfig.ts.html">utils/statusBarConfig.ts</a>, <a href="utils_statusBarConfig.ts.html#line24">line 24</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="setDarkStatusBar">
    <a class="href-link" href="#setDarkStatusBar">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        setDarkStatusBar
    </span>
    
</h4>




<div class="description">
    <p>Configure status bar for dark screens (black background)</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_statusBarConfig.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_statusBarConfig.ts.html">utils/statusBarConfig.ts</a>, <a href="utils_statusBarConfig.ts.html#line6">line 6</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="setLightStatusBar">
    <a class="href-link" href="#setLightStatusBar">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        setLightStatusBar
    </span>
    
</h4>




<div class="description">
    <p>Configure status bar for light screens (white/light background)</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_statusBarConfig.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_statusBarConfig.ts.html">utils/statusBarConfig.ts</a>, <a href="utils_statusBarConfig.ts.html#line15">line 15</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="setNavigationContext">
    <a class="href-link" href="#setNavigationContext">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        setNavigationContext
    </span>
    
</h4>




<div class="description">
    <p>Set navigation context in Sentry</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_sentryUtils.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_sentryUtils.ts.html">utils/sentryUtils.ts</a>, <a href="utils_sentryUtils.ts.html#line110">line 110</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="setStatusBarHidden">
    <a class="href-link" href="#setStatusBarHidden">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        setStatusBarHidden
    </span>
    
</h4>




<div class="description">
    <p>Hide the status bar</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_statusBarConfig.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_statusBarConfig.ts.html">utils/statusBarConfig.ts</a>, <a href="utils_statusBarConfig.ts.html#line34">line 34</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="setUserContext">
    <a class="href-link" href="#setUserContext">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        setUserContext
    </span>
    
</h4>




<div class="description">
    <p>Set user context in Sentry</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_sentryUtils.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_sentryUtils.ts.html">utils/sentryUtils.ts</a>, <a href="utils_sentryUtils.ts.html#line90">line 90</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="setupMemoryCleaning">
    <a class="href-link" href="#setupMemoryCleaning">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        setupMemoryCleaning
    </span>
    
</h4>




<div class="description">
    <p>Setup periodic memory cleaning
This should be called once during app initialization</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_memoryManager.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_memoryManager.ts.html">utils/memoryManager.ts</a>, <a href="utils_memoryManager.ts.html#line31">line 31</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="setupTextureErrorHandlers">
    <a class="href-link" href="#setupTextureErrorHandlers">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        setupTextureErrorHandlers
    </span>
    
</h4>




<div class="description">
    <p>Set up global error handlers for texture allocation issues
This should be called during app initialization</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_memoryManager.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_memoryManager.ts.html">utils/memoryManager.ts</a>, <a href="utils_memoryManager.ts.html#line58">line 58</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="signInWithFacebook">
    <a class="href-link" href="#signInWithFacebook">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        signInWithFacebook
    </span>
    
</h4>




<div class="description">
    <p>Helper function to handle Facebook authentication</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_auth.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_auth.ts.html">utils/auth.ts</a>, <a href="utils_auth.ts.html#line75">line 75</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="signInWithGoogle">
    <a class="href-link" href="#signInWithGoogle">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        signInWithGoogle
    </span>
    
</h4>




<div class="description">
    <p>Helper function to handle Google authentication</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_auth.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_auth.ts.html">utils/auth.ts</a>, <a href="utils_auth.ts.html#line25">line 25</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="startTransaction">
    <a class="href-link" href="#startTransaction">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        startTransaction
    </span>
    
</h4>




<div class="description">
    <p>Create a performance transaction - DISABLED
This function is disabled because the Sentry version doesn't support transactions.
Instead, it adds a breadcrumb to track the operation.</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_sentryUtils.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_sentryUtils.ts.html">utils/sentryUtils.ts</a>, <a href="utils_sentryUtils.ts.html#line219">line 219</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="updateUserProfile">
    <a class="href-link" href="#updateUserProfile">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        updateUserProfile
    </span>
    
</h4>




<div class="description">
    <p>Update user profile</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_userApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_userApi.ts.html">services/userApi.ts</a>, <a href="services_userApi.ts.html#line12">line 12</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="useAuthStatus">
    <a class="href-link" href="#useAuthStatus">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        useAuthStatus
    </span>
    
</h4>




<div class="description">
    <p>Hook for checking authentication status</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="hooks_queries_useAuth.ts.html" class="button">View Source</a>
            <span>
                <a href="hooks_queries_useAuth.ts.html">hooks/queries/useAuth.ts</a>, <a href="hooks_queries_useAuth.ts.html#line198">line 198</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="useCurrentUser">
    <a class="href-link" href="#useCurrentUser">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        useCurrentUser
    </span>
    
</h4>




<div class="description">
    <p>Hook for getting the current user</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="hooks_queries_useAuth.ts.html" class="button">View Source</a>
            <span>
                <a href="hooks_queries_useAuth.ts.html">hooks/queries/useAuth.ts</a>, <a href="hooks_queries_useAuth.ts.html#line209">line 209</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="useLogin">
    <a class="href-link" href="#useLogin">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        useLogin
    </span>
    
</h4>




<div class="description">
    <p>Hook for logging in with email and password</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="hooks_queries_useAuth.ts.html" class="button">View Source</a>
            <span>
                <a href="hooks_queries_useAuth.ts.html">hooks/queries/useAuth.ts</a>, <a href="hooks_queries_useAuth.ts.html#line17">line 17</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="useRegister">
    <a class="href-link" href="#useRegister">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        useRegister
    </span>
    
</h4>




<div class="description">
    <p>Hook for registering with email, password, and name</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="hooks_queries_useAuth.ts.html" class="button">View Source</a>
            <span>
                <a href="hooks_queries_useAuth.ts.html">hooks/queries/useAuth.ts</a>, <a href="hooks_queries_useAuth.ts.html#line91">line 91</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="useSafeArea">
    <a class="href-link" href="#useSafeArea">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        useSafeArea
    </span>
    
</h4>




<div class="description">
    <p>Hook to get safe area insets with additional utility functions</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_safeAreaUtils.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_safeAreaUtils.ts.html">utils/safeAreaUtils.ts</a>, <a href="utils_safeAreaUtils.ts.html#line7">line 7</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="useSocialLogin">
    <a class="href-link" href="#useSocialLogin">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        useSocialLogin
    </span>
    
</h4>




<div class="description">
    <p>Hook for logging in with email and password</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="hooks_queries_useAuth.ts.html" class="button">View Source</a>
            <span>
                <a href="hooks_queries_useAuth.ts.html">hooks/queries/useAuth.ts</a>, <a href="hooks_queries_useAuth.ts.html#line54">line 54</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="useUpdateUserProfile">
    <a class="href-link" href="#useUpdateUserProfile">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        useUpdateUserProfile
    </span>
    
</h4>




<div class="description">
    <p>Hook for updating the user profile</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="hooks_queries_useUser.ts.html" class="button">View Source</a>
            <span>
                <a href="hooks_queries_useUser.ts.html">hooks/queries/useUser.ts</a>, <a href="hooks_queries_useUser.ts.html#line26">line 26</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

<h4 class="name" id="useUserProfile">
    <a class="href-link" href="#useUserProfile">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        useUserProfile
    </span>
    
</h4>




<div class="description">
    <p>Hook for getting the user profile</p>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="hooks_queries_useUser.ts.html" class="button">View Source</a>
            <span>
                <a href="hooks_queries_useUser.ts.html">hooks/queries/useUser.ts</a>, <a href="hooks_queries_useUser.ts.html#line15">line 15</a>
            </span>
        </p>
    
</dl>





</div>
            
            </div>
        </div>
    

    
        <div class='vertical-section'>
            <h1>Methods</h1>
            <div class="members">
            
                <div class="member">


    
    <h4 class="name" id="apiCall">
        <a class="href-link" href="#apiCall">#</a>
        
        <span class="code-name">
            
                apiCall<span class="signature">(url, method, data, headers)</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        <p>Generic API call function for making HTTP requests</p>
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>url</code></td>
  

  <td class="type">
  
  </td>

  

  

  <td class="description last"><p>The API endpoint URL</p></td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>method</code></td>
  

  <td class="type">
  
  </td>

  

  

  <td class="description last"><p>HTTP method (GET, POST, PUT, DELETE, etc.)</p></td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>data</code></td>
  

  <td class="type">
  
  </td>

  

  

  <td class="description last"><p>Request payload data (for POST, PUT, etc.)</p></td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>headers</code></td>
  

  <td class="type">
  
  </td>

  

  

  <td class="description last"><p>Custom headers to include with the request</p></td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="utils_getApiData.ts.html" class="button">View Source</a>
            <span>
                <a href="utils_getApiData.ts.html">utils/getApiData.ts</a>, <a href="utils_getApiData.ts.html#line3">line 3</a>
            </span>
        </p>
    
</dl>


















    <div class='columns method-parameter'>
        <div class="column is-2"><label>Returns:</label></div>
        <div class="column is-10">
            
                    

<div class="columns">
    
    <div class='param-desc column is-7'><p>Promise with the API response data</p></div>
    
    
</div>

                
        </div>
    </div>




</div>
            
                <div class="member">


    
    <h4 class="name" id="socialLoginApi">
        <a class="href-link" href="#socialLoginApi">#</a>
        
        <span class="code-name">
            
                socialLoginApi<span class="signature">(data)</span><span class="type-signature"> &rarr; {Promise.&lt;AuthResponse>}</span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        <p>Login with social media (Google/Facebook)</p>
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>data</code></td>
  

  <td class="type">
  
      
<code class="param-type"><a href="SocialRegisterRequest.html">SocialRegisterRequest</a></code>


  
  </td>

  

  

  <td class="description last"><p>SocialRegisterRequest with provider and credentials</p></td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="services_authApi.ts.html" class="button">View Source</a>
            <span>
                <a href="services_authApi.ts.html">services/authApi.ts</a>, <a href="services_authApi.ts.html#line105">line 105</a>
            </span>
        </p>
    
</dl>


















    <div class='columns method-parameter'>
        <div class="column is-2"><label>Returns:</label></div>
        <div class="column is-10">
            
                    

<div class="columns">
    
    
    <div class='column is-5 has-text-left'>
        <label>Type: </label>
        
<code class="param-type">Promise.&lt;AuthResponse></code>


    </div>
    
</div>

                
        </div>
    </div>




</div>
            
                <div class="member">


    
    <h4 class="name" id="styles">
        <a class="href-link" href="#styles">#</a>
        
        <span class="code-name">
            
                styles<span class="signature">(theme)</span><span class="type-signature"> &rarr; {Object}</span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        <p>Stylesheet factory function</p>
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>theme</code></td>
  

  <td class="type">
  
      
<code class="param-type">any</code>


  
  </td>

  

  

  <td class="description last"><p>Theme object containing colors and font sizes</p></td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="screens_auth_ForgotPasswordScreen.tsx.html" class="button">View Source</a>
            <span>
                <a href="screens_auth_ForgotPasswordScreen.tsx.html">screens/auth/ForgotPasswordScreen.tsx</a>, <a href="screens_auth_ForgotPasswordScreen.tsx.html#line251">line 251</a>
            </span>
        </p>
    
</dl>


















    <div class='columns method-parameter'>
        <div class="column is-2"><label>Returns:</label></div>
        <div class="column is-10">
            
                    

<div class="columns">
    
    <div class='param-desc column is-7'><p>StyleSheet object with component styles</p></div>
    
    
    <div class='column is-5 has-text-left'>
        <label>Type: </label>
        
<code class="param-type">Object</code>


    </div>
    
</div>

                
        </div>
    </div>




</div>
            
                <div class="member">


    
    <h4 class="name" id="useLogout">
        <a class="href-link" href="#useLogout">#</a>
        
        <span class="code-name">
            
                useLogout<span class="signature">()</span><span class="type-signature"> &rarr; {<a href="UseLogoutReturn.html">UseLogoutReturn</a>}</span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        <p>Custom hook to handle user logout and account deletion modal logic.</p>
<p>Provides methods to trigger logout, manage modal state, and clear all user-related data.</p>
    </div>
    













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="hooks_useLogout.ts.html" class="button">View Source</a>
            <span>
                <a href="hooks_useLogout.ts.html">hooks/useLogout.ts</a>, <a href="hooks_useLogout.ts.html#line5">line 5</a>
            </span>
        </p>
    
</dl>


















    <div class='columns method-parameter'>
        <div class="column is-2"><label>Returns:</label></div>
        <div class="column is-10">
            
                    

<div class="columns">
    
    <div class='param-desc column is-7'><p>Hook API for logout and modal management.</p></div>
    
    
    <div class='column is-5 has-text-left'>
        <label>Type: </label>
        
<code class="param-type"><a href="UseLogoutReturn.html">UseLogoutReturn</a></code>


    </div>
    
</div>

                
        </div>
    </div>




</div>
            
            </div>
        </div>
    

    
        <div class='vertical-section'>
            <h1>Type Definitions</h1>
            <div class="members">
            
                <div class="member">

    <span class="method-parameter is-pulled-right">
        <label>Type:</label>
        
<code class="param-type">Object</code>


    </span>

<h4 class="name" id="DrawerParamList">
    <a class="href-link" href="#DrawerParamList">#</a>
    
    <span class="code-name">
        DrawerParamList
    </span>
    
</h4>




<div class="description">
    <p>Type definition for all possible drawer navigation screens and their parameters</p>
</div>





    <h5 class="subsection-title">Properties:</h5>

    
<div class="table-container">
    <table class="props table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        
            
<tr class="deep-level-0">
    
        <td class="name"><code>welcome</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>OrderReserveEquipment</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>InvitePlayers</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>CoachOptions</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>getCertified</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>MessageNotificationPreferences</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>Settings</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>EditProfile</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>MainTabs</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>PostAnAd</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>ManageContent</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>ManageClasses</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>ManageServiceRequest</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>ManagePlayerAssets</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>ManageCommunity</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>Orders</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>Help</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>Rewards</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>RecycleBalls</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>ProfileQrScreen</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>DateScreen</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>EquipmentReservationScreen</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>RacquetCategory</code></td>
    

    <td class="type">
    
        
<code class="param-type">object</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

  
      
<tr class="deep-level-1">
    
        <td class="name"><code>sportsTitle</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

  

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>RacquetBrands</code></td>
    

    <td class="type">
    
        
<code class="param-type">object</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

  
      
<tr class="deep-level-1">
    
        <td class="name"><code>sportsTitle</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

  
      
<tr class="deep-level-1">
    
        <td class="name"><code>category</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

  

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>RacquetSelector</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>RacquetSelectorDetail</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>PlayerConnectScreen</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>GoEats</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>GoTravel</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>GetCertified</code></td>
    

    <td class="type">
    
        
<code class="param-type">undefined</code>


    
    </td>

    

    

    <td class="description last"></td>
</tr>

        
        </tbody>
    </table>
</div>



<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="navigation_DrawerNavigator.tsx.html" class="button">View Source</a>
            <span>
                <a href="navigation_DrawerNavigator.tsx.html">navigation/DrawerNavigator.tsx</a>, <a href="navigation_DrawerNavigator.tsx.html#line391">line 391</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

    <span class="method-parameter is-pulled-right">
        <label>Type:</label>
        
<code class="param-type">Object</code>


    </span>

<h4 class="name" id="FormData">
    <a class="href-link" href="#FormData">#</a>
    
    <span class="code-name">
        FormData
    </span>
    
</h4>




<div class="description">
    <p>Form data interface</p>
</div>





    <h5 class="subsection-title">Properties:</h5>

    
<div class="table-container">
    <table class="props table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            
            <th>Attributes</th>
            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        
            
<tr class="deep-level-0">
    
        <td class="name"><code>email</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    
        <td class="attributes">
        

        
        </td>
    

    

    <td class="description last"><p>User's email address</p></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>password</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    
        <td class="attributes">
        
            &lt;optional><br>
        

        
        </td>
    

    

    <td class="description last"><p>New password (required after OTP verification)</p></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>confirmPassword</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    
        <td class="attributes">
        
            &lt;optional><br>
        

        
        </td>
    

    

    <td class="description last"><p>Confirm new password (required after OTP verification)</p></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>code</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    
        <td class="attributes">
        
            &lt;optional><br>
        

        
        </td>
    

    

    <td class="description last"><p>6-digit OTP code (required after OTP verification)</p></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>email</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    
        <td class="attributes">
        

        
        </td>
    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>password</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    
        <td class="attributes">
        
            &lt;optional><br>
        

        
        </td>
    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>confirmPassword</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    
        <td class="attributes">
        
            &lt;optional><br>
        

        
        </td>
    

    

    <td class="description last"></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>code</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    
        <td class="attributes">
        
            &lt;optional><br>
        

        
        </td>
    

    

    <td class="description last"></td>
</tr>

        
        </tbody>
    </table>
</div>



<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="screens_auth_ForgotPasswordScreen.tsx.html" class="button">View Source</a>
            <span>
                <a href="screens_auth_ForgotPasswordScreen.tsx.html">screens/auth/ForgotPasswordScreen.tsx</a>, <a href="screens_auth_ForgotPasswordScreen.tsx.html#line355">line 355</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

    <span class="method-parameter is-pulled-right">
        <label>Type:</label>
        
<code class="param-type">Object</code>


    </span>

<h4 class="name" id="FormData">
    <a class="href-link" href="#FormData">#</a>
    
    <span class="code-name">
        FormData
    </span>
    
</h4>








    <h5 class="subsection-title">Properties:</h5>

    
<div class="table-container">
    <table class="props table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        
            
<tr class="deep-level-0">
    
        <td class="name"><code>email</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    

    

    <td class="description last"><p>User's email address</p></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>password</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    

    

    <td class="description last"><p>User's password</p></td>
</tr>

        
        </tbody>
    </table>
</div>



<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="screens_auth_Login_index.tsx.html" class="button">View Source</a>
            <span>
                <a href="screens_auth_Login_index.tsx.html">screens/auth/Login/index.tsx</a>, <a href="screens_auth_Login_index.tsx.html#line18">line 18</a>
            </span>
        </p>
    
</dl>





</div>
            
                <div class="member">

    <span class="method-parameter is-pulled-right">
        <label>Type:</label>
        
<code class="param-type">Object</code>


    </span>

<h4 class="name" id="SubscriptionProps">
    <a class="href-link" href="#SubscriptionProps">#</a>
    
    <span class="code-name">
        SubscriptionProps
    </span>
    
</h4>








    <h5 class="subsection-title">Properties:</h5>

    
<div class="table-container">
    <table class="props table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        
            
<tr class="deep-level-0">
    
        <td class="name"><code>onAccept</code></td>
    

    <td class="type">
    
        
<code class="param-type">function</code>


    
    </td>

    

    

    <td class="description last"><p>Callback which is invoked when user accept the offer.</p></td>
</tr>

        
        </tbody>
    </table>
</div>



<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="components_Subscription_index.tsx.html" class="button">View Source</a>
            <span>
                <a href="components_Subscription_index.tsx.html">components/Subscription/index.tsx</a>, <a href="components_Subscription_index.tsx.html#line10">line 10</a>
            </span>
        </p>
    
</dl>





</div>
            
            </div>
        </div>
    

    
</article>

</section>




            </div>
            
            <!-- Footer removed via patch -->
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

<script src="scripts/search.js"> </script>


</body>
</html>