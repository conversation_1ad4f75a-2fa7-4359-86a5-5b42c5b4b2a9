import React, {useState, useRef, useMemo, useCallback, useEffect} from 'react';
import {View, TouchableOpacity, ActivityIndicator} from 'react-native';
import {styles as createStyles} from './styles';
import {useThemeStore} from '@/store/themeStore';
import {
  CButton,
  CInput,
  CustomModal,
  Header,
  Icon,
  NoData,
  RadioSelect,
  SafeAreaView,
} from '@/components';
import {DrawerActions, useNavigation, useFocusEffect} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation';
import Typography from '@/components/Typography';
import GroupCard from '@/components/GroupCard';
import useTranslation from '@/hooks/useTranslation';
import {getMyGroups, toaster} from '@/utils/commonFunctions';
import {useInfiniteQuery, useQueryClient} from '@tanstack/react-query';
import CLoader from '@/components/CLoader';
import {SwipeListView} from 'react-native-swipe-list-view';
import DeleteAccountModal, {DeleteAccountModalHandles} from '@/components/DeleteAccountModal';
import {useJoinOrLeaveGroup, useManageFavoriteGroup} from '@/hooks/queries/groups';
import {useSendbirdChat} from '@sendbird/uikit-react-native';
import {useGroupChannel} from '@sendbird/uikit-chat-hooks';
import {groupTypeOptions, predefinedTags} from '@/config/staticData';
import RefreshControl from '@/components/RefreshControl';
import {Icons} from '@/config/icons';
import debounce from 'lodash/debounce';
import CIcon from '@/components/CIcon';
import {getMessaging} from '@react-native-firebase/messaging';
import {getApp} from '@react-native-firebase/app';

type NavigationProp = StackNavigationProp<RootStackParamList>;

interface GroupsData {
  id: string;
  group_name: string;
  total_members: number;
  highlighted: boolean;
  type: string;
  locked: boolean;
  group_image: string;
  group_type: string;
  channel_url: string;
  is_favorite_group: boolean;
  unread_count: number;
}

interface ApiResponse {
  status: boolean;
  data: GroupsData[];
  pagination: {
    total: number;
    totalPages: number;
    currentPage: number;
    perPage: number;
  };
}

interface TabData {
  id: number;
  label: string;
  value: string;
}

const GroupCardWrapper = ({item, navigation}: {item: GroupsData; navigation: any}) => {
  const channelUrl = item?.channel_url;
  const group_id = item?.id;
  const {sdk} = useSendbirdChat();
  const {channel} = useGroupChannel(sdk, channelUrl);
  const theme = useThemeStore();

  return (
    <GroupCard
      name={item.group_name}
      members={item.total_members}
      highlighted={item.highlighted}
      locked={item?.group_type === 'private'}
      unReadCount={item?.unread_count}
      containerStyle={{
        borderWidth: 1,
        borderColor: theme.colors.divider,
        marginVertical: 8,
        backgroundColor: theme.colors.background,
      }}
      showMore={true}
      onMorePress={() => {
        navigation.navigate('JoinGroupDetails', {id: item.id, from: 'my-groups'});
      }}
      image={item.group_image}
      onPress={() => {
        if (channel) {
          navigation.navigate('SendBird', {
            channelUrl: channelUrl,
            group_id,
          });
        } else {
          toaster('error', 'User is not connected with Sendbird', 'top');
        }
      }}
    />
  );
};

const MyGroups = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);

  const navigation = useNavigation<NavigationProp>();
  const deleteModalRef = useRef<DeleteAccountModalHandles>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const [groupType, setGroupType] = useState<TabData>(groupTypeOptions[0]);

  const {t} = useTranslation();
  const [openRowKey, setOpenRowKey] = useState<string | null>(null);
  const [isFavoriteLoading, setIsFavoriteLoading] = useState<string | null>(null);

  const joinOrLeaveMutation = useJoinOrLeaveGroup();
  const manageFavoriteGroup = useManageFavoriteGroup();

  const swipeListRef = useRef<SwipeListView<any>>(null);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedValues, setSelectedValues] = React.useState<string[]>([]);
  const [appliedFilters, setAppliedFilters] = useState<string[]>([]);
  const [inputValue, setInputValue] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');

  const queryClient = useQueryClient();

  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchQuery(value);
      }, 500),
    [],
  );

  const handleSearchChange = useCallback(
    (value: string) => {
      setInputValue(value);
      debouncedSearch(value);
    },
    [debouncedSearch],
  );

  const filterSelectedVal = appliedFilters.join(',');

  const {data, isLoading, refetch, fetchNextPage, hasNextPage, isFetchingNextPage, isRefetching} =
    useInfiniteQuery<ApiResponse>({
      queryKey: ['my-groups', groupType, filterSelectedVal, searchQuery],
      refetchOnMount: true,
      gcTime: 0,
      queryFn: async ({pageParam = 1}) => {
        const response = await getMyGroups(
          groupType?.value,
          pageParam as number,
          searchQuery,
          filterSelectedVal,
        );
        return (
          response || {data: [], pagination: {total: 0, totalPages: 0, currentPage: 1, perPage: 10}}
        );
      },
      getNextPageParam: lastPage => {
        if (lastPage.pagination.currentPage < lastPage.pagination.totalPages) {
          return lastPage.pagination.currentPage + 1;
        }
        return undefined;
      },
      initialPageParam: 1,
    });

  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch]),
  );

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const renderComponent = ({item}: {item: GroupsData}) => {
    return <GroupCardWrapper item={item} navigation={navigation} />;
  };

  const updateBadgeCount = useCallback(
    (data: any) => {
      if (!data?.group_id) return;
      queryClient.setQueryData(
        ['my-groups', groupType, filterSelectedVal, searchQuery],
        (oldData: any) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            pages: oldData.pages.map((page: any) => ({
              ...page,
              data: page.data.map((group: GroupsData) =>
                group.id === data.group_id
                  ? {...group, unread_count: data.unread_count || 0}
                  : group,
              ),
            })),
          };
        },
      );
    },
    [queryClient, groupType, filterSelectedVal, searchQuery],
  );

  useEffect(() => {
    const app = getApp();
    const messaging = getMessaging(app);
    const unsubscribe = messaging.onMessage(async remoteMessage => {
      updateBadgeCount(remoteMessage?.data);
    });
    return unsubscribe;
  }, [updateBadgeCount]);

  const renderHeader = () => (
    <View style={styles.tabContainer}>
      <View style={styles.privacyRow}>
        {groupTypeOptions.map(option => (
          <TouchableOpacity
            activeOpacity={0.7}
            key={option.id}
            style={[
              styles.privacyButton,
              groupType.value === option.value && styles.privacyButtonSelected,
            ]}
            onPress={() => {
              setGroupType(option);
            }}>
            <Typography
              variant="tryNow"
              style={[
                groupType.value === option.value
                  ? styles.privacyButtonTextSelected
                  : styles.privacyButtonText,
              ]}>
              {option.label}
            </Typography>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const toggleSelection = (value: string) => {
    setSelectedValues(prev =>
      prev.includes(value) ? prev.filter(v => v !== value) : [...prev, value],
    );
  };

  const myGroupData = useMemo(() => {
    return data?.pages.flatMap(page => page.data) || [];
  }, [data?.pages]);

  const handleDeleteCancel = () => {
    deleteModalRef.current?.close();
  };

  const handleDeleteConfirm = async () => {
    if (!openRowKey) return;

    try {
      setIsDeleting(true);
      await joinOrLeaveMutation.mutateAsync({groupId: openRowKey, refetch: true});
      deleteModalRef.current?.close();
      swipeListRef.current?.closeAllOpenRows();
      setOpenRowKey(null);
      refetch();
    } catch (error) {
      console.error('Error leaving group:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleFavorite = async (groupId: string) => {
    try {
      setIsFavoriteLoading(groupId);
      await manageFavoriteGroup.mutateAsync(groupId);
      refetch();
      swipeListRef.current?.closeAllOpenRows();
      setOpenRowKey(null);
    } catch (error) {
      console.error('Error leaving group:', error);
    } finally {
      setIsFavoriteLoading(null);
    }
  };

  const renderHiddenItem = (data: {item: GroupsData}) => (
    <View style={styles.rowBack}>
      {/* Favorite Button */}
      <TouchableOpacity
        style={[styles.actionButton, styles.favoriteBtn]}
        onPress={() => {
          if (isFavoriteLoading === data.item.id) return;
          handleFavorite(data.item.id);
        }}>
        {isFavoriteLoading === data.item.id ? (
          <ActivityIndicator size="small" color={theme.colors.primary} />
        ) : (
          <>
            <Icons.AntDesign
              name={data?.item?.is_favorite_group ? 'star' : 'staro'}
              size={28}
              color={theme.colors.orange}
            />
            <Typography variant="communityBtnText" style={styles.actionText}>
              Favorite
            </Typography>
          </>
        )}
      </TouchableOpacity>

      {/* Leave Button */}
      <TouchableOpacity
        style={[styles.actionButton, styles.moreBtn]}
        onPress={() => {
          deleteModalRef.current?.open();
        }}>
        <Icon name="delete" size={28} color={theme.colors.white} />
        <Typography variant="communityBtnText" style={styles.actionText}>
          {t('myGroupsScreen.leave')}
        </Typography>
      </TouchableOpacity>
    </View>
  );

  const ListFooterComponent = useMemo(() => {
    return hasNextPage || isFetchingNextPage ? (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    ) : null;
  }, [hasNextPage, isFetchingNextPage, theme.colors.primary]);

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage]);
  return (
    <SafeAreaView includeBottom={false} style={styles.root}>
      <Header
        leftIcon={{
          name: 'side-menu',
          size: 24,
          color: theme.colors.white,
          containerStyle: theme.colors.primary,
        }}
        leftIconButtonStyle={styles.menuButton}
        onLeftPress={openDrawer}
        rightIcons={[
          {name: 'notification', size: 24, badge: 0},
          {name: 'chat', size: 24, badge: 14},
        ]}
        pageTitle={t('myGroupsScreen.title')}
        backgroundColor="transparent"
      />
      <View style={styles.headerContainer}>{renderHeader()}</View>

      <View style={styles.searchContainer}>
        <View style={styles.flex}>
          <CInput
            inputStyle={styles.searchInput}
            placeholder={t('joinGroupsScreen.searchPlaceholder')}
            placeholderTextColorStyle={theme.colors.secondary}
            value={inputValue}
            onChangeText={handleSearchChange}
          />
        </View>
        <TouchableOpacity onPress={() => setIsModalVisible(true)}>
          <CIcon name="filter" size={28} color={theme.colors.white} />
        </TouchableOpacity>
      </View>

      {isLoading ? (
        <CLoader />
      ) : (
        <SwipeListView
          ref={swipeListRef}
          data={myGroupData}
          renderItem={renderComponent}
          renderHiddenItem={renderHiddenItem}
          rightOpenValue={-160}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.content}
          closeOnRowPress={true}
          disableRightSwipe={true}
          showsVerticalScrollIndicator={false}
          friction={100}
          tension={100}
          swipeToOpenPercent={15}
          onEndReachedThreshold={0.5}
          onEndReached={handleEndReached}
          refreshControl={<RefreshControl refreshing={isRefetching} onRefresh={() => refetch()} />}
          onRowOpen={rowKey => {
            setOpenRowKey(rowKey);
          }}
          onRowClose={() => {
            setOpenRowKey(null);
          }}
          ListEmptyComponent={
            <NoData
              title={t('myGroupsScreen.noGroups')}
              message={t('myGroupsScreen.noGroupsMessage')}
            />
          }
          ListFooterComponent={ListFooterComponent}
          removeClippedSubviews={false}
          maxToRenderPerBatch={20}
          initialNumToRender={20}
          windowSize={5}
        />
      )}

      <CustomModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        variant="bottom"
        showCloseButton={true}
        title="Filter results">
        <View>
          <Typography variant="subtitle" style={{color: theme.colors.offWhite}}>
            {t('addMembersScreen.show')}
          </Typography>
          {predefinedTags.map(option => (
            <RadioSelect
              key={option}
              label={option}
              selected={selectedValues.includes(option)}
              onPress={() => toggleSelection(option)}
            />
          ))}

          <View style={styles.buttonContainer}>
            <CButton
              title={t('addMembersScreen.results')}
              onPress={() => {
                setAppliedFilters(selectedValues);
                setIsModalVisible(false);
                refetch();
              }}
              variant="primary"
              containerStyle={styles.resultBtn}
            />
            <CButton
              title={t('common.clearFilters')}
              onPress={() => {
                setAppliedFilters([]);
                setSelectedValues([]);
                if (appliedFilters.length > 0 && selectedValues.length > 0) {
                  refetch();
                }
                setIsModalVisible(false);
              }}
              variant="outline"
              containerStyle={styles.clearBtn}
            />
          </View>
        </View>
      </CustomModal>

      <DeleteAccountModal
        ref={deleteModalRef}
        type="logout"
        onCancel={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        loader={isDeleting}
        message={t('myGroupsScreen.leaveConfirmation')}
        confirmButtonText={t('myGroupsScreen.leave')}
      />
    </SafeAreaView>
  );
};

export default MyGroups;
