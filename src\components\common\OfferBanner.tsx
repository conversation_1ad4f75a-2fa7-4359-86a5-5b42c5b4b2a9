import React from 'react';
import {View, StyleSheet} from 'react-native';
import Typography from '../Typography';
import {useThemeStore} from '@/store/themeStore';

interface OfferBannerProps {
  text: string;
  style?: any;
}

/**
 * @component
 * @category Components
 * @description
 * A component that renders a centered, styled text block with a slight margin,
 * intended for use in displaying promotional offers, etc.
 *
 * @param text The text to display
 * @param style Optional additional styles
 * @returns A styled text block
 */
export const OfferBanner: React.FC<OfferBannerProps> = ({text, style}) => {
  const theme = useThemeStore();

  return (
    <View style={[styles(theme).footer, style]}>
      <Typography variant="body" style={styles(theme).footerText} align="center">
        {text}
      </Typography>
    </View>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    footer: {
      padding: 4,
      backgroundColor: theme.colors.primary,
      borderRadius: 0,
      marginHorizontal: 0,
      marginVertical: 0,
    },
    footerText: {
      color: theme.colors.white,
    },
  });
