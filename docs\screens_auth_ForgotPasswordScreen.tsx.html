

<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> screens/auth/ForgotPasswordScreen.tsx</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="styles/style.css">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
             
                <a href="index.html">
                    <h1 class="navbar-item">GoRaqt Documentation</h1>
                </a>
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    Documentation
                </a>
                
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
                <div class="search-wrapper">
                    <input id="search" type="text" placeholder="Search docs..." class="input">
                </div>
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Global</h3><ul><li><a href="global.html#HIDE_TAB_SCREENS">HIDE_TAB_SCREENS</a></li><li><a href="global.html#SENSITIVE_FIELDS">SENSITIVE_FIELDS</a></li><li><a href="global.html#addBreadcrumb">addBreadcrumb</a></li><li><a href="global.html#apiCall">apiCall</a></li><li><a href="global.html#authenticateWithBiometrics">authenticateWithBiometrics</a></li><li><a href="global.html#calculateAge">calculateAge</a></li><li><a href="global.html#checkAuthStatusApi">checkAuthStatusApi</a></li><li><a href="global.html#checkLocationPermission">checkLocationPermission</a></li><li><a href="global.html#cleanMemoryIfNeeded">cleanMemoryIfNeeded</a></li><li><a href="global.html#clearAllData">clearAllData</a></li><li><a href="global.html#configureStatusBar">configureStatusBar</a></li><li><a href="global.html#createHealthService">createHealthService</a></li><li><a href="global.html#deleteFcmTokenFromDB">deleteFcmTokenFromDB</a></li><li><a href="global.html#deleteNotification">deleteNotification</a></li><li><a href="global.html#disableBiometrics">disableBiometrics</a></li><li><a href="global.html#enableBiometrics">enableBiometrics</a></li><li><a href="global.html#forgotPasswordApi">forgotPasswordApi</a></li><li><a href="global.html#formatBirthdate">formatBirthdate</a></li><li><a href="global.html#getAddressFromCoordinates">getAddressFromCoordinates</a></li><li><a href="global.html#getCurrentLocationWithAddress">getCurrentLocationWithAddress</a></li><li><a href="global.html#getCurrentPosition">getCurrentPosition</a></li><li><a href="global.html#getCurrentRegion">getCurrentRegion</a></li><li><a href="global.html#getCurrentUserApi">getCurrentUserApi</a></li><li><a href="global.html#getNotificationsList">getNotificationsList</a></li><li><a href="global.html#getTabBarVisibility">getTabBarVisibility</a></li><li><a href="global.html#getUnreadNotificationsCount">getUnreadNotificationsCount</a></li><li><a href="global.html#getUserDetails">getUserDetails</a></li><li><a href="global.html#getUserProfile">getUserProfile</a></li><li><a href="global.html#handleNotificationNavigation">handleNotificationNavigation</a></li><li><a href="global.html#handleTextureError">handleTextureError</a></li><li><a href="global.html#initSentry">initSentry</a></li><li><a href="global.html#isBiometricsAvailable">isBiometricsAvailable</a></li><li><a href="global.html#isBiometricsEnabled">isBiometricsEnabled</a></li><li><a href="global.html#lastCacheClearTime">lastCacheClearTime</a></li><li><a href="global.html#logError">logError</a></li><li><a href="global.html#logMessage">logMessage</a></li><li><a href="global.html#loginWithEmail">loginWithEmail</a></li><li><a href="global.html#loginWithEmailApi">loginWithEmailApi</a></li><li><a href="global.html#logoutApi">logoutApi</a></li><li><a href="global.html#markAllNotificationsAsRead">markAllNotificationsAsRead</a></li><li><a href="global.html#markNotificationAsRead">markNotificationAsRead</a></li><li><a href="global.html#navigateBackFromExample">navigateBackFromExample</a></li><li><a href="global.html#navigateToOptimizedFlatListExample">navigateToOptimizedFlatListExample</a></li><li><a href="global.html#otpResendApi">otpResendApi</a></li><li><a href="global.html#otpVerifyApi">otpVerifyApi</a></li><li><a href="global.html#prefetchInitialQueries">prefetchInitialQueries</a></li><li><a href="global.html#prefetchPost">prefetchPost</a></li><li><a href="global.html#prefetchUser">prefetchUser</a></li><li><a href="global.html#refreshTokenApi">refreshTokenApi</a></li><li><a href="global.html#registerWithEmail">registerWithEmail</a></li><li><a href="global.html#registerWithEmailApi">registerWithEmailApi</a></li><li><a href="global.html#requestLocationPermission">requestLocationPermission</a></li><li><a href="global.html#resetPasswordApi">resetPasswordApi</a></li><li><a href="global.html#saveFcmTokenInDB">saveFcmTokenInDB</a></li><li><a href="global.html#schema">schema</a></li><li><a href="global.html#scrubObject">scrubObject</a></li><li><a href="global.html#scrubSensitiveData">scrubSensitiveData</a></li><li><a href="global.html#setCustomStatusBar">setCustomStatusBar</a></li><li><a href="global.html#setDarkStatusBar">setDarkStatusBar</a></li><li><a href="global.html#setLightStatusBar">setLightStatusBar</a></li><li><a href="global.html#setNavigationContext">setNavigationContext</a></li><li><a href="global.html#setStatusBarHidden">setStatusBarHidden</a></li><li><a href="global.html#setUserContext">setUserContext</a></li><li><a href="global.html#setupMemoryCleaning">setupMemoryCleaning</a></li><li><a href="global.html#setupTextureErrorHandlers">setupTextureErrorHandlers</a></li><li><a href="global.html#signInWithFacebook">signInWithFacebook</a></li><li><a href="global.html#signInWithGoogle">signInWithGoogle</a></li><li><a href="global.html#startTransaction">startTransaction</a></li><li><a href="global.html#styles">styles</a></li><li><a href="global.html#updateUserProfile">updateUserProfile</a></li><li><a href="global.html#useAuthStatus">useAuthStatus</a></li><li><a href="global.html#useCurrentUser">useCurrentUser</a></li><li><a href="global.html#useLogin">useLogin</a></li><li><a href="global.html#useRegister">useRegister</a></li><li><a href="global.html#useSafeArea">useSafeArea</a></li><li><a href="global.html#useSocialLogin">useSocialLogin</a></li><li><a href="global.html#useUpdateUserProfile">useUpdateUserProfile</a></li><li><a href="global.html#useUserProfile">useUserProfile</a></li></ul></div><div class="category"><h2>Components</h2><h3>Components</h3><ul><li><a href="Advertisement.html">Advertisement</a></li><li><a href="BiometricGate.html">BiometricGate</a></li><li><a href="CButton.html">CButton</a></li><li><a href="CreateGoRaqtProfile.html">CreateGoRaqtProfile</a></li><li><a href="CustomMapView.html">CustomMapView</a></li><li><a href="CustomMarker.html">CustomMarker</a></li><li><a href="NotificationCard.html">NotificationCard</a></li><li><a href="OfferBanner.html">OfferBanner</a></li><li><a href="Subscription.html">Subscription</a></li></ul></div><div class="category"><h2>Interfaces</h2><h3>Interfaces</h3><ul><li><a href="AdvertisementProps.html">AdvertisementProps</a></li><li><a href="ApiResponse.html">ApiResponse</a></li><li><a href="BiometricGateProps.html">BiometricGateProps</a></li><li><a href="CButtonProps.html">CButtonProps</a></li><li><a href="CreateProfileProps.html">CreateProfileProps</a></li><li><a href="CustomMarkerProps.html">CustomMarkerProps</a></li><li><a href="DrawerItemType.html">DrawerItemType</a></li><li><a href="ForgotPasswordScreenProps.html">ForgotPasswordScreenProps</a></li><li><a href="Notification.html">Notification</a></li><li><a href="NotificationCardProps.html">NotificationCardProps</a></li><li><a href="PaginationState.html">PaginationState</a></li><li><a href="PermissionsScreenProps.html">PermissionsScreenProps</a></li><li><a href="RouteParams.html">RouteParams</a></li><li><a href="SentryNavigationContainerProps.html">SentryNavigationContainerProps</a></li><li><a href="SocialRegisterRequest.html">SocialRegisterRequest</a></li><li><a href="SplashScreenProps.html">SplashScreenProps</a></li><li><a href="TermsOfServiceScreenProps.html">TermsOfServiceScreenProps</a></li><li><a href="UseLogoutReturn.html">UseLogoutReturn</a></li></ul><h3><a href="global.html">Global</a></h3></div><div class="category"><h2>Navigation</h2><h3>Components</h3><ul><li><a href="DrawerNavigator.html">DrawerNavigator</a></li><li><a href="SentryNavigationContainer.html">SentryNavigationContainer</a></li></ul></div><div class="category"><h2>Screens</h2><h3>Components</h3><ul><li><a href="CommunityDetails.html">CommunityDetails</a></li><li><a href="DirectFetchTest.html">DirectFetchTest</a></li><li><a href="EmailSignupScreen.html">EmailSignupScreen</a></li><li><a href="ForgotPasswordScreen.html">ForgotPasswordScreen</a></li><li><a href="LoginScreen.html">LoginScreen</a></li><li><a href="MyMatchesScreen.html">MyMatchesScreen</a></li><li><a href="NotificationsListScreen.html">NotificationsListScreen</a></li><li><a href="PermissionsScreen.html">PermissionsScreen</a></li><li><a href="SignupScreen.html">SignupScreen</a></li><li><a href="SplashScreen.html">SplashScreen</a></li><li><a href="TermsOfServiceScreen.html">TermsOfServiceScreen</a></li><li><a href="Verification.html">Verification</a></li></ul></div><div class="category"><h2>Utils</h2><h3>Global</h3><ul><li><a href="global.html#api">api</a></li><li><a href="global.html#socialLoginApi">socialLoginApi</a></li><li><a href="global.html#useLogout">useLogout</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Source</p>
                    <h1>screens/auth/ForgotPasswordScreen.tsx</h1>
                </header>
                



    
    <section>
        <article>
            <pre class="prettyprint source linenums"><code>import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation/index';
import {useThemeStore} from '@/store/themeStore';
import {useForm, Controller} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {CInput, Icon, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import {getGlobalStyles} from '@/utils';
import {useAuthStore} from '@/store';
import {useForgotPassword, useResendOtp, useResetPassword} from '@/hooks/queries/useAuth';
import {toaster, validatePassword} from '@/utils/commonFunctions';
import {OtpInput} from 'react-native-otp-entry';
import {OTP_SCREEN_TIMEOUT} from './Verification';
import useTranslation from '@/hooks/useTranslation';

/**
 * Navigation prop type for ForgotPassword screen
 * @category Interfaces
 * @typedef {Object} ForgotPasswordScreenNavigationProp
 * @property {function} navigate - Navigation function to move between screens
 * @property {function} goBack - Navigation function to go back
 */
type ForgotPasswordScreenNavigationProp = StackNavigationProp&lt;RootStackParamList, 'ForgotPassword'>;

// Form validation schema - moved before type definition
const createSchema = (t: any, isSuccess: boolean) =>
  yup.object({
    email: yup
      .string()
      .email(t('forgotPasswordScreen.emailFormatInvalid'))
      .required(t('forgotPasswordScreen.emailRequired')),
    password: isSuccess
      ? yup
          .string()
          .required(t('forgotPasswordScreen.passwordRequired'))
          .test(
            'password-validation',
            t('forgotPasswordScreen.passwordValidation'),
            value => !!value &amp;&amp; validatePassword(value).isValid,
          )
      : yup.string(),
    confirmPassword: isSuccess
      ? yup
          .string()
          .oneOf([yup.ref('password')], t('forgotPasswordScreen.passwordsMustMatch'))
          .required(t('forgotPasswordScreen.pleaseConfirmYourPassword'))
      : yup.string(),
    code: isSuccess
      ? yup
          .string()
          .length(6, t('forgotPasswordScreen.codeMustBe6Digits'))
          .required(t('forgotPasswordScreen.codeRequired'))
      : yup.string(),
  });

/**
 * Form data interface
 * @category Interfaces
 * @typedef {Object} FormData
 * @property {string} email - User's email address
 * @property {string} [password] - New password (required after OTP verification)
 * @property {string} [confirmPassword] - Confirm new password (required after OTP verification)
 * @property {string} [code] - 6-digit OTP code (required after OTP verification)
 */
type FormData = {
  email: string;
  password?: string;
  confirmPassword?: string;
  code?: string;
};

/**
 * Route parameters interface
 * @category Interfaces
 * @typedef {Object} RouteParams
 * @property {string} [email] - Pre-filled email address
 */
interface RouteParams {
  email?: string;
}

/**
 * Component props interface
 * @category Interfaces
 * @typedef {Object} ForgotPasswordScreenProps
 * @property {Object} route - Navigation route object
 * @property {RouteParams} route.params - Route parameters
 */
interface ForgotPasswordScreenProps {
  route: {
    params: RouteParams;
  };
}

/**
 * Forgot Password Screen Component
 *
 * Handles both email submission for password reset and OTP + new password flow.
 * The flow works in two steps:
 * - Step 1: User submits email to request OTP
 * - Step 2: After OTP success, user sets a new password
 *
 * @component
 * @category Screens
 * @param {ForgotPasswordScreenProps} props - Component props containing navigation route
 * @returns {JSX.Element} Forgot Password screen layout
 *
 * @example
 * // Navigation to this screen
 * navigation.navigate('ForgotPassword', { email: '<EMAIL>' });
 *
 * @example
 * // Navigation without pre-filled email
 * navigation.navigate('ForgotPassword');
 */
const ForgotPasswordScreen: React.FC&lt;ForgotPasswordScreenProps> = ({route}) => {
  const email = route?.params?.email ?? '';
  const navigation = useNavigation&lt;ForgotPasswordScreenNavigationProp>();
  const theme = useThemeStore();
  const {t} = useTranslation();

  const emailInputRef = useRef&lt;TextInput>(null);
  const passwordInputRef = useRef&lt;TextInput>(null);
  const confirmPasswordInputRef = useRef&lt;TextInput>(null);
  const otpRef = useRef&lt;any>(null);

  const globalStyles = getGlobalStyles({theme});
  const {isApiStatus} = useAuthStore();
  const forgotPasswordMutation = useForgotPassword();
  const resetPasswordApi = useResetPassword();
  const resendOtpApi = useResendOtp();

  const [isSuccess, setIsSuccess] = React.useState&lt;boolean>(false);
  const mutation = isSuccess ? resetPasswordApi : forgotPasswordMutation;

  const [timer, setTimer] = useState(OTP_SCREEN_TIMEOUT);
  const [isResendDisabled, setIsResendDisabled] = useState(true);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (isResendDisabled) {
      interval = setInterval(() => {
        setTimer(prev => {
          if (prev &lt;= 1) {
            clearInterval(interval!);
            setIsResendDisabled(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isResendDisabled]);

  const schema = createSchema(t, isSuccess);

  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm&lt;FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      email: email || '',
      password: '',
    },
  });

  /**
   * Handles back navigation
   * @function handleBack
   * @memberof ForgotPasswordScreen
   * @description Handle back navigation
   */
  const handleBack = (): void => {
    navigation.goBack();
  };

  /**
   * @function onSubmit
   * @memberof ForgotPasswordScreen
   * @description Handles form submission and forgot password process
   * @param {FormData} _data - Form data containing email, password and OTP code
   */
  const onSubmit = (_data: FormData): void => {
    const payload = isSuccess
      ? {email: _data.email, password: _data.password, code: _data.code}
      : {email: _data.email};

    if (isApiStatus) {
      mutation.mutate(payload as any, {
        onSuccess: response => {
          if (response?.status) {
            if (isSuccess) {
              toaster('success', response.message, 'top');
              navigation.goBack();
            } else {
              setIsSuccess(true);
            }
          } else {
            toaster('error', response.message, 'top');
          }
        },
        onError: error => {
          toaster('error', error.message, 'top');
        },
      });
    } else {
      navigation.navigate('Verification', {
        email: _data.email,
      });
    }
  };
  /**
   * @function resendOtp
   * @memberof ForgotPasswordScreen
   * @description Handles resend OTP after OTP expired
   */
  const resendOtp = (): void => {
    const emailStr = control._formValues.email;

    if (isApiStatus) {
      resendOtpApi.mutate(
        {email: emailStr},
        {
          onSuccess: response => {
            if (response?.status) {
              setIsResendDisabled(true);
              setTimer(OTP_SCREEN_TIMEOUT);
              toaster('success', response?.message, 'top');
            } else {
              toaster('error', response?.message, 'top');
            }
          },
          onError: error => {
            toaster('error', error.message, 'top');
          },
        },
      );
    }
  };

  /**
   * Formats seconds into MM:SS format
   * @function
   * @param {number} seconds - Number of seconds to format
   * @returns {string} Formatted time string in MM:SS format
   */
  const formatTime = (seconds: number): string => {
    const m = Math.floor(seconds / 60);
    const s = seconds % 60;
    return `${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
  };

  return (
    &lt;SafeAreaView
      includeTop
      style={[styles(theme).container, {backgroundColor: theme.colors.background}]}>
      &lt;KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles(theme).keyboardAvoidingView}>
        &lt;ScrollView
          contentContainerStyle={styles(theme).scrollContainer}
          keyboardShouldPersistTaps="handled">
          &lt;View style={styles(theme).headerContainer}>
            &lt;TouchableOpacity
              onPress={handleBack}
              style={styles(theme).backButton}
              activeOpacity={0.7}
              hitSlop={{top: 10, right: 10, bottom: 10, left: 10}}>
              &lt;Icon name="Left-chevron" size={22} color={theme.colors.gray} />
            &lt;/TouchableOpacity>
            &lt;Typography variant="subtitle" style={globalStyles.title}>
              {t('forgotPasswordScreen.title')}
            &lt;/Typography>
          &lt;/View>

          &lt;View style={styles(theme).form}>
            &lt;Text style={styles(theme).instructions}>{t('forgotPasswordScreen.instructions')}&lt;/Text>

            &lt;View style={styles(theme).inputContainer}>
              &lt;Controller
                control={control}
                name="email"
                render={({field: {onChange, onBlur, value}}) => (
                  &lt;CInput
                    label={t('forgotPasswordScreen.email')}
                    showLabel={true}
                    variant="dark"
                    placeholder={t('forgotPasswordScreen.emailPlaceholder')}
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    hasError={!!errors.email}
                    error={errors.email?.message}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    inputStyle={styles(theme).input}
                    containerStyle={{marginBottom: 0}}
                    returnKeyType="done"
                    onSubmitEditing={handleSubmit(onSubmit)}
                    blurOnSubmit={false}
                    ref={emailInputRef}
                  />
                )}
              />
            &lt;/View>
            {isSuccess &amp;&amp; (
              &lt;>
                &lt;View style={styles(theme).inputContainer}>
                  &lt;Controller
                    control={control}
                    name="password"
                    render={({field: {onChange, onBlur, value}}) => {
                      const validation = validatePassword(value || '');

                      const errorMessage =
                        errors.password?.message ||
                        (value &amp;&amp; value.length > 0 &amp;&amp; !validation.isValid
                          ? validation.message
                          : '');

                      return (
                        &lt;CInput
                          label={t('forgotPasswordScreen.newPassword')}
                          showLabel={true}
                          variant="dark"
                          placeholder={t('forgotPasswordScreen.newPasswordPlaceholder')}
                          value={value}
                          onChangeText={onChange}
                          onBlur={onBlur}
                          hasError={
                            !!errors.password || (value &amp;&amp; value.length > 0 &amp;&amp; !validation.isValid)
                          }
                          error={errorMessage}
                          secureTextEntry
                          inputStyle={styles(theme).input}
                          containerStyle={{marginBottom: 0}}
                          ref={passwordInputRef}
                          returnKeyType="done"
                          onSubmitEditing={() => confirmPasswordInputRef.current?.focus()}
                        />
                      );
                    }}
                  />
                &lt;/View>
                &lt;View style={styles(theme).inputContainer}>
                  &lt;Controller
                    control={control}
                    name="confirmPassword"
                    render={({field: {onChange, onBlur, value}}) => (
                      &lt;CInput
                        label={t('forgotPasswordScreen.confirmPassword')}
                        showLabel={true}
                        variant="dark"
                        placeholder={t('forgotPasswordScreen.confirmPasswordPlaceholder')}
                        value={value}
                        onChangeText={onChange}
                        onBlur={onBlur}
                        hasError={!!errors.confirmPassword}
                        error={errors.confirmPassword?.message}
                        secureTextEntry
                        inputStyle={styles(theme).input}
                        containerStyle={{marginBottom: 0}}
                        ref={confirmPasswordInputRef}
                        returnKeyType="done"
                        blurOnSubmit={false}
                        onSubmitEditing={() => otpRef.current?.focus()}
                      />
                    )}
                  />
                &lt;/View>
                &lt;View style={styles(theme).inputContainer}>
                  &lt;Typography style={styles(theme).otpLabel}>
                    {t('forgotPasswordScreen.code')}
                  &lt;/Typography>
                  &lt;Controller
                    control={control}
                    name="code"
                    render={({field: {onChange, value}}) => (
                      &lt;OtpInput
                        ref={otpRef}
                        numberOfDigits={6}
                        autoFocus={true}
                        focusColor={theme.colors.activeColor}
                        blurOnFilled={true}
                        disabled={false}
                        type="numeric"
                        secureTextEntry={false}
                        focusStickBlinkingDuration={500}
                        onTextChange={onChange}
                        onFilled={handleSubmit(onSubmit)}
                        theme={{
                          pinCodeContainerStyle: {
                            borderColor: errors.code
                              ? theme.colors.coralRed
                              : theme.colors.activeColor,
                            width: 50,
                          },
                          pinCodeTextStyle: {color: theme.colors.activeColor},
                        }}
                      />
                    )}
                  />
                  {errors.code &amp;&amp; (
                    &lt;Typography variant="body" style={styles(theme).errorText}>
                      {errors.code.message}
                    &lt;/Typography>
                  )}
                &lt;/View>

                {/* Resend */}
                &lt;TouchableOpacity
                  onPress={() => {
                    if (timer === 0) resendOtp();
                  }}
                  disabled={isResendDisabled}
                  style={styles(theme).resendContainer}>
                  &lt;Text
                    style={{
                      color: isResendDisabled ? theme.colors.white2 : theme.colors.primary,
                      fontSize: theme.fontSize.medium,
                    }}>
                    {isResendDisabled
                      ? `${t('forgotPasswordScreen.resendCodeIn')} ${formatTime(timer)}`
                      : t('forgotPasswordScreen.resendCode')}
                  &lt;/Text>
                &lt;/TouchableOpacity>
              &lt;/>
            )}

            &lt;TouchableOpacity
              style={[
                styles(theme).resetButton,
                {backgroundColor: theme.colors.primary},
                mutation.isPending &amp;&amp; styles(theme).disabledButton,
              ]}
              onPress={handleSubmit(onSubmit)}
              disabled={mutation.isPending}
              activeOpacity={0.7}>
              &lt;Text style={styles(theme).resetButtonText}>
                {mutation.isPending
                  ? t('forgotPasswordScreen.sending')
                  : t('forgotPasswordScreen.resetPassword')}
              &lt;/Text>
            &lt;/TouchableOpacity>
          &lt;/View>
        &lt;/ScrollView>
      &lt;/KeyboardAvoidingView>
    &lt;/SafeAreaView>
  );
};

/**
 * Stylesheet factory function
 * @function
 * @param {any} theme - Theme object containing colors and font sizes
 * @returns {Object} StyleSheet object with component styles
 */
const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingHorizontal: 20,
    },
    headerContainer: {
      flexDirection: 'row',
      marginBottom: 30,
      position: 'relative',
      alignItems: 'center',
      justifyContent: 'flex-start',
      paddingTop: 20,
      gap: 10,
    },
    backButton: {
      padding: 5,
    },
    title: {
      fontSize: theme.fontSize.xlarge,
      color: theme.colors.white,
    },
    form: {
      marginBottom: 20,
    },
    instructions: {
      color: theme.colors.transparentWhite1,
      marginBottom: 24,
      textAlign: 'center',
      lineHeight: 20,
    },
    inputContainer: {
      marginBottom: 24,
    },
    label: {
      marginBottom: 8,
      fontWeight: '500',
      color: theme.colors.white,
      fontSize: theme.fontSize.font14,
    },
    input: {
      height: 50,
      paddingHorizontal: 16,
      borderRadius: 8,
      borderWidth: 1,
    },
    resetButton: {
      padding: 14,
      borderRadius: 8,
      alignItems: 'center',
    },
    resetButtonText: {
      color: theme.colors.white,
      fontSize: theme.fontSize.medium,
      fontWeight: '600',
    },
    disabledButton: {
      opacity: 0.7,
    },
    otpLabel: {
      marginBottom: 6,
      fontSize: theme.fontSize.font14,
      fontWeight: '500',
      color: theme.colors.activeColor,
    },
    resendContainer: {
      alignSelf: 'flex-end',
      marginBottom: 15,
      padding: 2,
    },
    errorText: {
      color: theme.colors.coralRed,
      marginTop: 5,
      fontSize: theme.fontSize.small,
      marginBottom: -15,
    },
  });

export default ForgotPasswordScreen;
</code></pre>
        </article>
    </section>




            </div>
            
            <!-- Footer removed via patch -->
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

<script src="scripts/search.js"> </script>


</body>
</html>
