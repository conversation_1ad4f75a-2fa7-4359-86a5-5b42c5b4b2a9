import React, {useState, useRef} from 'react';
import {View, TouchableOpacity, FlatList, Animated, Easing} from 'react-native';
import Typography from '@/components/Typography';
import {useThemeStore} from '@/store/themeStore';
import {styles as CreateStyles} from './styles';
import CoachWrapper from '@/components/CoachWrapper';
import {useNavigation} from '@react-navigation/native';
import {Icon} from '@/components';
import RequestCard from '@/components/RequestCard';
import useTranslation from '@/hooks/useTranslation';

const mockData = [
  {
    date: 'January 24, 2025',
    requests: [
      {id: '1', type: 'Stringing', status: 'Open', date: 'January 24, 2025'},
      {id: '2', type: 'Grips', status: 'Pending', date: 'January 24, 2025'},
      {id: '3', type: 'Customization', status: 'Closed', date: 'January 24, 2025'},
      {id: '5', type: 'Customization', status: 'Closed', date: 'January 24, 2025'},
      {id: '6', type: 'Customization', status: 'Closed', date: 'January 24, 2025'},
      {id: '7', type: 'Customization', status: 'Closed', date: 'January 24, 2025'},
    ],
  },
  {
    date: 'January 23, 2025',
    requests: [{id: '4', type: 'Stringing', status: 'Open', date: 'January 23, 2025'}],
  },
];

const ManageServiceRequest = (props: any) => {
  const {onClose} = props;
  const theme = useThemeStore();
  const styles = CreateStyles(theme);
  const navigation = useNavigation();

  const [expandedGroups, setExpandedGroups] = useState<string[]>([mockData[0].date]);

  // Animated values for each group
  const rotationAnimations = useRef<{[key: string]: Animated.Value}>({}).current;

  const {t} = useTranslation();

  const toggleGroup = (date: string) => {
    const isExpanded = expandedGroups.includes(date);
    const newExpandedGroups = isExpanded
      ? expandedGroups.filter(d => d !== date)
      : [...expandedGroups, date];
    setExpandedGroups(newExpandedGroups);

    // Initialize animated value if not present
    if (!rotationAnimations[date]) {
      rotationAnimations[date] = new Animated.Value(isExpanded ? 1 : 0);
    }

    // Animate rotation
    Animated.timing(rotationAnimations[date], {
      toValue: isExpanded ? 0 : 1,
      duration: 200,
      easing: Easing.linear,
      useNativeDriver: true,
    }).start();
  };

  const renderRequest = ({item}: {item: any}) => (
    <TouchableOpacity activeOpacity={1}>
      <RequestCard type={item.type} status={item.status} date={item.date} />
    </TouchableOpacity>
  );

  const renderGroup = ({item}: {item: any}) => {
    const expanded = expandedGroups.includes(item.date);

    // Initialize animated value if not present
    if (!rotationAnimations[item.date]) {
      rotationAnimations[item.date] = new Animated.Value(expanded ? 1 : 0);
    }

    const rotateInterpolate = rotationAnimations[item.date].interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '180deg'],
    });

    return (
      <View style={{marginBottom: 8}}>
        <TouchableOpacity
          onPress={() => toggleGroup(item.date)}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginTop: 16,
          }}>
          <Typography variant="openingTitle" color={theme.colors.white}>
            {item.date}
          </Typography>
          <Animated.View style={{transform: [{rotate: rotateInterpolate}]}}>
            <Icon name="dropdown" size={20} color={theme.colors.white} />
          </Animated.View>
        </TouchableOpacity>
        {expanded && (
          <FlatList
            data={item.requests}
            keyExtractor={req => req.id}
            renderItem={renderRequest}
            scrollEnabled={false}
            style={{marginTop: 8}}
          />
        )}
      </View>
    );
  };

  return (
    <View style={{paddingHorizontal: 16, flex: 1}}>
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={onClose} style={{marginTop: 3}}>
          <Icon name="Left-chevron" size={24} color={theme.colors.white} />
        </TouchableOpacity>
        <Typography variant="invitePlayersTitle" color={theme.colors.white}>
          {t('manageServiceRequestScreen.title')}
        </Typography>
      </View>
      <View style={styles.numbersContainer}>
        <View style={styles.numberView}>
          <Typography variant="statsNumber" color={theme.colors.white}>
            13
          </Typography>
          <Typography variant="tryNow" color={theme.colors.white} style={{marginTop: -5}}>
            {t('manageServiceRequestScreen.stringing')}
          </Typography>
        </View>
        <View style={styles.numberView}>
          <Typography variant="statsNumber" color={theme.colors.white}>
            20
          </Typography>
          <Typography variant="tryNow" color={theme.colors.white} style={{marginTop: -5}}>
            {t('manageServiceRequestScreen.grips')}
          </Typography>
        </View>
        <View style={styles.numberView}>
          <Typography variant="statsNumber" color={theme.colors.white}>
            12
          </Typography>
          <Typography variant="tryNow" color={theme.colors.white} style={{marginTop: -5}}>
            {t('manageServiceRequestScreen.customs')}
          </Typography>
        </View>
      </View>
      <View style={{flex: 1, marginTop: 16}}>
        <FlatList
          data={mockData}
          keyExtractor={item => item.date}
          renderItem={renderGroup}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{flexGrow: 1}}
        />
      </View>
    </View>
  );
};

export default ManageServiceRequest;
