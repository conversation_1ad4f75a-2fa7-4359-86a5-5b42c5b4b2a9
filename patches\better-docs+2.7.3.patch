diff --git a/node_modules/better-docs/tmpl/layout.tmpl b/node_modules/better-docs/tmpl/layout.tmpl
index 38d6dba..f7652e8 100644
--- a/node_modules/better-docs/tmpl/layout.tmpl
+++ b/node_modules/better-docs/tmpl/layout.tmpl
@@ -48,15 +48,7 @@ var search = env.conf.templates && env.conf.templates.search
                 <?js= content ?>
             </div>
             <?js if (betterDocs.hideGenerator !== true) { ?>
-            <footer class="footer">
-                <div class="content has-text-centered">
-                    <p>Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc <?js= env.version.number ?></a><?js if(env.conf.templates && env.conf.templates.default && env.conf.templates.default.includeDate) { ?> on <?js= (new Date()) ?><?js } ?></p>
-                    <p class="sidebar-created-by">
-                        <a href="https://github.com/SoftwareBrothers/better-docs" target="_blank">BetterDocs theme</a> provided with <i class="fas fa-heart"></i> by
-                        <a href="http://softwarebrothers.co" target="_blank">SoftwareBrothers - JavaScript Development Agency</a>
-                    </p>
-                </div>
-            </footer>
+            <!-- Footer removed via patch -->
             <?js } ?>
         </div>
         <div id="side-nav" class="side-nav">
