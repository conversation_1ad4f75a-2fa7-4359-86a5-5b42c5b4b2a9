import React, {forwardRef, memo, useCallback, useImperativeHandle, useRef} from 'react';
import {View, StyleSheet} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {useTranslationContext} from '@/context/TranslationContext';
import Typography from '@/components/Typography';
import CButton from '@/components/CButton';
import {Icon} from '@/components';
import ConfirmationModal, {ConfirmationModalHandles} from '@/components/ConfirmationModal';

export interface DeleteAccountModalProps {
  onCancel: () => void;
  onConfirm: () => void;
  type: 'delete' | 'logout' | 'deleteNotification';
  loader: boolean;
  message?: string;
  confirmButtonText?: string;
}

export interface DeleteAccountModalHandles {
  open: () => void;
  close: () => void;
}

// Delete Account confirmation content component
const DeleteAccountContent = ({
  onCancel,
  onConfirm,
  loader,
}: {
  onCancel: () => void;
  onConfirm: () => void;
  loader: boolean;
}) => {
  const theme = useThemeStore();
  const {t} = useTranslationContext();

  return (
    <View style={styles(theme).contentContainer}>
      <Typography variant="subtitle" style={styles(theme).deleteTitle}>
        {t('auth.delete_account')}
      </Typography>

      <Icon
        name="delete"
        size={50}
        style={{marginBottom: 10, marginTop: 10}}
        color={theme.colors.darkRed}
      />

      <Typography variant="subtitle" style={styles(theme).message}>
        {t('auth.delete_account_confirmation')}
      </Typography>

      <Typography variant="body" style={styles(theme).details}>
        {t('auth.delete_account_details')}
      </Typography>

      <View style={styles(theme).buttonsContainer}>
        <CButton
          title={t('common.cancel')}
          onPress={onCancel}
          variant={'outline'}
          containerStyle={{
            width: '48%',
            backgroundColor: theme.colors.background,
            borderColor: theme.colors.activeColor,
          }}
          textStyle={{
            color: theme.colors.activeColor,
          }}
        />
        <CButton
          title={t('auth.confirm_delete')}
          onPress={onConfirm}
          loading={loader}
          variant={'outline'}
          containerStyle={{
            width: '48%',
            borderColor: theme.colors.darkRed,
            backgroundColor: theme.colors.darkRed + '20',
          }}
          textStyle={{
            color: theme.colors.darkRed,
          }}
        />
      </View>
    </View>
  );
};

// Logout confirmation content component
const LogoutContent = ({
  onCancel,
  onConfirm,
  message,
  confirmButtonText,
}: {
  onCancel: () => void;
  onConfirm: () => void;
  message?: string;
  confirmButtonText?: string;
}) => {
  const theme = useThemeStore();
  const {t} = useTranslationContext();

  return (
    <View style={styles(theme).contentContainer}>
      <Typography variant="subtitle" style={[styles(theme).title, {marginBottom: 20}]}>
        {message || t('auth.logout_confirmation')}
      </Typography>

      <View style={styles(theme).buttonsContainer}>
        <CButton
          title={t('common.cancel')}
          onPress={onCancel}
          variant={'outline'}
          containerStyle={{
            width: '48%',
            backgroundColor: theme.colors.background,
            borderColor: theme.colors.activeColor,
          }}
          textStyle={{
            color: theme.colors.activeColor,
          }}
        />
        <CButton
          title={confirmButtonText || t('auth.logout')}
          onPress={onConfirm}
          variant={'outline'}
          containerStyle={{
            width: '48%',
            borderColor: theme.colors.darkRed,
            backgroundColor: theme.colors.darkRed + '20',
          }}
          textStyle={{
            color: theme.colors.darkRed,
          }}
        />
      </View>
    </View>
  );
};

const DeleteNotificationContent = ({
  onCancel,
  onConfirm,
  loader,
}: {
  onCancel: () => void;
  onConfirm: () => void;
  loader: boolean;
}) => {
  const theme = useThemeStore();
  const {t} = useTranslationContext();

  return (
    <View style={styles(theme).contentContainer}>
      <Typography variant="subtitle" style={styles(theme).deleteTitle}>
        {t('NotificationScreen.deleteNotification')}
      </Typography>

      <Icon
        name="delete"
        size={50}
        style={{marginBottom: 10, marginTop: 10}}
        color={theme.colors.darkRed}
      />

      <Typography variant="subtitle" style={styles(theme).message}>
        {t('NotificationScreen.deleteNotificationConfirmation')}
      </Typography>

      {/* <Typography variant="body" style={styles(theme).details}>
        {t('NotificationScreen.deleteNotification')}
      </Typography> */}

      <View style={styles(theme).buttonsContainer}>
        <CButton
          title={t('common.cancel')}
          onPress={onCancel}
          variant={'outline'}
          containerStyle={{
            width: '48%',
            backgroundColor: theme.colors.background,
            borderColor: theme.colors.activeColor,
          }}
          textStyle={{
            color: theme.colors.activeColor,
          }}
        />
        <CButton
          title={t('auth.confirm_delete')}
          onPress={onConfirm}
          loading={loader}
          variant={'outline'}
          containerStyle={{
            width: '48%',
            borderColor: theme.colors.darkRed,
            backgroundColor: theme.colors.darkRed + '20',
          }}
          textStyle={{
            color: theme.colors.darkRed,
          }}
        />
      </View>
    </View>
  );
};

// Main component that uses the ConfirmationModal
const DeleteAccountModal = forwardRef<DeleteAccountModalHandles, DeleteAccountModalProps>(
  ({onCancel, onConfirm, type, loader, message, confirmButtonText}, ref) => {
    const modalRef = useRef<ConfirmationModalHandles>(null);

    // Expose methods to parent components
    useImperativeHandle(ref, () => ({
      open: () => {
        modalRef.current?.open();
      },
      close: () => {
        modalRef.current?.close();
      },
    }));

    // Handle actions
    const handleCancel = useCallback(() => {
      modalRef.current?.close();
      onCancel();
    }, [onCancel]);

    const handleConfirm = useCallback(() => {
      onConfirm();
    }, [onConfirm]);

    return (
      <ConfirmationModal
        ref={modalRef}
        heightPercentage={type === 'delete' ? 45 : type === 'deleteNotification' ? 35 : 20}>
        {type === 'delete' ? (
          <DeleteAccountContent onCancel={handleCancel} onConfirm={handleConfirm} loader={loader} />
        ) : type === 'deleteNotification' ? (
          <DeleteNotificationContent
            onCancel={handleCancel}
            onConfirm={handleConfirm}
            loader={loader}
          />
        ) : (
          <LogoutContent
            onCancel={handleCancel}
            onConfirm={handleConfirm}
            message={message}
            confirmButtonText={confirmButtonText}
          />
        )}
      </ConfirmationModal>
    );
  },
);

// Styles as a function to access theme consistently across components
const styles = (theme: any) =>
  StyleSheet.create({
    contentContainer: {
      alignItems: 'center',
    },
    title: {
      textAlign: 'center',
      color: theme.colors.text,
    },
    deleteTitle: {
      textAlign: 'center',
      color: theme.colors.darkRed,
    },
    message: {
      textAlign: 'center',
      color: theme.colors.text,
      marginBottom: 8,
    },
    details: {
      textAlign: 'center',
      color: theme.colors.text,
      marginBottom: 20,
    },
    buttonsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
      gap: 10,
    },
  });

export default memo(DeleteAccountModal);

DeleteAccountModal.displayName = 'DeleteAccountModal';
