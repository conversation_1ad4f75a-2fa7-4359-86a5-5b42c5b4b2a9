import api from '@/services/api';

export const playerConnectList = async (
  pageNo: number = 1,
  search?: string,
  rating?: any,
  tab?: string,
  primary_sport?: string,
  lat?: number,
  lng?: number,
) => {
  try {
    const params = {
      page: pageNo,
      searchParams: {
        ...(search ? {search} : {}),
        ...(rating ? {rating} : {}),
        ...(lat ? {lat} : {}),
        ...(lng ? {long: lng} : {}),
        ...(tab ? {tab} : {}),
        ...(primary_sport ? {primary_sport} : {}),
      },
    };
    const response = await api.post('/players-list-filters', params);
    if (response.status) {
      return response?.data;
    }
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};
