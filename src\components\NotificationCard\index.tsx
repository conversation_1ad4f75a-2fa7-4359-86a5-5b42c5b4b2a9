import {useThemeStore} from '@/store/themeStore';
import React from 'react';
import {StyleSheet, View, Image, ImageSourcePropType, TouchableOpacity} from 'react-native';
import Typography from '../Typography';
import {Icon} from '..';

/**
 * @category Interfaces
 * @typedef {Object} NotificationCardProps
 * @property {string} title - The title text to display in the notification
 * @property {string} type - The type of notification, affects the display style and actions
 * @property {ImageSourcePropType} image - Image source for the notification avatar/icon
 * @property {string} community - Community name associated with the notification
 * @property {string} time - Timestamp string for when the notification was created
 * @property {boolean} highlight - Whether the notification should be highlighted with special styling
 * @property {boolean} isRead - Whether the notification has been read by the user
 * @property {Function} onNotificationPress - Callback function triggered when the notification is pressed
 */
interface NotificationCardProps {
  title?: string;
  type?: 'invite' | 'default';
  image?: ImageSourcePropType;
  community?: string;
  time?: string;
  highlight?: boolean;
  isRead?: boolean;
  onNotificationPress: () => void;
}

/**
 * @component
 * @category Components
 *
 * @description
 * A reusable card component for displaying notifications with various states and types.
 * Supports different notification types (invite, default), read/unread states,
 * community associations, and relative time display.
 * @param {NotificationCardProps} props - The component props
 *
 * @return {JSX.Element} The rendered notification card
 */
const NotificationCard = ({
  title,
  type,
  image,
  community,
  time,
  highlight,
  isRead,
  onNotificationPress,
}: NotificationCardProps) => {
  const theme = useThemeStore();

  const getRelativeTime = (timestamp: string) => {
    const now = Date.now();
    const timestampMs = parseInt(timestamp, 10); // Convert string to number
    const diffMs = now - timestampMs; // difference in milliseconds

    const seconds = Math.floor(diffMs / 1000);
    const minutes = Math.floor(diffMs / (1000 * 60));
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (seconds < 60) return 'just now';
    if (minutes < 60) return `${minutes}m`;
    if (hours < 24) return `${hours}h`;
    if (days < 7) return `${days}d`;

    // For older dates, return formatted date
    const date = new Date(timestampMs); // Use the converted timestamp
    return date.toLocaleDateString();
  };

  return (
    <TouchableOpacity
      activeOpacity={1}
      onPress={() => {
        onNotificationPress();
      }}
      style={[
        styles(theme).container,
        {
          borderColor: isRead ? theme.colors.secondary : theme.colors.activeColor,
        },
      ]}>
      <View>
        <View style={styles(theme).titleContainer}>
          {community && (
            <View style={styles(theme).cardContainer}>
              <View style={styles(theme).emptyView} />
              <View style={styles(theme).communityTimeContainer}>
                <Typography variant="tagTitle" color={theme.colors.white}>
                  {'community'}
                </Typography>
                {time && (
                  <Typography variant="tagTitle" color={theme.colors.white}>
                    {getRelativeTime(time)}
                  </Typography>
                )}
              </View>
            </View>
          )}
          <View style={styles(theme).cardContainer}>
            <View style={styles(theme).imageAndTitle}>
              <View style={styles(theme).imgAndTitleContainer}>
                {image && <Image source={image} style={styles(theme).image} />}
              </View>
              <View style={styles(theme).flex1}>
                <Typography
                  variant="notificationText"
                  style={styles(theme).title}
                  color={highlight ? theme.colors.orange : theme.colors.white}>
                  {title}
                </Typography>
              </View>
            </View>

            {!community && time && (
              <Typography variant="tagTitle" color={theme.colors.white}>
                {getRelativeTime(time)}{' '}
                {/* Changed from remainingDays(time) to getRelativeTime(time) */}
              </Typography>
            )}
          </View>
          {type === 'invite' && (
            <View style={styles(theme).cardContainer}>
              <View style={styles(theme).emptyView} />
              <View style={styles(theme).buttonContainer}>
                <TouchableOpacity
                  style={[
                    styles(theme).plusButton,
                    {
                      backgroundColor: theme.colors.activeColor,
                    },
                  ]}>
                  <Icon name={'check'} size={24} color={theme.colors.background} />
                </TouchableOpacity>
                <TouchableOpacity activeOpacity={0.7}>
                  <Icon name="editpen" size={30} color={theme.colors.orange} />
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.notificationBg,
      width: '100%',
      paddingLeft: 16,
      paddingVertical: 12,
      borderWidth: 1,
      borderRadius: 10,
      overflow: 'hidden',
    },
    flex1: {
      flex: 1,
    },
    emptyView: {
      width: 42,
      marginRight: 15,
    },
    imageAndTitle: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    cardContainer: {
      flexDirection: 'row',
    },
    imgAndTitleContainer: {
      flexDirection: 'row',
    },
    image: {
      width: 42,
      height: 42,
      marginRight: 15,
      borderRadius: 100,
    },
    titleContainer: {
      flex: 1,
      paddingRight: 16,
      justifyContent: 'center',
    },
    title: {
      marginRight: 16,
    },
    plusButton: {
      width: 36,
      height: 36,
      borderRadius: 30,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 15,
    },
    buttonContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 8,
    },
    communityTimeContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: -8,
      flex: 1,
    },
  });

export default NotificationCard;
