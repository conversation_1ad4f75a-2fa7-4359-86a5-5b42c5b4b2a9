import React, {use<PERSON><PERSON>back, useMemo, useState} from 'react';
import {View, FlatList, ActivityIndicator, TouchableOpacity} from 'react-native';
import {createStyles} from './styles';
import {useAuthStore, useThemeStore} from '@/store';
import {
  <PERSON>er,
  NoData,
  SafeAreaView,
  CInput,
  CustomModal,
  RadioSelect,
  CButton,
} from '@/components';
import {useInfiniteQuery, useQueryClient} from '@tanstack/react-query';
import {getMyGroups, getPublicGroups, toaster} from '@/utils/commonFunctions';
import CLoader from '@/components/CLoader';
import GroupCard from '@/components/GroupCard';
import {useSendbirdChat} from '@sendbird/uikit-react-native';
import {useGroupChannel} from '@sendbird/uikit-chat-hooks';
import RefreshControl from '@/components/RefreshControl';
import {useJoinOrLeaveGroup, useRequestToJoinGroup} from '@/hooks/queries/groups';
import {predefinedTags} from '@/config/staticData';
import CIcon from '@/components/CIcon';
import debounce from 'lodash/debounce';
import useTranslation from '@/hooks/useTranslation';
import Typography from '@/components/Typography';

interface GroupsData {
  id: string;
  group_name: string;
  total_members: number;
  highlighted: boolean;
  type: string;
  locked: boolean;
  group_image: string;
  group_type: string;
  channel_url: string;
  location: 'string';
  login_user_exist: boolean;
  unread_count: number;
  has_user_requested: boolean;
}

interface ApiResponse {
  status: boolean;
  data: GroupsData[];
  pagination: {
    total: number;
    totalPages: number;
    currentPage: number;
    perPage: number;
  };
}

const GroupCardWrapper = ({
  item,
  navigation,
  type,
}: {
  item: GroupsData;
  navigation: any;
  type: string;
}) => {
  const channelUrl = item?.channel_url;
  const group_id = item?.id;
  const {sdk} = useSendbirdChat();
  const {channel} = useGroupChannel(sdk, channelUrl);
  const theme = useThemeStore();
  const queryClient = useQueryClient();
  const joinOrLeaveMutation = useJoinOrLeaveGroup();
  const [joiningGroupId, setJoiningGroupId] = React.useState<string | null>(null);
  const {user} = useAuthStore();
  const requestToJoinGroupMutation = useRequestToJoinGroup();

  const handleJoinOrLeave = async () => {
    try {
      setJoiningGroupId(item.id);
      const queryKey =
        type === 'popular'
          ? ['popular-groups', '', user?.location_lat, user?.location_long]
          : ['my-groups', ''];
      queryClient.setQueryData(queryKey, (oldData: any) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          pages: oldData.pages.map((page: any) => ({
            ...page,
            data: page.data.map((group: GroupsData) =>
              group.id === item.id ? {...group, login_user_exist: !group.login_user_exist} : group,
            ),
          })),
        };
      });

      await joinOrLeaveMutation.mutateAsync({groupId: item.id, refetch: true});

      // Invalidate both queries to ensure data is fresh
      queryClient.invalidateQueries({queryKey: ['my-groups']});
      queryClient.invalidateQueries({queryKey: ['popular-groups']});
    } catch (error: any) {
      toaster('error', error.message, 'top');

      // Get the correct query key based on type
      const queryKey =
        type === 'popular'
          ? ['popular-groups', '', user?.location_lat, user?.location_long]
          : ['my-groups', ''];

      // Revert the optimistic update on error
      queryClient.setQueryData(queryKey, (oldData: any) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          pages: oldData.pages.map((page: any) => ({
            ...page,
            data: page.data.map((group: GroupsData) =>
              group.id === item.id ? {...group, login_user_exist: !group.login_user_exist} : group,
            ),
          })),
        };
      });
    } finally {
      setJoiningGroupId(null);
    }
  };

  const handleRequestToJoinGroup = async (groupId: string) => {
    try {
      setJoiningGroupId(groupId);

      const response = await requestToJoinGroupMutation.mutateAsync({
        groupId: groupId,
        refetch: false,
      });

      if (response?.status === true) {
        toaster('success', response.message, 'top');

        const queryKey =
          type === 'popular'
            ? ['popular-groups', '', user?.location_lat, user?.location_long]
            : ['my-groups', ''];
        queryClient.setQueryData(queryKey, (oldData: any) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            pages: oldData.pages.map((page: any) => ({
              ...page,
              data: page.data.map((group: GroupsData) =>
                group.id === groupId ? {...group, has_user_requested: true} : group,
              ),
            })),
          };
        });
        queryClient.invalidateQueries({queryKey: ['my-groups']});
        queryClient.invalidateQueries({queryKey: ['popular-groups']});
      }
    } catch (error: any) {
      toaster('error', error.message, 'top');
    } finally {
      setJoiningGroupId(null);
    }
  };

  return (
    <GroupCard
      name={item.group_name}
      members={item.total_members}
      highlighted={item.highlighted}
      locked={item?.group_type === 'private'}
      containerStyle={{
        borderWidth: 1,
        borderColor: theme.colors.divider,
        marginVertical: 8,
        backgroundColor: theme.colors.background,
      }}
      location={item.location}
      requestedForJoin={item.has_user_requested}
      image={item.group_image}
      showJoinGroup={type === 'popular' && item.group_type !== 'private'}
      showMore={false}
      isMember={item.login_user_exist}
      onSelect={() => {
        handleRequestToJoinGroup(item.id);
      }}
      isLoading={joiningGroupId === item.id}
      onJoinGroup={handleJoinOrLeave}
      unReadCount={item?.unread_count}
      onPress={() => {
        if (channel && item.login_user_exist) {
          navigation.navigate('SendBird', {
            channelUrl: channelUrl,
            group_id,
          });
        } else {
          toaster('error', 'User is not connected with Sendbird', 'top');
        }
      }}
    />
  );
};
const GroupList = ({navigation, route}: any) => {
  const {title, type} = route.params || {};
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const {user} = useAuthStore();
  const {t} = useTranslation();

  // Search and filter state
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedValues, setSelectedValues] = useState<string[]>([]);
  const [appliedFilters, setAppliedFilters] = useState<string[]>([]);
  const [inputValue, setInputValue] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');

  const qKey =
    type === 'popular'
      ? [
          'popular-groups',
          searchQuery,
          appliedFilters.join(','),
          user?.location_lat,
          user?.location_long,
        ]
      : ['my-groups', searchQuery, appliedFilters.join(',')];

  const toggleSelection = (value: string) => {
    setSelectedValues(prev =>
      prev.includes(value) ? prev.filter(v => v !== value) : [...prev, value],
    );
  };

  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchQuery(value);
      }, 500),
    [],
  );

  const handleSearchChange = useCallback(
    (value: string) => {
      setInputValue(value);
      debouncedSearch(value);
    },
    [debouncedSearch],
  );

  const filterSelectedVal = appliedFilters.join(',');

  const {data, isLoading, refetch, fetchNextPage, hasNextPage, isFetchingNextPage, isRefetching} =
    useInfiniteQuery<ApiResponse>({
      queryKey: qKey,
      refetchOnMount: true,
      gcTime: 0,
      queryFn: async ({pageParam = 1}) => {
        const response =
          type === 'popular'
            ? await getPublicGroups(
                searchQuery,
                pageParam as number,
                filterSelectedVal,
                user?.location_lat,
                user?.location_long,
              )
            : await getMyGroups(type, pageParam as number, searchQuery, filterSelectedVal);
        return (
          response || {data: [], pagination: {total: 0, totalPages: 0, currentPage: 1, perPage: 10}}
        );
      },
      getNextPageParam: lastPage => {
        if (lastPage.pagination.currentPage < lastPage.pagination.totalPages) {
          return lastPage.pagination.currentPage + 1;
        }
        return undefined;
      },
      initialPageParam: 1,
    });

  const myGroupData = useMemo(() => {
    return data?.pages.flatMap(page => page.data) || [];
  }, [data?.pages]);

  console.log('myGroupData', myGroupData);

  const renderComponent = ({item}: {item: GroupsData}) => {
    return <GroupCardWrapper item={item} navigation={navigation} type={type} />;
  };

  const ListFooterComponent = useMemo(() => {
    return hasNextPage || isFetchingNextPage ? (
      <View style={{paddingVertical: 20, alignItems: 'center'}}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    ) : null;
  }, [hasNextPage, isFetchingNextPage, theme.colors.primary]);

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

  return (
    <SafeAreaView includeBottom={false} style={styles.root}>
      <Header
        leftIcon={{
          name: 'side-menu',
          size: 24,
          color: theme.colors.white,
          containerStyle: theme.colors.primary,
        }}
        rightIcons={[
          {name: 'notification', size: 24, badge: 0},
          {name: 'chat', size: 24, badge: 14},
        ]}
        pageTitle={title || 'Group List'}
        backgroundColor="transparent"
      />

      <View style={styles.searchContainer}>
        <View style={{flex: 1}}>
          <CInput
            inputStyle={styles.searchInput}
            placeholder={t('joinGroupsScreen.searchPlaceholder')}
            placeholderTextColorStyle={theme.colors.secondary}
            value={inputValue}
            onChangeText={handleSearchChange}
          />
        </View>
        <TouchableOpacity onPress={() => setIsModalVisible(true)}>
          <CIcon name="filter" size={28} color={theme.colors.white} />
        </TouchableOpacity>
      </View>

      <View style={styles.container}>
        {isLoading ? (
          <CLoader />
        ) : (
          <FlatList
            data={myGroupData}
            renderItem={renderComponent}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.content}
            showsVerticalScrollIndicator={false}
            onEndReachedThreshold={0.1}
            onEndReached={handleEndReached}
            refreshControl={
              <RefreshControl refreshing={isRefetching} onRefresh={() => refetch()} />
            }
            ListEmptyComponent={<NoData />}
            ListFooterComponent={ListFooterComponent}
            removeClippedSubviews={false}
            maxToRenderPerBatch={20}
            initialNumToRender={20}
            windowSize={5}
          />
        )}
      </View>

      <CustomModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        variant="bottom"
        showCloseButton={true}
        title="Filter results">
        <View>
          <Typography variant="subtitle" style={{color: theme.colors.offWhite}}>
            {t('addMembersScreen.show')}
          </Typography>
          {predefinedTags.map(option => (
            <RadioSelect
              key={option}
              label={option}
              selected={selectedValues.includes(option)}
              onPress={() => toggleSelection(option)}
            />
          ))}

          <View style={styles.buttonContainer}>
            <CButton
              title={t('addMembersScreen.results')}
              onPress={() => {
                setAppliedFilters(selectedValues);
                setIsModalVisible(false);
                refetch();
              }}
              variant="primary"
              containerStyle={{
                width: '49%',
              }}
            />
            <CButton
              title={t('common.clearFilters')}
              onPress={() => {
                setAppliedFilters([]);
                setSelectedValues([]);
                if (appliedFilters.length > 0 && selectedValues.length > 0) {
                  refetch();
                }
                setIsModalVisible(false);
              }}
              variant="outline"
              containerStyle={{
                width: '49%',
                backgroundColor: theme.colors.darkGray,
                borderWidth: 0,
              }}
            />
          </View>
        </View>
      </CustomModal>
    </SafeAreaView>
  );
};
export default GroupList;
