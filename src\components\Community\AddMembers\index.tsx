import React, {useCallback, useMemo, useState} from 'react';
import {
  View,
  TouchableOpacity,
  FlatList,
  TouchableWithoutFeedback,
  ActivityIndicator,
} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import Typography from '@/components/Typography';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import {createStyles} from './Styles';
import CIcon from '@/components/CIcon';
import CInput from '@/components/CInput';
import PlayerCard from '@/components/PlayerCard';
import CustomModal from '@/components/Modal';
import RadioSelect from '@/components/RadioSelect';
import RatingSlider from '@/components/RatingSlider';
import SearchInput from '@/components/SearchInput';
import CButton from '@/components/CButton';
import {NoData, SafeAreaView} from '@/components';
import useTranslation from '@/hooks/useTranslation';
import {useInfiniteQuery} from '@tanstack/react-query';
import {getUsersList, toaster} from '@/utils/commonFunctions';
import RefreshControl from '@/components/RefreshControl';
import CLoader from '@/components/CLoader';
import useDebounce from '@/hooks/useDebounce';
import {useConfigStore} from '@/store';

type NavigationProp = StackNavigationProp<CommunityStackParamList>;

interface MembersData {
  id: string;
  group_name: string;
  total_members: number;
  highlighted: boolean;
  type: string;
  locked: boolean;
  group_image: string;
}

interface ApiResponse {
  status: boolean;
  data: MembersData[];
  pagination: {
    total: number;
    totalPages: number;
    page: number;
    perPage: number;
  };
}

interface Member {
  id: string;
  profile_pic?: string;
  [key: string]: any;
}

const options = [
  {label: 'Tennis', value: 'tennis'},
  {label: 'Pickleball', value: 'pickleball'},
  {label: 'Plateform Tennis', value: 'plateform-tennis'},
  {label: 'Padel', value: 'padel'},
];
const options2 = [
  {label: 'Friends', value: 'friends'},
  {label: 'Invited', value: 'invited'},
  {label: 'Sponsored', value: 'sponsored'},
];

const AddMembers = ({route}: {route: any}) => {
  const {setSelectedGroupMembers, getSelectedGroupMembers} = useConfigStore();

  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp>();
  const [search, setSearch] = React.useState<string | null>(null);
  const [selectedMembers, setSelectedMembers] = React.useState<Member[]>(
    getSelectedGroupMembers() || [],
  );
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedValues, setSelectedValues] = React.useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [appliedFilters, setAppliedFilters] = useState<string[]>([]);
  const [utrRating, setUtrRating] = useState(0);
  const [appliedUtrRating, setAppliedUtrRating] = useState(0);

  const {t} = useTranslation();
  const maxMembers = 25;

  const handleSelect = (item: Member) => {
    setSelectedMembers((prev: Member[]) => {
      const isSelected = prev.some(member => member.id === item.id);
      if (isSelected) {
        const newSelection = prev.filter(member => member.id !== item.id);
        setSelectedGroupMembers(newSelection);
        return newSelection;
      } else {
        if (prev.length >= maxMembers) {
          toaster('error', `You can select a maximum of ${maxMembers} members`, 'top');
          return prev;
        }
        const newSelection = [...prev, item];
        setSelectedGroupMembers(newSelection);
        return newSelection;
      }
    });
  };

  const renderMember = ({item}: any) => {
    return (
      <TouchableWithoutFeedback key={item.id}>
        <PlayerCard
          playerData={item}
          onSelect={() => handleSelect(item)}
          isSelected={selectedMembers.some(member => member.id === item.id)}
          borderColor={theme.colors.activeColor}
        />
      </TouchableWithoutFeedback>
    );
  };
  const debouncedSearch = useDebounce(search, 500);
  const filterSelectedVal = appliedFilters.join(',');
  const {
    data: groupData,
    isLoading,
    refetch,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isRefetching,
  } = useInfiniteQuery<ApiResponse>({
    queryKey: ['users-list', debouncedSearch, filterSelectedVal, appliedUtrRating],
    gcTime: 0,

    queryFn: async ({pageParam = 1}) => {
      const response = await getUsersList(
        pageParam as number,
        debouncedSearch,
        filterSelectedVal,
        appliedUtrRating,
      );

      return (
        response || {
          data: [],
          pagination: {total: 0, totalPages: 0, currentPage: 1, perPage: 10},
        }
      );
    },
    getNextPageParam: lastPage => {
      if (lastPage.pagination.page < lastPage.pagination.totalPages) {
        return lastPage.pagination.page + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });

  const UsersListData = useMemo(() => {
    return (
      groupData?.pages.flatMap(page =>
        page.data.map((user: any) => ({
          ...user,
          image: user.profile_pic,
        })),
      ) || []
    );
  }, [groupData?.pages]);

  const toggleSelection = (value: string) => {
    setSelectedValues(prev =>
      prev.includes(value) ? prev.filter(v => v !== value) : [...prev, value],
    );
  };

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

  const ListFooterComponent = useMemo(() => {
    return hasNextPage || isFetchingNextPage ? (
      <View style={{paddingVertical: 20, alignItems: 'center'}}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    ) : null;
  }, [hasNextPage, isFetchingNextPage, theme.colors.primary]);

  return (
    <SafeAreaView includeTop={true} includeBottom={false}>
      <View style={styles.content}>
        <View style={styles.headerRow}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Typography variant="parkTitle" color={theme.colors.primary}>
              {t('common.back')}
            </Typography>
          </TouchableOpacity>
          <Typography variant="parkTitle" color={theme.colors.white}>
            {t('addMembersScreen.addMembers')}
          </Typography>
          <TouchableOpacity
            disabled={selectedMembers?.length === 0}
            onPress={() => {
              setSelectedGroupMembers(selectedMembers);
              navigation.goBack();
            }}>
            <Typography
              variant="parkTitle"
              color={
                selectedMembers?.length === 0 ? theme.colors.secondary : theme.colors.activeColor
              }>
              {t('common.next')}
            </Typography>
          </TouchableOpacity>
        </View>
        <Typography variant="bodyMedium" style={styles.memberCount} color={theme.colors.white}>
          {selectedMembers.length}/{maxMembers}
        </Typography>
        <CInput
          variant="dark"
          inputStyle={styles.searchInput}
          placeholder={t('addMembersScreen.searchPlaceholder')}
          placeholderTextColorStyle={theme.colors.secondary}
          value={search}
          onChangeText={setSearch}
        />
        <View style={styles.resultsHeaderRow}>
          <Typography variant="court" color={theme.colors.white}>
            {t('addMembersScreen.results')}
          </Typography>
          <TouchableOpacity onPress={() => setIsModalVisible(true)}>
            <CIcon name="filter" size={25} color={theme.colors.white} />
          </TouchableOpacity>
        </View>
        {isLoading ? (
          <CLoader />
        ) : (
          <FlatList
            data={UsersListData}
            renderItem={renderMember}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.membersList}
            showsVerticalScrollIndicator={false}
            onEndReached={handleEndReached}
            onEndReachedThreshold={0.5}
            refreshControl={
              <RefreshControl refreshing={isRefetching} onRefresh={() => refetch()} />
            }
            ListEmptyComponent={() => <NoData />}
            ListFooterComponent={ListFooterComponent}
            removeClippedSubviews={false}
            maxToRenderPerBatch={20}
            initialNumToRender={20}
            windowSize={5}
          />
        )}
      </View>
      <CustomModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        variant="bottom"
        showCloseButton={true}
        title="Filter results">
        <View>
          <Typography variant="subtitle" style={{color: theme.colors.offWhite}}>
            {t('addMembersScreen.show')}
          </Typography>
          {options.map(option => (
            <RadioSelect
              key={option.value}
              label={option.label}
              selected={selectedValues.includes(option.value)}
              onPress={() => toggleSelection(option.value)}
            />
          ))}

          {/* Rating Slider */}
          <RatingSlider min={0} max={20} value={utrRating} onChange={setUtrRating} />

          {options2.map(option => (
            <RadioSelect
              key={option.value}
              label={option.label}
              selected={selectedValues.includes(option.value)}
              onPress={() => toggleSelection(option.value)}
            />
          ))}

          <SearchInput
            variant="light"
            placeholder={t('addMembersScreen.searchEvent')}
            containerStyle={styles.filterInput}
            inputStyle={{color: theme.colors.primary}}
            value={searchValue}
            placeholderTextColor={theme.colors.primary}
            onChangeText={setSearchValue}
            iconColor={theme.colors.primary}
            onClear={() => setSearchValue('')}
          />

          <View style={styles.buttonContainer}>
            <CButton
              title={t('addMembersScreen.results')}
              onPress={() => {
                setAppliedFilters(selectedValues);
                setAppliedUtrRating(utrRating);
                setSearch(searchValue);
                setIsModalVisible(false);
                refetch();
              }}
              variant="primary"
              containerStyle={{
                height: 70,
                width: '49%',
              }}
            />
            <CButton
              title={t('common.clearFilters')}
              onPress={() => {
                setAppliedFilters([]);
                setSelectedValues([]);
                setUtrRating(0);
                if (appliedFilters.length > 0 && selectedValues.length > 0 && utrRating > 0) {
                  refetch();
                }
                setIsModalVisible(false);
              }}
              variant="outline"
              containerStyle={{
                width: '49%',
                backgroundColor: theme.colors.darkGray,
                borderWidth: 0,
              }}
            />
          </View>
        </View>
      </CustomModal>
    </SafeAreaView>
  );
};

export default AddMembers;
