import {StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    root: {
      flex: 1,
    },
    title: {
      color: theme.colors.text,
      marginBottom: -15,
    },
    keyboardAvoidingView: {
      // flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingHorizontal: 16,
      paddingBottom: 25,
    },
    form: {
      marginVertical: 10,
    },
    inputContainer: {
      marginBottom: 16,
      // borderWidth: 1,
    },
    label: {
      marginBottom: 8,
      fontWeight: '500',
      color: theme.colors.text,
      fontSize: theme.fontSize.font14,
    },
    input: {
      height: 50,
      paddingHorizontal: 16,
      borderRadius: 8,
      borderWidth: 1,
    },
    btnContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: 10,
      gap: 10,
      minHeight: 95,
    },
    radioContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      columnGap: 15,
    },
    radioTitle: {
      color: theme.colors.text,
    },
    fitnessBtn: {
      width: '48%',
      paddingHorizontal: 10,
      paddingVertical: 10,
    },
    fitnessRadio: {
      width: 170,
    },
    fitnessText: {
      fontSize: theme.fontSize.medium,
      fontWeight: '400',
      textAlign: 'center',
    },
    buttonText: {
      color: theme.colors.orange,
      fontSize: theme.fontSize.font14,
      fontWeight: '400',
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: 15,
      flex: 1,
      marginTop: 4,
    },

    flex1: {
      flex: 1,
    },
    errorText: {
      color: theme.colors.coralRed,
      marginTop: -3,
      fontSize: theme.fontSize.small,
      marginBottom: -5,
    },
    randomName: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    titleContainer: {
      paddingHorizontal: 16,
      paddingBottom: 10,
    },
  });
