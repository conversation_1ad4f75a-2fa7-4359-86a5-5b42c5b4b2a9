import React from 'react';
import {StyleProp, Image, TouchableOpacity, View, ViewStyle, ActivityIndicator} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {createStyles} from './styles';
import Typography from '@/components/Typography';
import {Icon} from '@/components';

interface GroupCardProps {
  name: string;
  members: number;
  highlighted?: boolean;
  onPress?: () => void;
  containerStyle?: StyleProp<ViewStyle>;
  locked?: boolean;
  location?: string;
  showJoinGroup?: boolean;
  showMore?: boolean;
  onMorePress?: () => void;
  onSelect?: () => void;
  isSelected?: boolean;
  image?: string;
  onJoinGroup?: () => void;
  isMember?: boolean;
  isLoading?: boolean;
  unReadCount?: number;
  requestedForJoin?: boolean;
}

const GroupCard: React.FC<GroupCardProps> = ({
  name,
  members,
  highlighted,
  onPress,
  containerStyle,
  locked = false,
  location = '',
  showJoinGroup = false,
  showMore = false,
  onMorePress = () => {},
  onSelect = () => {},
  isSelected = false,
  image = '',
  onJoinGroup = () => {},
  isMember,
  isLoading = false,
  unReadCount = 0,
  requestedForJoin = false,
}) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  return (
    <TouchableOpacity
      style={[styles.groupCard, containerStyle]}
      onPress={onPress}
      activeOpacity={1}>
      <View style={styles.groupIconContainer}>
        {image ? (
          <Image source={{uri: image}} style={styles.groupIcon} />
        ) : (
          <Icon name="groupicon-1" size={33} color={theme.colors.black} />
        )}
      </View>
      <View style={styles.groupTextContainer}>
        <View style={styles.groupNameContainer}>
          <Typography
            numberOfLines={1}
            variant="permissionTitle"
            color={highlighted ? theme.colors.orange : theme.colors.white}>
            {name}
          </Typography>
          <Typography
            variant="gameCardDescription"
            color={highlighted ? theme.colors.orange : theme.colors.white}
            style={{marginTop: 2}}>
            {members} members
          </Typography>
          {location ? (
            <View style={styles.locationContainer}>
              <Icon name="location-pin" size={18} color={theme.colors.orange} />
              <Typography variant="gameCardDescription" color={theme.colors.white}>
                {location}
              </Typography>
            </View>
          ) : null}
        </View>
        {locked &&
          (isLoading ? (
            <ActivityIndicator size="small" color={theme.colors.white} />
          ) : requestedForJoin ? (
            <Typography variant="tryNow" color={theme.colors.white}>
              Requested
            </Typography>
          ) : (
            <TouchableOpacity style={styles.lockContainer} onPress={onSelect}>
              <Icon name={isSelected ? 'check' : 'lock-1'} size={18} color={theme.colors.black} />
            </TouchableOpacity>
          ))}

        <View style={styles.buttonContainer}>
          {showJoinGroup && (
            <TouchableOpacity activeOpacity={0.7} onPress={onJoinGroup} disabled={isLoading}>
              {isLoading ? (
                <ActivityIndicator size="small" color={theme.colors.white} />
              ) : (
                <Typography variant="tryNow" color={theme.colors.white}>
                  {isMember ? 'Leave group' : 'Join group'}
                </Typography>
              )}
            </TouchableOpacity>
          )}
          {showMore && (
            <TouchableOpacity activeOpacity={0.7} onPress={onMorePress}>
              <Typography variant="permissionTitle" color={theme.colors.white} align="right">
                More{' '}
              </Typography>
            </TouchableOpacity>
          )}
          {unReadCount > 0 && (
            <TouchableOpacity activeOpacity={0.7} onPress={onMorePress} style={styles.countView}>
              <Typography
                variant="body"
                color={theme.colors.black}
                align="center"
                style={styles.countText}>
                {unReadCount > 9 ? '9+' : unReadCount}
              </Typography>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default GroupCard;
