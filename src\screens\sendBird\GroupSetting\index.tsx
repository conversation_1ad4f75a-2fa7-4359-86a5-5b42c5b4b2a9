import React, {useRef, useState, useEffect} from 'react';
import {createGroupChannelSettingsFragment, useSendbirdChat} from '@sendbird/uikit-react-native';
import {useGroupChannel} from '@sendbird/uikit-chat-hooks';
import {useNavigation, useIsFocused} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation';
import {useJoinOrLeaveGroup} from '@/hooks/queries/groups';
import DeleteAccountModal, {DeleteAccountModalHandles} from '@/components/DeleteAccountModal';
import useTranslation from '@/hooks/useTranslation';

type NavigationProp = StackNavigationProp<RootStackParamList, 'SendBird'>;

const GroupSetting = ({route}: {route: any}) => {
  const GroupChannelSettingsFragment = createGroupChannelSettingsFragment();
  const deleteModalRef = useRef<DeleteAccountModalHandles>(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const isFocused = useIsFocused();

  const {channelUrl, group_id} = route.params;
  const {sdk} = useSendbirdChat();
  const {channel} = useGroupChannel(sdk, channelUrl);
  const navigation = useNavigation<NavigationProp>();
  const joinOrLeaveMutation = useJoinOrLeaveGroup();
  const [isDeleting, setIsDeleting] = React.useState(false);
  const {t} = useTranslation();

  useEffect(() => {
    if (isFocused && channel) {
      setRefreshKey(prev => prev + 1);
    }
    if (channel) {
      // Patch to disable leave
      channel.leave = () => {
        return Promise.resolve();
      };
    }
  }, [isFocused, channel]);

  const handleDeleteCancel = () => {
    deleteModalRef.current?.close();
  };

  const handleDeleteConfirm = async () => {
    try {
      setIsDeleting(true);
      const response = await joinOrLeaveMutation.mutateAsync({groupId: group_id, refetch: true});
      deleteModalRef.current?.close();

      // Only navigate to top if the operation was successful
      if (response?.status) {
        navigation.popToTop();
      }
    } catch (error) {
      console.error('Error leaving group:', error);
      // Don't navigate to top on error
    } finally {
      setIsDeleting(false);
    }
  };

  if (!channel) return null;

  return (
    <>
      <GroupChannelSettingsFragment
        key={refreshKey}
        channel={channel}
        onPressHeaderLeft={() => navigation.goBack()}
        onPressMenuModeration={() =>
          navigation.navigate('GroupModeration', {
            channelUrl: channelUrl,
          })
        }
        onPressMenuMembers={() =>
          navigation.navigate('MemberList', {
            channelUrl: channelUrl,
          })
        }
        onPressMenuLeaveChannel={() => deleteModalRef.current?.open()}
      />
      <DeleteAccountModal
        ref={deleteModalRef}
        type="logout"
        onCancel={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        loader={isDeleting}
        message={t('myGroupsScreen.leaveConfirmation')}
        confirmButtonText={t('myGroupsScreen.leave')}
      />
    </>
  );
};

export default GroupSetting;
