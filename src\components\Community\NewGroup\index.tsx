import React, {useEffect} from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Text,
  Dimensions,
} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import Typography from '@/components/Typography';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import {createStyles} from './Styles';
import CInput from '@/components/CInput';
import * as yup from 'yup';
import {useForm, Controller} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import {CImage, Icon, SafeAreaView} from '@/components';
import useTranslation from '@/hooks/useTranslation';
import UploadComponent from '@/components/Cupload/Index';
import {groupTagsOptions, groupTypeOptions} from '@/config/staticData';
import {toaster} from '@/utils/commonFunctions';
import {useUploadFile} from '@/hooks/queries/useUsers';
import CustomDropdown from '@/components/CustomDropdown/CustomDropdown';
import LocationSearch from '@/components/GooglePlaceAutocomplete';
import {CollapsibleViewWithIcon} from '@/components/CollapsibleView';
import {useAuthStore, useConfigStore} from '@/store';
import {useCreateUserGroup} from '@/hooks/queries/groups';
import TagsComponent from '@/components/CustomTags';

type NavigationProp = StackNavigationProp<CommunityStackParamList>;

type FormValues = {
  groupImage: string | null;
  groupName: string;
  groupType: 'public' | 'private' | 'hidden';
  favoriteLocation: string;
  locationCoordinates: {
    lat?: number;
    lng?: number;
  } | null;
  tags: string[];
  description: string;
};

interface NewGroupProps {
  route: {
    params?: {
      groupImage?: string | null;
      groupName?: string;
      groupType?: 'Public' | 'Private' | 'Hidden';
      favoriteLocation?: string;
      tags?: string;
      description?: string;
    };
  };
}

const NewGroup: React.FC<NewGroupProps> = ({route}) => {
  const data = route.params || {};
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp>();

  const {getSelectedGroupMembers, setSelectedGroupMembers} = useConfigStore();

  const selectedMembers = getSelectedGroupMembers() || [];

  const locationRef = React.useRef<any>(null);

  const {t} = useTranslation();
  const imageUpload = useUploadFile();
  const {user} = useAuthStore();

  const createGroupMutation = useCreateUserGroup();

  const {width: screenWidth} = Dimensions.get('screen');
  const itemWidth = (screenWidth - 40 - 3 * 10) / 4; // 40 for container padding (20 each side), 3*10 for gaps between 4 items

  useEffect(() => {
    setSelectedGroupMembers([]);
  }, []);

  const schema = yup.object().shape({
    groupImage: yup.string().nullable(),
    groupName: yup.string().required(t('newGroupScreen.groupNameRequired')),
    groupType: yup
      .string()
      .oneOf(['public', 'private', 'hidden'])
      .required(t('newGroupScreen.groupTypeRequired')),
    favoriteLocation: yup.string().required(t('newGroupScreen.favoriteLocationRequired')),
    locationCoordinates: yup
      .object()
      .shape({
        lat: yup.number().optional(),
        lng: yup.number().optional(),
      })
      .nullable()
      .default(null),
    tags: yup.array().of(yup.string()).min(1, t('newGroupScreen.tagsRequired')),
    description: yup.string().required(t('newGroupScreen.groupDescriptionRequired')),
  });

  const {
    control,
    handleSubmit,
    formState: {errors},
    setValue,
  } = useForm<FormValues>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      groupImage: data?.groupImage || null,
      groupName: data?.groupName || '',
      groupType: (data?.groupType?.toLowerCase() as 'public' | 'private' | 'hidden') || '',
      favoriteLocation: data?.favoriteLocation || '',
      locationCoordinates: null,
      tags: [],
      description: data?.description || '',
    },
  });
  console.log('errors=====>>>>>', errors);

  const onSubmit: (data: FormValues) => void = data => {
    console.log('data=====>>>>>', data);
    const formattedData = {
      group_name: data?.groupName,
      group_type: data?.groupType,
      group_image: data?.groupImage,
      location: data?.favoriteLocation,
      location_lat: data?.locationCoordinates?.lat,
      location_long: data?.locationCoordinates?.lng,
      description: data?.description,
      users: [
        ...selectedMembers,
        {
          id: user?.id,
          name: user?.name,
          image:
            user?.profile_pic ||
            'https://goraqt-storage.s3.us-east-1.amazonaws.com/user/user_photo.jpg',
        },
      ],
      tags: data?.tags,
    };

    console.log('formattedData=====>>>>>', formattedData);

    createGroupMutation.mutate(formattedData as any, {
      onSuccess: response => {
        navigation.popToTop();
        toaster('success', response.message, 'top');
        setSelectedGroupMembers([]);
      },
      onError: error => {
        toaster('error', error.message, 'top');
      },
    });
  };

  const handleUpload = (imageObj: Array<any>) => {
    const files = imageObj.map(img => ({
      uri: img?.path,
      name: img?.filename,
      type: img?.mime,
      size: img?.size,
    }));
    console.log('Files to upload:', files);
    imageUpload.mutate(
      {files},
      {
        onSuccess: response => {
          if (response?.status) {
            console.log('Image uploaded successfully:', response);
            setValue('groupImage', response?.data[0]);
          }
        },
        onError: error => {
          toaster('error', error.message, 'top');
        },
      },
    );
  };

  return (
    <SafeAreaView
      includeTop={true}
      includeBottom={false}
      style={{flex: 1, backgroundColor: theme.colors.background}}>
      <View style={styles.headerRow}>
        <TouchableOpacity
          onPress={() => {
            setSelectedGroupMembers([]);
            navigation.goBack();
          }}>
          <Typography variant="parkTitle" color={theme.colors.primary}>
            {t('common.back')}
          </Typography>
        </TouchableOpacity>
        <Typography variant="parkTitle" color={theme.colors.white}>
          {t('newGroupScreen.newGroup')}
        </Typography>

        <TouchableOpacity
          onPress={handleSubmit(onSubmit as any)}
          disabled={createGroupMutation.isPending || selectedMembers?.length === 0}>
          {createGroupMutation.isPending ? (
            <ActivityIndicator size="small" color={theme.colors.primary} />
          ) : (
            <Typography
              variant="parkTitle"
              color={
                selectedMembers?.length === 0 ? theme.colors.secondary : theme.colors.activeColor
              }>
              {t('common.create')}
            </Typography>
          )}
        </TouchableOpacity>
      </View>
      <ScrollView contentContainerStyle={styles.content}>
        {/* Group Name with Camera Icon */}
        <View
          style={[styles.groupNameContainer, {marginBottom: errors.groupName?.message ? 0 : 10}]}>
          <Controller
            control={control}
            name="groupImage"
            render={({field: {value}, fieldState: {error}}) => (
              <UploadComponent
                value={value}
                onSelected={e => handleUpload(Array.isArray(e) ? e : [e])}
                allowCameraCapture={true}
                allowGallerySelection={true}
                containerStyle={styles.cameraCircle}
                imageContentStyle={styles.imageContent}
              />
            )}
          />

          <Controller
            control={control}
            name="groupName"
            render={({field: {onChange, value, onBlur}}) => (
              <View style={{flex: 1}}>
                <CInput
                  containerStyle={styles.groupNameInput}
                  placeholder={t('newGroupScreen.groupNamePlaceholder')}
                  inputStyle={styles.groupNameInput}
                  placeholderTextColorStyle={theme.colors.activeColor}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  onSubmitEditing={() => locationRef.current?.focus()}
                />
              </View>
            )}
          />
        </View>
        {errors.groupName?.message && (
          <Typography style={[styles.errorText, {marginBottom: 10}]}>
            {errors.groupName?.message}
          </Typography>
        )}

        <View style={{marginBottom: 20}}>
          <Controller
            control={control}
            name="description"
            render={({field: {onChange, value, onBlur}}) => (
              <CInput
                variant="light"
                multiline={true}
                style={styles.textArea}
                numberOfLines={10}
                label={t('newGroupScreen.groupDescription')}
                showLabel={true}
                labelStyle={styles.label}
                placeholder={t('newGroupScreen.groupDescriptionPlaceholder')}
                placeholderTextColor={theme.colors.activeColor}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                hasError={!!errors.description}
                error={errors.description?.message}
                inputStyle={{...styles.inputText, textAlignVertical: 'top'}}
                containerStyle={{flex: 1}}
                // ref={classDescriptionInputRef}
                returnKeyType="next"
                // onSubmitEditing={() => numberOfStudentsInputRef.current?.focus()}
                blurOnSubmit={false}
                cursorColor={theme.colors.white}
                maxLength={1500}
              />
            )}
          />
        </View>

        <CollapsibleViewWithIcon
          initiallyExpanded
          customHeader={
            <Typography variant="notificationText" color={theme.colors.white}>
              {t('newGroupScreen.groupSettings')}
            </Typography>
          }
          containerStyle={styles.collapsibleContainer}
          titleStyle={styles.collapsibleTitle}>
          <Typography style={{marginTop: 25}} variant="notificationText" color={theme.colors.white}>
            {t('newGroupScreen.privacy')}
          </Typography>
          <Controller
            control={control}
            name="groupType"
            render={({field: {onChange, value}}) => (
              <View style={styles.privacyRow}>
                {groupTypeOptions?.map(option => (
                  <TouchableOpacity
                    activeOpacity={0.7}
                    key={option?.value}
                    style={[
                      styles.privacyButton,
                      value === option?.value && styles.privacyButtonSelected,
                    ]}
                    onPress={() => onChange(option?.value as 'public' | 'private' | 'hidden')}>
                    <Typography
                      variant="tryNow"
                      style={
                        value === option?.value
                          ? [styles.privacyButtonText, styles.privacyButtonTextSelected]
                          : [styles.privacyButtonText]
                      }>
                      {option?.label}
                    </Typography>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          />
          {errors.groupType && (
            <Typography style={{color: theme.colors.coralRed, marginBottom: 8}}>
              {errors.groupType.message}
            </Typography>
          )}

          <Controller
            control={control}
            name="favoriteLocation"
            render={({field: {onChange, value, onBlur}}) => (
              <View style={{marginVertical: 16}}>
                <LocationSearch
                  inputStyle={{
                    backgroundColor: theme.colors.background,
                    borderWidth: 1,
                    borderColor: theme.colors.placeholder,
                    borderRadius: 10,
                  }}
                  handleLocationSelect={(location, coordinates) => {
                    onChange(location);
                    if (coordinates) {
                      setValue('locationCoordinates', coordinates);
                    }
                  }}
                  selectedLocation={value}
                  label={t('newGroupScreen.favoriteLocation')}
                  placeholder={t('newGroupScreen.favoriteLocationPlaceholder')}
                  locationBtn={false}
                />
                {errors.favoriteLocation?.message && (
                  <Typography style={[styles.errorText]}>
                    {errors.favoriteLocation?.message}
                  </Typography>
                )}
              </View>
            )}
          />

          <Controller
            control={control}
            name="tags"
            render={({field: {onChange, value, onBlur}}) => (
              <>
                {/* <CustomDropdown
                  sportsData={groupTagsOptions}
                  value={value}
                  placeholder={t('newGroupScreen.tags')}
                  onChangeValue={onChange}
                  containerStyle={styles.dropdownContainer}
                  mainStyles={styles.dropdownMain}
                  label={t('newGroupScreen.tags')}
                  labelStyle={styles.label}
                  contentContainerStyle={styles.dropdownContent}
                  placeholderStyle={styles.placeholderText}
                /> */}
                <TagsComponent initialTags={value} onTagsChange={onChange} />
                {errors.tags?.message && (
                  <Typography style={[styles.errorText, {marginBottom: 20}]}>
                    {errors.tags?.message}
                  </Typography>
                )}
              </>
            )}
          />
        </CollapsibleViewWithIcon>

        <View style={styles.addMembersContainer}>
          <TouchableOpacity
            onPress={() => navigation.navigate('AddMembers')}
            style={styles.addMembersBtn}>
            <Typography variant="parkTitle" color={theme.colors.activeColor}>
              {t('addMembersScreen.addMembers')}
            </Typography>
            <Icon
              name="create-group"
              size={18}
              color={theme.colors.activeColor}
              style={{marginLeft: 5}}
            />
          </TouchableOpacity>
          <Typography
            style={[styles.selectedMembersTitle, {marginTop: 5}]}
            variant="notificationText"
            color={theme.colors.white}>
            {selectedMembers.length} {t('createGroupMemberListScreen.of')} 25
          </Typography>
        </View>
        <View
          style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            gap: 10,
            marginTop: 15,
          }}>
          {selectedMembers.map(item => (
            <View key={item.id} style={[styles.memberContainer, {width: itemWidth}]}>
              <View style={styles.memberAvatarContainer}>
                <CImage source={{uri: item.profile_pic || ''}} style={styles.memberAvatar} />
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => {
                    setSelectedGroupMembers(selectedMembers.filter(m => m.id !== item.id));
                  }}>
                  <Icon name="close" size={12} color={theme.colors.black} />
                </TouchableOpacity>
              </View>
              <Typography
                style={styles.userName}
                color={theme.colors.inputLabel}
                variant="caption"
                align="center"
                numberOfLines={2}>
                {item.name || item.display_name}
              </Typography>
            </View>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default NewGroup;
