import React from 'react';
import {createGroupChannelModerationFragment, useSendbirdChat} from '@sendbird/uikit-react-native';
import {useGroupChannel} from '@sendbird/uikit-chat-hooks';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation';

type NavigationProp = StackNavigationProp<RootStackParamList, 'SendBird'>;

const GroupModeration = ({route}: {route: any}) => {
  const GroupChannelModerationFragment = createGroupChannelModerationFragment();

  const {channelUrl} = route.params;
  const {sdk} = useSendbirdChat();
  const {channel} = useGroupChannel(sdk, channelUrl);
  const navigation = useNavigation<NavigationProp>();
  if (!channel) return null;

  return (
    <GroupChannelModerationFragment
      channel={channel}
      onPressHeaderLeft={() => navigation.goBack()}
      onPressMenuOperators={() =>
        navigation.navigate('OperatorList', {
          channelUrl: channelUrl,
        })
      }
      onPressMenuMutedMembers={() =>
        navigation.navigate('MutedList', {
          channelUrl: channelUrl,
        })
      }
      onPressMenuBannedUsers={() =>
        navigation.navigate('BannedList', {
          channelUrl: channelUrl,
        })
      }
    />
  );
};

export default GroupModeration;
