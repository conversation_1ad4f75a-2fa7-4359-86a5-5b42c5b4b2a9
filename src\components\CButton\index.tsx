import React from 'react';
import {useThemeStore} from '@/store/themeStore';
import {
  TouchableOpacity,
  Text,
  GestureResponderEvent,
  ActivityIndicator,
  StyleProp,
  ViewStyle,
  TextStyle,
  View,
} from 'react-native';
import {styles as createStyles} from './styles';
type Variant = 'primary' | 'secondary' | 'pill' | 'square' | 'dark' | 'outline' | 'active';

/**
 * @category Interfaces
 * @typedef {Object} CButtonProps
 * @property {string} title - The text content displayed on the button.
 * @property {string} variant - The visual style variant of the button @default 'primary'.
 * @property {boolean} isDisabled - Whether the button is disabled and non-interactive @default false.
 * @property {boolean} loading - Whether to show loading spinner and disable interaction @default false.
 * @property {Function} onPress - Callback function executed when the button is pressed .
 * @property {ViewStyle} containerStyle - Additional styles to apply to the button container.
 * @property {TextStyle} textStyle - Additional styles to apply to the button text.
 * @property {Object} theme -  Custom theme object to override default colors.
 */
interface CButtonProps {
  title: string;
  variant?: Variant;
  isDisabled?: boolean;
  loading?: boolean;
  onPress?: (event: GestureResponderEvent) => void;
  containerStyle?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  theme?: {
    colors: {
      primary: string;
      secondary: string;
      dark: string;
    };
  };
}

const defaultTheme = {
  colors: {
    primary: '#007BFF',
    secondary: '#707070',
    dark: '#2B2B2B',
  },
};

/**
 * @component
 * @category Components
 * @description
 * A customizable button component with multiple variants, loading states, and theme support.
 * @param {CButtonProps} props - The properties for the CButton component
 * @returns {JSX.Element} - Returns the Custom button component.
 */
const CButton: React.FC<CButtonProps> = ({
  title,
  variant = 'primary',
  isDisabled = false,
  loading = false,
  onPress,
  containerStyle,
  textStyle,
}) => {
  const theme = useThemeStore() || defaultTheme;
  const styles = createStyles(theme);

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      style={[
        styles.base,
        styles[variant],
        (isDisabled || loading) && styles.disabled,
        containerStyle,
      ]}
      onPress={onPress}
      disabled={isDisabled || loading}>
      <View style={styles.contentContainer}>
        <Text
          style={[
            styles.text,
            textStyle,
            {
              opacity: loading ? 0 : 1,
              color: variant === 'active' ? theme.colors.black : '',
            },
          ]}>
          {title}
        </Text>
        <ActivityIndicator
          style={[styles.loader, {opacity: loading ? 1 : 0}]}
          size="small"
          color={theme.colors.text}
        />
      </View>
    </TouchableOpacity>
  );
};

export default CButton;
