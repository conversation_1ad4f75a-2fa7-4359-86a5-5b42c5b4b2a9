// components/RatingSlider.tsx
import React, {useMemo} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import Slider from '@react-native-community/slider';
import Typography from './Typography';
import {useThemeStore} from '@/store';

interface RatingSliderProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
}

const RatingSlider: React.FC<RatingSliderProps> = ({
  value,
  onChange,
  min = 0,
  max = 16.5,
  step = 0.1,
}) => {
  const theme = useThemeStore();

  const styles = useMemo(
    () =>
      StyleSheet.create({
        range: {
          color: theme.colors.offWhite,
        },
        sliderContainer: {
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 10,
        },
        slider: {
          width: '92%',
          backgroundColor: '#ddd',
          borderRadius: 16,
          height: 26,
          marginTop: 5,
        },
        value: {
          color: theme.colors.offWhite,
          marginLeft: 8,
          fontSize: 14,
          fontWeight: 'bold',
        },
      }),
    [theme],
  );

  return (
    <View style={{paddingVertical: 16}}>
      <Typography variant="subtitle" color={theme.colors.offWhite}>
        UTR Rating{' '}
        <Text style={styles.range}>
          ({min} - {max})
        </Text>
      </Typography>
      <View style={styles.sliderContainer}>
        <Slider
          style={styles.slider}
          minimumValue={min}
          maximumValue={max}
          step={step}
          value={value}
          onValueChange={onChange}
          minimumTrackTintColor="#ccc"
          maximumTrackTintColor="#ccc"
          thumbTintColor="#000"
        />
        <Text style={styles.value}>{value.toFixed(1)}</Text>
      </View>
    </View>
  );
};

export default RatingSlider;
