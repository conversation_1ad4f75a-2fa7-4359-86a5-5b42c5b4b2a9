import React, {useState, useEffect} from 'react';
import {View, TouchableOpacity, ScrollView, ActivityIndicator, RefreshControl} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {CustomModal, Header, Icon, NoData} from '@/components';
import {OfferBanner} from '@/components/common/OfferBanner';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation';
import NotificationCard from '@/components/NotificationCard';
import {Images} from '@/config';
import {SwipeListView} from 'react-native-swipe-list-view';
import Typography from '@/components/Typography';
import {styles as createStyles} from './styles';
import {SafeAreaView} from '@/components/common';
import useTranslation from '@/hooks/useTranslation';
import {
  getNotificationsList,
  markNotificationAsRead,
  deleteNotification,
  markAllNotificationsAsRead,
} from '@/services/notificationsApi';
import DeleteAccountModal, {DeleteAccountModalHandles} from '@/components/DeleteAccountModal';
import {useNotificationStore} from '@/store/notificationStore';
import {useMutation} from '@tanstack/react-query';
import CLoader from '@/components/CLoader';
import {ImageSourcePropType} from 'react-native';

type NavigationProp = StackNavigationProp<RootStackParamList>;

/**
 * @category Interfaces
 * @typedef {Object} Notification
 * @property {string} id - Unique identifier for the notification
 * @property {string} title - Display title of the notification
 * @property {string} type - Type/category of the notification
 * @property {ImageSourcePropType} image - Image source for the notification icon
 * @property {string} highlight - Whether the notification should be highlighted
 * @property {string} created_at - Display time for the notification
 * @property {string} community - Community name associated with the notification
 * @property {boolean} is_read - Whether the notification has been read (1 for read, 0 for unread)
 */
interface Notification {
  id: string;
  title: string;
  type: string;
  image: ImageSourcePropType;
  highlight?: boolean;
  created_at?: string;
  community?: string;
  is_read?: boolean;
}

/**
 * @category Interfaces
 * @typedef {Object} PaginationNotification
 * @property {number} page - Current page number
 * @property {boolean} loadMore - Whether more data can be loaded
 */
interface PaginationState {
  page: number;
  loadMore: boolean;
}

/**
 * @component
 * @category Screens
 *
 * @description
 * Displays a list of notifications with swipe-to-delete functionality,
 * mark as read/unread capabilities, and pagination support.
 *
 * @return {JSX.Element} The notifications list screen component
 */
const NotificationsListScreen = () => {
  const theme = useThemeStore();
  const navigation = useNavigation<NavigationProp>();
  const {t} = useTranslation();
  const [listData, setListData] = useState<Notification[]>([]);
  const styles = createStyles(theme);
  const [openRowKey, setOpenRowKey] = useState<string | null>(null);

  const [loader, setLoader] = useState<boolean>(false);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedNotificationId, setSelectedNotificationId] = useState<string | null>(null);
  const [paginationLoader, setPaginationLoader] = useState(false);
  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    loadMore: false,
  });
  const [refreshLoader, setRefreshLoader] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteLoader, setDeleteLoader] = useState(false);
  const deleteModalRef = React.useRef<DeleteAccountModalHandles>(null);

  const notificationStore = useNotificationStore();
  const fetchUnreadCount = notificationStore.fetchUnreadCount;

  /**
   * @function markAsReadMutation
   * @memberof NotificationsListScreen
   * @description
   * Mutation for marking a single notification as read
   * Updates local state and refreshes unread count on success
   * @param {string} notificationId - Id of notification
   */
  const markAsReadMutation = useMutation({
    mutationFn: (notificationId: string) => markNotificationAsRead(notificationId),
    onSuccess: (_, notificationId) => {
      setListData(prevData =>
        prevData.map(notification =>
          notification.id === notificationId ? {...notification, is_read: true} : notification,
        ),
      );
      // Update unread count after marking as read
      fetchUnreadCount();
    },
    onError: error => {
      console.error('Error marking notification as read:', error);
    },
  });

  /**
   * @function deleteNotificationMutation
   * @memberof NotificationsListScreen
   * @description
   * Mutation for deleting a notification
   * Removes notification from local state and refreshes unread count on success
   * @param {string} notificationId - Id of notification
   */
  const deleteNotificationMutation = useMutation({
    mutationFn: (notificationId: string) => deleteNotification(notificationId),
    onSuccess: (_, notificationId) => {
      setListData(prevData => prevData.filter(notification => notification.id !== notificationId));
      setIsModalVisible(false);
      setSelectedNotificationId(null);
      deleteModalRef.current?.close();
      fetchUnreadCount();
    },
    onError: error => {
      console.error('Error deleting notification:', error);
    },
  });

  /**
   * @function markAllAsReadMutation
   * @memberof NotificationsListScreen
   * @description
   * Mutation for marking all notifications as read
   * Updates all notifications in local state to read status
   */
  const markAllAsReadMutation = useMutation({
    mutationFn: () => markAllNotificationsAsRead(),
    onSuccess: () => {
      setListData(prevData =>
        prevData.map(notification => ({
          ...notification,
          is_read: true,
        })),
      );
      fetchUnreadCount();
    },
    onError: error => {
      console.error('Error marking all notifications as read:', error);
    },
  });

  /**
   * @function fetchNotifications
   * @memberof NotificationsListScreen
   * @description
   * Fetches notifications from the API with pagination support
   * @param {number} page - The page number to fetch
   * @param {boolean} [isRefresh=false] - Whether this is a refresh operation
   * @param {boolean} [loader=false] - Whether to show the main loader
   * @returns {Promise<void>}
   */
  const fetchNotifications = async (page: number, isRefresh = false, loader = false) => {
    if (loader) {
      setLoader(true);
    }
    try {
      if (!isRefresh) setPaginationLoader(true);
      const response = await getNotificationsList(page, 'all');
      const newNotifications = response?.data || [];

      setListData(prevData => (isRefresh ? newNotifications : [...prevData, ...newNotifications]));
      setPagination(prev => ({
        ...prev,
        page,
        loadMore: newNotifications.length > 0, // Stop loading if no more data
      }));
      setLoader(false);
      // Fetch unread count after fetching notifications
      await fetchUnreadCount();
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setLoader(false);
    } finally {
      setPaginationLoader(false);
      setRefreshLoader(false);
      setLoader(false);
    }
  };

  // Load more data for pagination
  const loadMoreData = () => {
    if (pagination.loadMore && !paginationLoader) {
      fetchNotifications(pagination.page + 1, false, false);
    }
  };

  // Refresh notifications
  const refreshNotifications = () => {
    setRefreshLoader(true);
    fetchNotifications(1, true, false);
  };

  useEffect(() => {
    fetchNotifications(1, true, true);
  }, []);

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  /**
   * @function handleNotificationNavigation
   * @memberof NotificationsListScreen
   * @description
   * Handles navigation based on notification type and action data
   * @param {Object} notification - The notification object containing navigation data
   */

  const handleNotificationNavigation = (notification: any) => {
    const actionData = JSON.parse(notification?.extra_data);

    if (!notification || !notification.extra_data) return;
    const {action_type, channel_url, group_id} = actionData;

    switch (action_type) {
      case 'chat_detail':
        navigation.navigate('SendBird', {
          channelUrl: channel_url,
          group_id,
        });
        break;
      case 'group_join_request':
        navigation.navigate('RequestList', {groupId: group_id});
        break;
      case 'accepted_group_join_request':
        navigation.navigate('SendBird', {
          channelUrl: channel_url,
          group_id,
        });
        break;
      case 'rejected_group_join_request':
        navigation.navigate('JoinGroupDetails', {id: group_id, from: 'my-groups'});
        break;
      default:
        break;
    }
  };

  /**
   * @function handleNotificationPress
   * @memberof NotificationsListScreen
   * @description
   * Handles notification press events
   * Marks unread notifications as read and navigates to the appropriate screen
   * @param {Object} Notification - The notification that was pressed
   */
  const handleNotificationPress = async (item: Notification) => {
    if (item?.is_read === 1) {
      handleNotificationNavigation(item);
    } else {
      try {
        if (!item.is_read) {
          // Update UI state immediately
          setListData(prevData =>
            prevData.map(notification =>
              notification.id === item.id ? {...notification, is_read: true} : notification,
            ),
          );
          markAsReadMutation.mutate(item.id);
        }
        console.log('item.id=====>>>>>', item.id);
      } catch (error) {
        console.error('Error marking notification as read:', error);
      }
      handleNotificationNavigation(item);
    }
  };

  const handleDeleteNotification = async (notificationId: string) => {
    setDeleteLoader(true);
    try {
      await deleteNotificationMutation.mutateAsync(notificationId);
    } finally {
      setDeleteLoader(false);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsReadMutation.mutateAsync();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const handleDeletePress = (notificationId: string) => {
    setSelectedNotificationId(notificationId);
    setShowDeleteModal(true);
    setIsModalVisible(false);
    setTimeout(() => {
      deleteModalRef.current?.open();
    }, 100);
  };

  const renderItem = ({item}: {item: Notification}) => (
    <View style={[styles.rowFront, openRowKey === item.id && styles.rowFrontOpen]}>
      <View style={styles.cardContainer}>
        <NotificationCard
          title={item.title}
          type={item.type}
          image={item.image || Images.dunlopRound}
          time={item.created_at}
          community={item.community}
          isRead={item.is_read}
          onNotificationPress={() => handleNotificationPress(item)}
        />
      </View>
    </View>
  );

  const renderHiddenItem = (data: {item: Notification}) => (
    <View style={styles.rowBack}>
      {/* <TouchableOpacity
        style={[styles.actionButton, styles.moreBtn]}
        onPress={() => {
          setSelectedNotificationId(data.item.id);
          setIsModalVisible(true);
        }}>
        <Icon name="threeDot" size={28} color={theme.colors.white} />
        <Typography variant="communityBtnText" style={styles.actionText}>
          {t('NotificationScreen.more')}
        </Typography>
      </TouchableOpacity> */}
      <TouchableOpacity
        style={[styles.actionButton, styles.trashBtn]}
        activeOpacity={0.7}
        onPress={() => handleDeletePress(data.item.id)}>
        <Icon name="delete" size={30} color={theme.colors.white} />
        <Typography variant="communityBtnText" style={styles.actionText}>
          {t('NotificationScreen.trash')}
        </Typography>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header
        leftComponent={
          <TouchableOpacity onPress={() => navigation.goBack()} activeOpacity={0.7}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
        }
        leftIconButtonStyle={styles.menuButton}
        onLeftPress={openDrawer}
        // rightIcons={[
        //   {name: 'notification', size: 24, badge: unreadCount},
        //   {name: 'chat', size: 24},
        // ]}
        title={t('NotificationScreen.title')}
        backgroundColor={theme.colors.notificationBg}
        transparent={false}
        showBack={false}
      />
      {listData.length > 0 && listData?.some(obj => obj.is_read !== 1) && (
        <View style={styles.markAllAsReadContainer}>
          <TouchableOpacity
            onPress={handleMarkAllAsRead}
            activeOpacity={0.7}
            style={styles.markAllAsReadButton}>
            <Typography variant="bodyMedium" align="right" color={theme.colors.white}>
              {t('NotificationScreen.all_read')}
            </Typography>
          </TouchableOpacity>
        </View>
      )}
      {loader ? (
        <CLoader />
      ) : (
        <SwipeListView
          data={listData}
          renderItem={renderItem}
          renderHiddenItem={renderHiddenItem}
          rightOpenValue={-70}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
          closeOnRowPress={true}
          disableRightSwipe={true}
          showsVerticalScrollIndicator={false}
          friction={100}
          tension={100}
          onEndReachedThreshold={0.5}
          swipeToOpenPercent={30}
          onEndReached={loadMoreData}
          onRowOpen={rowKey => {
            setOpenRowKey(rowKey);
          }}
          onRowClose={() => {
            setOpenRowKey(null);
          }}
          ListEmptyComponent={() => <NoData />}
          ListFooterComponent={() =>
            paginationLoader ? (
              <View style={styles.paginationLoader}>
                <ActivityIndicator size="large" color={theme.colors.primary} />
              </View>
            ) : null
          }
          refreshControl={
            <RefreshControl
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
              refreshing={refreshLoader}
              onRefresh={refreshNotifications}
            />
          }
        />
      )}

      <View style={styles.footer}>
        <OfferBanner text="Invite friends, get 10% off" />
      </View>
      <CustomModal
        visible={isModalVisible}
        variant="bottom"
        modalContainerStyle={{
          height: '100%',
          width: '100%',
        }}
        showCloseButton
        onClose={() => {
          setIsModalVisible(false);
          setSelectedNotificationId(null);
        }}>
        <ScrollView contentContainerStyle={styles.modalContent}>
          <View style={styles.modalRow}>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => {
                setIsModalVisible(false);
                // Add reply functionality here
              }}>
              <View style={styles.iconContainer}>
                <Icon name="reply" size={30} color={theme.colors.white} />
              </View>
              <Typography
                variant="notificationText"
                color={theme.colors.white}
                style={{
                  marginTop: 5,
                }}>
                {t('NotificationScreen.reply')}
              </Typography>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => {
                if (selectedNotificationId) {
                  setIsModalVisible(false);
                  handleDeletePress(selectedNotificationId);
                }
              }}>
              <View style={styles.iconContainer}>
                <Icon name="delete" size={32} color={theme.colors.white} />
              </View>
              <Typography
                variant="notificationText"
                color={theme.colors.white}
                style={{
                  marginTop: 5,
                }}>
                {t('NotificationScreen.trash')}
              </Typography>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.modalFullButton}
            onPress={() => {
              setIsModalVisible(false);
            }}>
            <Typography variant="notificationText" color={theme.colors.white}>
              {t('NotificationScreen.remindMe')}
            </Typography>
          </TouchableOpacity>

          {/* Multiple notification function buttons */}
          {[1, 2, 3, 4].map(item => (
            <TouchableOpacity
              key={`notification-function-${item}`}
              style={styles.modalFullButton}
              onPress={() => {
                setIsModalVisible(false);
                // Add notification function here
              }}>
              <Typography variant="notificationText" color={theme.colors.white}>
                {t('NotificationScreen.notificationFunction')}
              </Typography>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </CustomModal>
      <DeleteAccountModal
        ref={deleteModalRef}
        type="deleteNotification"
        onCancel={() => {
          deleteModalRef.current?.close();
          setShowDeleteModal(false);
          setSelectedNotificationId(null);
        }}
        onConfirm={() => {
          if (selectedNotificationId) {
            handleDeleteNotification(selectedNotificationId);
          }
        }}
        loader={deleteLoader}
      />
    </SafeAreaView>
  );
};

export default NotificationsListScreen;
