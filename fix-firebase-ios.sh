#!/bin/bash

# Firebase iOS Build Fix Script
# This script resolves all Firebase conflicts and sets up a clean environment

echo "🔧 Starting Firebase iOS Build Fix..."

# Step 1: Clean iOS build artifacts
echo "📱 Cleaning iOS build artifacts..."
cd ios
rm -rf build/
rm -rf Pods/
rm -f Podfile.lock
rm -rf ~/Library/Developer/Xcode/DerivedData/GoRaqtApp-*
cd ..

# Step 2: Clean React Native cache
echo "🧹 Cleaning React Native cache..."
npx react-native clean
rm -rf node_modules/
rm -f yarn.lock

# Step 3: Reinstall dependencies
echo "📦 Reinstalling dependencies..."
yarn install

# Step 4: Reinstall iOS pods
echo "🍎 Reinstalling iOS pods..."
cd ios
pod deintegrate
pod install --repo-update
cd ..

# Step 5: Reset Metro cache
echo "🚇 Resetting Metro cache..."
npx react-native start --reset-cache &
METRO_PID=$!
sleep 5
kill $METRO_PID

echo "✅ Firebase iOS Build Fix completed!"
echo ""
echo "🚀 Next steps:"
echo "1. Open Xcode and clean build folder (Cmd+Shift+K)"
echo "2. Run: yarn ios"
echo "3. Test push notifications"
echo ""
echo "📋 Verification checklist:"
echo "- ✅ No duplicate Firebase dependencies"
echo "- ✅ React Native Firebase only"
echo "- ✅ Static frameworks enabled"
echo "- ✅ Clean AppDelegate.swift"
echo "- ✅ Proper GoogleService-Info.plist"
