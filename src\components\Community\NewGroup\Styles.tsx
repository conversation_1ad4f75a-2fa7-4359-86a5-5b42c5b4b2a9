import FONTS, {FONT_SIZE} from '@/utils/fonts';
import {Dimensions, StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    content: {
      flexGrow: 1,
      padding: 16,
      backgroundColor: theme.colors.background,
    },
    cardContainer: {
      gap: 20,
    },
    headerContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginVertical: 16,
    },
    headerTitle: {
      color: theme.colors.white,
    },

    errorText: {
      color: theme.colors.coralRed,
      fontSize: theme.fontSize.small,
    },
    imageContainer: {
      height: Dimensions.get('screen').height * 0.25,
      width: '100%',
    },
    actionContainer: {
      backgroundColor: theme.colors.black,
      paddingHorizontal: 30,
      paddingVertical: 6,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 20,
    },

    searchContainer: {
      backgroundColor: theme.colors.white,
    },

    searchBar: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.white,
      borderRadius: 8,
      paddingHorizontal: 20,
      paddingVertical: 10,
      gap: 20,
      borderWidth: 1,
      borderColor: theme.colors.primary,
    },
    searchIcon: {
      marginRight: 8,
      fontSize: 16,
    },
    searchInput: {
      color: theme.colors.primary,
    },
    btn: {
      width: '100%',
      borderWidth: 0,
      marginTop: 20,
    },
    headerRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 10,
      paddingHorizontal: 16,
      paddingBottom: 10,
    },
    headerBack: {
      fontSize: 18,
      fontWeight: 'bold',
    },
    headerCreate: {
      fontSize: 18,
      fontWeight: 'bold',
    },
    groupNameContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.placeholder,
      borderRadius: 10,
      padding: 16,
      gap: 5,
    },
    cameraCircle: {
      width: 51,
      height: 51,
      borderRadius: 40,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.white,
    },
    imageContent: {
      justifyContent: 'center',
      alignItems: 'center',
      width: 50,
      height: 50,
    },
    groupNameInput: {
      color: theme.colors.white,
      borderWidth: 0,
      marginBottom: 0,
    },
    sectionLabel: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.white,
      marginTop: 16,
    },

    privacyRow: {
      flexDirection: 'row',
      justifyContent: 'space-evenly',
      marginVertical: 5,
      borderWidth: 1,
      borderColor: theme.colors.placeholder,
      borderRadius: 10,
      paddingHorizontal: 10,
      paddingVertical: 8,
      gap: 10,
    },
    privacyButton: {
      flex: 1,
      marginHorizontal: 4,
      padding: 12,
      borderRadius: 8,
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
    },
    privacyButtonSelected: {
      backgroundColor: theme.colors.white,
    },
    privacyButtonText: {
      color: theme.colors.white,
      fontWeight: 'bold',
    },
    privacyButtonTextSelected: {
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.placeholder,
      borderRadius: 8,
      paddingHorizontal: 20,
      color: theme.colors.text,
      height: 57,
    },
    inputText: {
      fontWeight: '400',
      fontSize: 16,
    },
    label: {
      color: theme.colors.white,
      fontFamily: FONTS.medium,
      fontSize: FONT_SIZE.xl,
      marginTop: 10,
    },
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    container: {
      flex: 1,
    },
    dropdownContainer: {
      width: '100%',
      borderRadius: 10,
    },
    dropdownMain: {
      borderWidth: 1,
      borderColor: theme.colors.placeholder,
      borderRadius: 10,
      height: 57,
    },
    dropdownContent: {
      marginTop: 35,
      borderWidth: 1,
      borderColor: theme.colors.placeholder,
      borderRadius: 10,
    },
    placeholderText: {
      color: theme.colors.activeColor,
    },
    collapsibleTitle: {
      color: theme.colors.text,
      fontSize: 14,
      flex: 1,
    },
    collapsibleContainer: {
      borderWidth: 1,
      borderRadius: 12,
      padding: 15,
      borderColor: theme.colors.placeholder,
    },
    addMembersBtn: {
      // marginTop: 20,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 2,
    },
    memberList: {
      gap: 24,
      paddingVertical: 10,
    },
    memberAvatar: {
      width: 64,
      height: 64,
      borderRadius: 32,
    },
    memberContainer: {
      alignItems: 'center',
      marginBottom: 10,
      // width: 70,
      // flex: 1,
      // justifyContent: 'center',
    },
    memberAvatarContainer: {
      position: 'relative',
    },
    userName: {
      marginTop: 6,
      fontSize: 14,
      textAlign: 'center',
    },

    selectedMembersContainer: {
      marginTop: 16,
    },
    selectedMembersTitle: {
      marginBottom: 10,
    },
    closeButton: {
      position: 'absolute',
      top: 0,
      right: -7,
      backgroundColor: theme.colors.inputLabel,
      borderRadius: 12,
      width: 20,
      height: 20,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: theme.colors.background,
    },
    addMembersContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: 20,
    },
    inputStyle: {
      backgroundColor: 'transparent',
    },
    textArea: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: theme.colors.placeholder,
      borderRadius: 10,
      color: theme.colors.white,
      width: '100%',
      minHeight: 150,
      justifyContent: 'flex-start',
      textAlignVertical: 'top',
      paddingLeft: 15,
      paddingTop: 15,
      fontSize: 16,
    },
  });
