import React from 'react';
import {useThemeStore} from '@/store/themeStore';
import {View, TouchableOpacity, Image, ActivityIndicator} from 'react-native';
import {styles as createStyles} from './styles';
import Typography from '@/components/Typography';
import {CImage, Icon} from '@/components';
import {Images} from '@/config';

interface PlayerCardProps {
  playerData: {
    id?: string;
    name: string;
    display_name?: string;
    rating: string;
    location: string;
    image: string | number; // string for URI, number for require
    profile_pic: string;
    color?: string;
    isPremium?: boolean;
    userData?: {
      location?: string;
    };
    status?: 'available' | 'busy' | 'in-match'; // Add status property
  };
  onSelect?: () => void;
  isSelected?: boolean;
  borderColor?: string;
  showAdd?: boolean;
  showActionButton?: boolean;
  onAcceptPress?: () => void; // Assuming this prop is used elsewhere
  onRejectPress?: () => void; // Assuming this prop is used elsewhere
  actionLoading?: boolean; // Assuming this prop is used elsewhere
}

const PlayerCard: React.FC<PlayerCardProps> = ({
  playerData,
  onSelect,
  isSelected = false,
  borderColor = '',
  showAdd = true,
  showActionButton = false, // Assuming this prop is used elsewhere
  onAcceptPress = () => {}, // Default function if not provided
  onRejectPress = () => {}, // Default function if not provided
  actionLoading = false,
}) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);

  // Render either CImage for remote images or Image for local images
  const renderImage = () => {
    if (typeof playerData.image === 'string') {
      return (
        <CImage
          source={{
            uri:
              playerData.profile_pic ||
              'https://goraqt-storage.s3.us-east-1.amazonaws.com/user/user_photo.jpg',
          }}
          style={styles.avatar}
          fallbackSource={Images.profile1}
        />
      );
    }
    return (
      <Image
        source={{
          uri:
            playerData.profile_pic ||
            'https://goraqt-storage.s3.us-east-1.amazonaws.com/user/user_photo.jpg',
        }}
        style={styles.avatar}
        resizeMode="cover"
      />
    );
  };

  // Get status indicator color based on player status
  const getStatusColor = () => {
    switch (playerData.status) {
      case 'available':
        return theme.colors.lime; // Green for available
      case 'busy':
      case 'in-match':
        return theme.colors.red; // Red for busy/in-match
      default:
        return 'transparent'; // No indicator if status is not specified
    }
  };

  return (
    <View
      style={[
        styles.card,
        {borderColor: borderColor && isSelected ? borderColor : theme.colors.secondary},
      ]}>
      {/* Avatar with Status Indicator */}
      <View style={styles.avatarContainer}>
        {renderImage()}

        {/* Status Indicator */}
        {playerData.status && (
          <View style={[styles.statusIndicator, {backgroundColor: getStatusColor()}]} />
        )}
      </View>

      {/* Info */}
      <View style={styles.infoContainer}>
        <View style={styles.nameRow}>
          <Typography
            variant="playerTitle"
            color={playerData?.isPremium ? theme.colors.orange : theme.colors.text}>
            {playerData?.name || playerData?.display_name || playerData?.user?.name}
          </Typography>
        </View>

        <View
          style={[
            styles.locationRow,
            {
              width: '80%',
            },
          ]}>
          <Icon name="location-pin" size={22} color={theme?.colors?.activeColor} />
          <Typography
            variant="caption"
            numberOfLines={2}
            style={{
              lineHeight: 15,
            }}
            color={playerData?.isPremium ? theme.colors.orange : theme.colors.text}>
            {playerData?.userData?.location}
          </Typography>
        </View>
      </View>

      {showActionButton && (
        <View style={styles.actionButtonsContainer}>
          <TouchableOpacity style={[styles.actionBtn]} onPress={onAcceptPress}>
            {actionLoading ? (
              <ActivityIndicator size="small" color={theme.colors.white} />
            ) : (
              <Icon name={'check'} size={20} color={theme.colors.text} />
            )}
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.plusButton,
              {
                backgroundColor: theme.colors.error,
              },
            ]}
            onPress={onRejectPress}>
            <Icon name={'close'} size={18} color={theme.colors.white} />
          </TouchableOpacity>
        </View>
      )}

      {/* Checkbox / Plus Button */}
      {showAdd && (
        <TouchableOpacity
          style={[
            styles.plusButton,
            {
              backgroundColor: isSelected ? theme.colors.activeColor : theme.colors.primary,
            },
          ]}
          onPress={onSelect}>
          <Icon
            name={isSelected ? 'check' : 'plus'}
            size={isSelected ? 26 : 25}
            color={isSelected ? theme.colors.background : theme?.colors?.text}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default PlayerCard;
