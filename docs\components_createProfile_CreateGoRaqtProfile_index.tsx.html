

<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> components/createProfile/CreateGoRaqtProfile/index.tsx</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="styles/style.css">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
             
                <a href="index.html">
                    <h1 class="navbar-item">GoRaqt Documentation</h1>
                </a>
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    Documentation
                </a>
                
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
                <div class="search-wrapper">
                    <input id="search" type="text" placeholder="Search docs..." class="input">
                </div>
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Global</h3><ul><li><a href="global.html#HIDE_TAB_SCREENS">HIDE_TAB_SCREENS</a></li><li><a href="global.html#SENSITIVE_FIELDS">SENSITIVE_FIELDS</a></li><li><a href="global.html#addBreadcrumb">addBreadcrumb</a></li><li><a href="global.html#apiCall">apiCall</a></li><li><a href="global.html#authenticateWithBiometrics">authenticateWithBiometrics</a></li><li><a href="global.html#calculateAge">calculateAge</a></li><li><a href="global.html#checkAuthStatusApi">checkAuthStatusApi</a></li><li><a href="global.html#checkLocationPermission">checkLocationPermission</a></li><li><a href="global.html#cleanMemoryIfNeeded">cleanMemoryIfNeeded</a></li><li><a href="global.html#clearAllData">clearAllData</a></li><li><a href="global.html#configureStatusBar">configureStatusBar</a></li><li><a href="global.html#createHealthService">createHealthService</a></li><li><a href="global.html#deleteFcmTokenFromDB">deleteFcmTokenFromDB</a></li><li><a href="global.html#deleteNotification">deleteNotification</a></li><li><a href="global.html#disableBiometrics">disableBiometrics</a></li><li><a href="global.html#enableBiometrics">enableBiometrics</a></li><li><a href="global.html#forgotPasswordApi">forgotPasswordApi</a></li><li><a href="global.html#formatBirthdate">formatBirthdate</a></li><li><a href="global.html#getAddressFromCoordinates">getAddressFromCoordinates</a></li><li><a href="global.html#getCurrentLocationWithAddress">getCurrentLocationWithAddress</a></li><li><a href="global.html#getCurrentPosition">getCurrentPosition</a></li><li><a href="global.html#getCurrentRegion">getCurrentRegion</a></li><li><a href="global.html#getCurrentUserApi">getCurrentUserApi</a></li><li><a href="global.html#getNotificationsList">getNotificationsList</a></li><li><a href="global.html#getTabBarVisibility">getTabBarVisibility</a></li><li><a href="global.html#getUnreadNotificationsCount">getUnreadNotificationsCount</a></li><li><a href="global.html#getUserDetails">getUserDetails</a></li><li><a href="global.html#getUserProfile">getUserProfile</a></li><li><a href="global.html#handleNotificationNavigation">handleNotificationNavigation</a></li><li><a href="global.html#handleTextureError">handleTextureError</a></li><li><a href="global.html#initSentry">initSentry</a></li><li><a href="global.html#isBiometricsAvailable">isBiometricsAvailable</a></li><li><a href="global.html#isBiometricsEnabled">isBiometricsEnabled</a></li><li><a href="global.html#lastCacheClearTime">lastCacheClearTime</a></li><li><a href="global.html#logError">logError</a></li><li><a href="global.html#logMessage">logMessage</a></li><li><a href="global.html#loginWithEmail">loginWithEmail</a></li><li><a href="global.html#loginWithEmailApi">loginWithEmailApi</a></li><li><a href="global.html#logoutApi">logoutApi</a></li><li><a href="global.html#markAllNotificationsAsRead">markAllNotificationsAsRead</a></li><li><a href="global.html#markNotificationAsRead">markNotificationAsRead</a></li><li><a href="global.html#navigateBackFromExample">navigateBackFromExample</a></li><li><a href="global.html#navigateToOptimizedFlatListExample">navigateToOptimizedFlatListExample</a></li><li><a href="global.html#otpResendApi">otpResendApi</a></li><li><a href="global.html#otpVerifyApi">otpVerifyApi</a></li><li><a href="global.html#prefetchInitialQueries">prefetchInitialQueries</a></li><li><a href="global.html#prefetchPost">prefetchPost</a></li><li><a href="global.html#prefetchUser">prefetchUser</a></li><li><a href="global.html#refreshTokenApi">refreshTokenApi</a></li><li><a href="global.html#registerWithEmail">registerWithEmail</a></li><li><a href="global.html#registerWithEmailApi">registerWithEmailApi</a></li><li><a href="global.html#requestLocationPermission">requestLocationPermission</a></li><li><a href="global.html#resetPasswordApi">resetPasswordApi</a></li><li><a href="global.html#saveFcmTokenInDB">saveFcmTokenInDB</a></li><li><a href="global.html#schema">schema</a></li><li><a href="global.html#scrubObject">scrubObject</a></li><li><a href="global.html#scrubSensitiveData">scrubSensitiveData</a></li><li><a href="global.html#setCustomStatusBar">setCustomStatusBar</a></li><li><a href="global.html#setDarkStatusBar">setDarkStatusBar</a></li><li><a href="global.html#setLightStatusBar">setLightStatusBar</a></li><li><a href="global.html#setNavigationContext">setNavigationContext</a></li><li><a href="global.html#setStatusBarHidden">setStatusBarHidden</a></li><li><a href="global.html#setUserContext">setUserContext</a></li><li><a href="global.html#setupMemoryCleaning">setupMemoryCleaning</a></li><li><a href="global.html#setupTextureErrorHandlers">setupTextureErrorHandlers</a></li><li><a href="global.html#signInWithFacebook">signInWithFacebook</a></li><li><a href="global.html#signInWithGoogle">signInWithGoogle</a></li><li><a href="global.html#startTransaction">startTransaction</a></li><li><a href="global.html#styles">styles</a></li><li><a href="global.html#updateUserProfile">updateUserProfile</a></li><li><a href="global.html#useAuthStatus">useAuthStatus</a></li><li><a href="global.html#useCurrentUser">useCurrentUser</a></li><li><a href="global.html#useLogin">useLogin</a></li><li><a href="global.html#useRegister">useRegister</a></li><li><a href="global.html#useSafeArea">useSafeArea</a></li><li><a href="global.html#useSocialLogin">useSocialLogin</a></li><li><a href="global.html#useUpdateUserProfile">useUpdateUserProfile</a></li><li><a href="global.html#useUserProfile">useUserProfile</a></li></ul></div><div class="category"><h2>Components</h2><h3>Components</h3><ul><li><a href="Advertisement.html">Advertisement</a></li><li><a href="BiometricGate.html">BiometricGate</a></li><li><a href="CButton.html">CButton</a></li><li><a href="CreateGoRaqtProfile.html">CreateGoRaqtProfile</a></li><li><a href="CustomMapView.html">CustomMapView</a></li><li><a href="CustomMarker.html">CustomMarker</a></li><li><a href="NotificationCard.html">NotificationCard</a></li><li><a href="OfferBanner.html">OfferBanner</a></li><li><a href="Subscription.html">Subscription</a></li></ul></div><div class="category"><h2>Interfaces</h2><h3>Interfaces</h3><ul><li><a href="AdvertisementProps.html">AdvertisementProps</a></li><li><a href="ApiResponse.html">ApiResponse</a></li><li><a href="BiometricGateProps.html">BiometricGateProps</a></li><li><a href="CButtonProps.html">CButtonProps</a></li><li><a href="CreateProfileProps.html">CreateProfileProps</a></li><li><a href="CustomMarkerProps.html">CustomMarkerProps</a></li><li><a href="DrawerItemType.html">DrawerItemType</a></li><li><a href="ForgotPasswordScreenProps.html">ForgotPasswordScreenProps</a></li><li><a href="Notification.html">Notification</a></li><li><a href="NotificationCardProps.html">NotificationCardProps</a></li><li><a href="PaginationState.html">PaginationState</a></li><li><a href="PermissionsScreenProps.html">PermissionsScreenProps</a></li><li><a href="RouteParams.html">RouteParams</a></li><li><a href="SentryNavigationContainerProps.html">SentryNavigationContainerProps</a></li><li><a href="SocialRegisterRequest.html">SocialRegisterRequest</a></li><li><a href="SplashScreenProps.html">SplashScreenProps</a></li><li><a href="TermsOfServiceScreenProps.html">TermsOfServiceScreenProps</a></li><li><a href="UseLogoutReturn.html">UseLogoutReturn</a></li></ul><h3><a href="global.html">Global</a></h3></div><div class="category"><h2>Navigation</h2><h3>Components</h3><ul><li><a href="DrawerNavigator.html">DrawerNavigator</a></li><li><a href="SentryNavigationContainer.html">SentryNavigationContainer</a></li></ul></div><div class="category"><h2>Screens</h2><h3>Components</h3><ul><li><a href="CommunityDetails.html">CommunityDetails</a></li><li><a href="DirectFetchTest.html">DirectFetchTest</a></li><li><a href="EmailSignupScreen.html">EmailSignupScreen</a></li><li><a href="ForgotPasswordScreen.html">ForgotPasswordScreen</a></li><li><a href="LoginScreen.html">LoginScreen</a></li><li><a href="MyMatchesScreen.html">MyMatchesScreen</a></li><li><a href="NotificationsListScreen.html">NotificationsListScreen</a></li><li><a href="PermissionsScreen.html">PermissionsScreen</a></li><li><a href="SignupScreen.html">SignupScreen</a></li><li><a href="SplashScreen.html">SplashScreen</a></li><li><a href="TermsOfServiceScreen.html">TermsOfServiceScreen</a></li><li><a href="Verification.html">Verification</a></li></ul></div><div class="category"><h2>Utils</h2><h3>Global</h3><ul><li><a href="global.html#api">api</a></li><li><a href="global.html#socialLoginApi">socialLoginApi</a></li><li><a href="global.html#useLogout">useLogout</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Source</p>
                    <h1>components/createProfile/CreateGoRaqtProfile/index.tsx</h1>
                </header>
                



    
    <section>
        <article>
            <pre class="prettyprint source linenums"><code>import React, {useRef, useEffect, useState} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  View,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {styles as createStyles} from './styles';
import {Controller, useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {useUpdateUserProfile, useUserProfile, useCheckDisplayName} from '@/hooks/queries/useUser';
import {useAuthStore} from '@/store/authStore';
import CInput from '@components/CInput';
import CButton from '@components/CButton/index';
import RadioSelect from '@components/RadioSelect/index';
import Typography from '@/components/Typography';
import {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import {debounce, isBoolean} from 'lodash';
import useTranslation from '@/hooks/useTranslation';
import {Icon} from '@/components';
import {toaster} from '@/utils/commonFunctions';
import {useRandomName} from '@/hooks/queries/useUsers';
import {useQueryClient} from '@tanstack/react-query';
import ModalYearPicker from '@/components/ModalYearPicker';
import {createProfileRadioData} from '@/config/staticData';

/**
 * @category Interfaces
 * @typedef {Object} CreateProfileProps
 * @property {Function} completeProfile - User completed their profile.
 * @property {Function} setupLater - User want to setup profile later.
 */
interface CreateProfileProps {
  completeProfile: () => void;
  setupLater: () => void;
}

interface FormData {
  fullName: string;
  email?: string;
  birthYear: string;
  userType: string;
  fitnessLevel: string;
  randomName?: string;
}

/**
 * @component
 * @category Components
 *
 * @description Profile setup after user completed login and authenticated.
 *
 * The Component receives the following props:
 *   - completeProfile - User completed their profile.
 *   - setupLater - User want to setup profile later.
 *
 * @param {{completeProfile: Function, setupLater: Function}} props
 *
 * @return {JSX.Element} Create profile component
 */

const CreateGoRaqtProfile = (props: CreateProfileProps) => {
  const {completeProfile = () => {}, setupLater = () => {}} = props;

  const theme = useThemeStore();
  const styles = createStyles(theme);
  const {user, login, isApiStatus} = useAuthStore();
  console.log('user', user);
  const updateProfileMutation = useUpdateUserProfile();
  const {data: userProfileData, isLoading} = useUserProfile();
  const [displayName, setDisplayName] = useState('');
  const [ageStatus, setAgeStatus] = useState&lt;string>('');
  const {data: nameCheck, isLoading: isCheckingName} = useCheckDisplayName(displayName);
  // const {data: randomNames, isLoading: isLoadingRandomNames, refetch} = useGetRandomNames();
  const randomNameApi = useRandomName();
  const queryClient = useQueryClient();
  const cachedData = queryClient.getQueryData(['randomName']);

  const {t} = useTranslation();
  const fullNameInputRef = useRef&lt;TextInput>(null);
  const emailInputRef = useRef&lt;TextInput>(null);
  const birthYearInputRef = useRef&lt;TextInput>(null);

  const [showYearModal, setShowYearModal] = useState(false);

  const schema = yup.object({
    fullName: yup.string().required(t('createProfile.nameError')),
    email: yup.string().email(t('createProfile.emailFormatError')).optional(),
    birthYear: yup
      .string()
      .required(t('createProfile.birthYearRequired'))
      .matches(/^[0-9]{4}$/, t('createProfile.birthYearformatErr'))
      .test('is-valid-year', t('createProfile.birthYearRangeErr'), value => {
        const numValue = parseInt(value, 10);
        const currentYear = new Date().getFullYear();
        if (numValue > currentYear) {
          return false;
        }
        return !isNaN(numValue) &amp;&amp; numValue >= currentYear - 59 &amp;&amp; numValue &lt;= currentYear;
      }),
    userType: yup.string().required(t('createProfile.userTypeError')),
    fitnessLevel: yup.string().required(t('createProfile.fitnessLevelError')),
    randomName: yup.string().optional(),
  });

  const {
    control,
    handleSubmit,
    formState: {errors},
    setValue,
    watch,
  } = useForm&lt;FormData>({
    resolver: yupResolver&lt;FormData, any, FormData>(schema),
    defaultValues: {
      fullName: user?.display_name || user?.name || '',
      email: user?.email || '',
      birthYear: user?.birthyear ? String(user.birthyear) : '',
      userType: user?.user_type || '',
      fitnessLevel: user?.fitness_level || '',
      randomName: '',
    },
  });

  // Prefill form with user data if available
  useEffect(() => {
    if (user) {
      handleRandomName();

      setValue('fullName', user?.display_name || user?.name || '');
      setValue('email', user?.email || '');
      if (user.birthyear) {
        setValue('birthYear', String(user.birthyear));
      }
    }

    // If we have profile data from the API, use it to prefill the form
    // if (userProfileData?.data) {
    //   const profileUser = userProfileData.data;

    //   if (profileUser.name) {
    //     setValue('fullName', profileUser.name);
    //   }

    //   if (profileUser.user_type) {
    //     setValue('userType', profileUser.user_type);
    //   }

    //   if (profileUser.fitness_level) {
    //     setValue('fitnessLevel', profileUser.fitness_level);
    //   }

    //   if (profileUser.birthdate) {
    //     const age = calculateAge(profileUser.birthdate);
    //     setValue('age', String(age));
    //   }
    // }
  }, [user, userProfileData, setValue]);

  // Get the current values for the radio buttons
  const userTypeValue = watch('userType');
  const fitnessLevelValue = watch('fitnessLevel');
  const randomNameValue = watch('randomName');

  /**
   * @function onSubmit
   * @memberof CreateGoRaqtProfile
   * @description
   * Handles form submission and create profile process.
   * @param {FormData} data - Form data containing name, user type, fitness level and birth year.
   */

  const onSubmit = (data: FormData) => {
    Keyboard.dismiss();

    const birthdate = new Date();
    birthdate.setFullYear(parseInt(data.birthYear, 10));
    birthdate.setMonth(0);
    birthdate.setDate(1);

    // Prepare data for API
    const updateData = {
      name: data.fullName,
      user_type: data.userType as 'player' | 'coach' | 'both',
      fitness_level: data.fitnessLevel as
        | 'slow_and_steady'
        | 'workout_warrior'
        | 'can_keep_rally'
        | 'play_competitively',
      // birthdate: birthdate.toISOString().split('T')[0],
      birthyear: data.birthYear,
      is_chat_on: true, // Default values
      is_notification_on: true, // Default values
    };

    // Call the update profile API
    if (isApiStatus) {
      updateProfileMutation.mutate(updateData, {
        onSuccess: data => {
          if (data?.data) {
            login(data.data);
          }
          toaster('success', data.message, 'top');
          completeProfile();
        },
        onError: error => {
          toaster('error', error.message, 'top');
        },
      });
    } else {
      completeProfile();
    }
  };

  /**
   * @function handleRandomName
   * @memberof CreateGoRaqtProfile
   * @description
   * Generate random display name, user can choose that name from the list.
   */
  const handleRandomName = () => {
    const {userType, fitnessLevel, birthYear} = control._formValues;
    const data =
      userType || fitnessLevel || birthYear
        ? {
            ...(userType &amp;&amp; {user_type: userType}),
            ...(fitnessLevel &amp;&amp; {fitness_level: fitnessLevel}),
            ...(birthYear &amp;&amp; {birthyear: birthYear}),
          }
        : {};
    if (isApiStatus) {
      randomNameApi.mutate(data as any, {
        onSuccess: response => {
          if (!response?.status) {
            toaster('error', response.message, 'top');
          }
        },
        onError: error => {
          toaster('error', error.message, 'top');
        },
      });
    } else {
      toaster('success', 'Random name generation API called', 'top');
    }
  };

  return (
    &lt;React.Fragment>
      &lt;View style={styles.titleContainer}>
        &lt;Typography variant="subtitle2" style={styles.title}>
          {t('createProfile.createProfileTitle')}
        &lt;/Typography>
      &lt;/View>
      &lt;KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}>
        &lt;BottomSheetScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}>
          &lt;View style={styles.form}>
            &lt;View style={styles.inputContainer}>
              &lt;Controller
                control={control}
                name="fullName"
                render={({field: {onChange, onBlur, value}}) => (
                  &lt;CInput
                    label={t('createProfile.name')}
                    showLabel={true}
                    variant="light"
                    placeholder={t('createProfile.namePlaceholder')}
                    value={value}
                    onChangeText={text => {
                      onChange(text);
                      const debouncedCheck = debounce((name: string) => {
                        setDisplayName(name);
                      }, 1000);
                      debouncedCheck(text);
                    }}
                    onBlur={onBlur}
                    hasError={
                      !!errors.fullName ||
                      (value.length > 0 &amp;&amp; isBoolean(nameCheck?.status) &amp;&amp; !nameCheck?.status)
                    }
                    error={
                      errors.fullName?.message ||
                      (value.length > 0 &amp;&amp; isBoolean(nameCheck?.status) &amp;&amp; !nameCheck?.status
                        ? nameCheck?.message
                        : '')
                    }
                    inputStyle={styles.input}
                    containerStyle={{marginBottom: 0}}
                    ref={fullNameInputRef}
                    returnKeyType="next"
                    onSubmitEditing={() => emailInputRef.current?.focus()}
                    blurOnSubmit={false}
                    useBottomSheetInput={true}
                    editable={!isCheckingName}
                    isLoading={isCheckingName}
                  />
                )}
              />
            &lt;/View>

            {/* &lt;View style={styles.inputContainer}>
              &lt;Controller
                control={control}
                name="email"
                render={({field: {onChange, onBlur, value}}) => (
                  &lt;CInput
                    label={t('createProfile.email')}
                    showLabel={false}
                    placeholder={t('createProfile.emailPlaceholder')}
                    placeholderTextColor={theme.colors.placeholder}
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    hasError={!!errors.email}
                    error={errors.email?.message}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    inputStyle={[styles.input, {color: theme.colors.placeholder}]}
                    containerStyle={{marginBottom: 0}}
                    ref={emailInputRef}
                    returnKeyType="next"
                    onSubmitEditing={() => ageInputRef.current?.focus()}
                    blurOnSubmit={false}
                    editable={false} // Make email field read-only
                    useBottomSheetInput={true}
                  />
                )}
              />
            &lt;/View> */}

            &lt;View style={[styles.inputContainer]}>
              &lt;View style={styles.randomName}>
                &lt;Typography variant="bodyMedium" style={styles.radioTitle}>
                  {t('createProfile.randomNameGenerator')}
                  {randomNameApi?.isPending &amp;&amp; (
                    &lt;ActivityIndicator
                      size={14}
                      style={{paddingLeft: 10}}
                      color={theme.colors.white}
                    />
                  )}
                &lt;/Typography>
                &lt;TouchableOpacity
                  activeOpacity={0.7}
                  onPress={() => !randomNameApi?.isPending &amp;&amp; handleRandomName()}>
                  &lt;Icon name="refresh" color={theme.colors.white} size={18} />
                &lt;/TouchableOpacity>
              &lt;/View>
              {/* &lt;Controller
                control={control}
                name="randomName"
                render={({field: {onChange, value}}) => (
                  &lt;View style={[styles.radioContainer]}>
                    {randomNames?.data?.map((option, index) => (
                      &lt;RadioSelect
                        containerStyle={styles.fitnessRadio}
                        key={index}
                        label={option}
                        selected={randomNameValue === option}
                        onPress={() => {
                          Keyboard.dismiss();
                          setDisplayName(option);
                          setValue('fullName', option, {
                            shouldValidate: true,
                          });
                          onChange(option); // Call onChange to update the form value
                        }}
                      />
                    ))}
                  &lt;/View>
                )}
              /> */}

              &lt;Controller
                control={control}
                name="randomName"
                render={({field: {onChange, value}}) => (
                  &lt;View style={[styles.btnContainer]}>
                    {cachedData?.data?.map((option, index) => (
                      &lt;CButton
                        containerStyle={styles.fitnessBtn}
                        textStyle={styles.fitnessText}
                        key={index}
                        title={option}
                        variant="primary"
                        onPress={() => {
                          Keyboard.dismiss();
                          setDisplayName(option);
                          setValue('fullName', option, {
                            shouldValidate: true,
                          });
                          onChange(option); // Call onChange to update the form value
                        }}
                      />
                    ))}
                  &lt;/View>
                )}
              />
              {/* {errors.randomName &amp;&amp; (
                &lt;Typography variant="body" style={styles.errorText}>
                  {errors.randomName.message}
                &lt;/Typography>
              )} */}
            &lt;/View>

            &lt;View style={styles.inputContainer}>
              &lt;Controller
                control={control}
                name="birthYear"
                render={({field: {onChange, onBlur, value}}) => (
                  &lt;View>
                    &lt;TouchableOpacity activeOpacity={1} onPress={() => setShowYearModal(true)}>
                      &lt;View pointerEvents="none">
                        &lt;CInput
                          label={t('createProfile.birthYear')}
                          showLabel={true}
                          variant="light"
                          placeholder={t('createProfile.birthYearPlaceholder')}
                          value={value}
                          onChangeText={text => {
                            onChange(text);
                            if (text.length === 4) {
                              const birthYear = parseInt(text, 10);
                              const currentYear = new Date().getFullYear();
                              if (birthYear > currentYear) {
                                setAgeStatus('');
                                setValue('birthYear', text, {
                                  shouldValidate: true,
                                });
                              } else if (birthYear >= 1920 &amp;&amp; birthYear &lt;= currentYear) {
                                const age = currentYear - birthYear;
                                setAgeStatus(age >= 18 ? 'ADULT' : 'JUNIOR');
                                setValue('birthYear', text, {
                                  shouldValidate: true,
                                });
                              } else {
                                setAgeStatus('');
                                setValue('birthYear', text, {
                                  shouldValidate: true,
                                });
                              }
                            } else {
                              setAgeStatus('');
                              setValue('birthYear', text, {
                                shouldValidate: true,
                              });
                            }
                          }}
                          editable={false}
                          onBlur={onBlur}
                          hasError={!!errors.birthYear}
                          error={errors.birthYear?.message}
                          keyboardType="numeric"
                          inputStyle={styles.input}
                          containerStyle={{marginBottom: 0}}
                          ref={birthYearInputRef}
                          returnKeyType="done"
                          blurOnSubmit={false}
                          useBottomSheetInput={true}
                          maxLength={4}
                        />
                        {ageStatus &amp;&amp; (
                          &lt;Typography
                            variant="subtitle"
                            style={{
                              fontWeight: '700',
                              fontSize: 16,
                              minWidth: 80,
                              textAlign: 'right',
                              position: 'absolute',
                              right: 15,
                              top: '50%',
                              // bottom: 0,
                              color: theme.colors.primary,
                            }}>
                            {ageStatus}
                          &lt;/Typography>
                        )}
                      &lt;/View>
                    &lt;/TouchableOpacity>
                  &lt;/View>
                )}
              />
            &lt;/View>

            &lt;View style={styles.inputContainer}>
              &lt;Typography variant="bodyMedium" style={styles.radioTitle}>
                {t('createProfile.describe')}
              &lt;/Typography>
              &lt;Controller
                control={control}
                name="userType"
                render={({field: {onChange, value}}) => (
                  &lt;View style={styles.radioContainer}>
                    {createProfileRadioData?.bestDescribeOptions.map(option => (
                      &lt;RadioSelect
                        key={option.value}
                        label={t(option.label)}
                        selected={userTypeValue === option.value}
                        onPress={() => {
                          Keyboard.dismiss();
                          setValue('userType', option.value, {
                            shouldValidate: true,
                          });
                          onChange(option.value); // Call onChange to update the form value
                        }}
                      />
                    ))}
                  &lt;/View>
                )}
              />
              {errors.userType &amp;&amp; (
                &lt;Typography variant="body" style={styles.errorText}>
                  {errors.userType.message}
                &lt;/Typography>
              )}
            &lt;/View>

            &lt;View style={styles.inputContainer}>
              &lt;Typography variant="bodyMedium" style={styles.radioTitle}>
                {t('createProfile.describeFitness')}
              &lt;/Typography>
              &lt;Controller
                control={control}
                name="fitnessLevel"
                render={({field: {onChange, value}}) => (
                  &lt;View style={styles.radioContainer}>
                    {createProfileRadioData?.describeFitnessOptions.map(option => (
                      &lt;RadioSelect
                        containerStyle={styles.fitnessRadio}
                        key={option.value}
                        label={t(option.label)}
                        selected={fitnessLevelValue === option.value}
                        onPress={() => {
                          Keyboard.dismiss();
                          setValue('fitnessLevel', option.value, {
                            shouldValidate: true,
                          });
                          onChange(option.value); // Call onChange to update the form value
                        }}
                      />
                    ))}
                  &lt;/View>
                )}
              />
              {errors.fitnessLevel &amp;&amp; (
                &lt;Typography variant="body" style={styles.errorText}>
                  {errors.fitnessLevel.message}
                &lt;/Typography>
              )}
            &lt;/View>
            &lt;View style={styles.buttonContainer}>
              &lt;View style={styles.flex1}>
                &lt;CButton
                  title={t('createProfile.submitBtn')}
                  variant="primary"
                  onPress={handleSubmit(onSubmit)}
                  textStyle={styles.fitnessText}
                  loading={updateProfileMutation.isPending}
                  isDisabled={updateProfileMutation.isPending}
                />
              &lt;/View>
              &lt;View style={styles.flex1}>
                &lt;CButton
                  title={t('createProfile.setupLaterBtn')}
                  variant="primary"
                  onPress={() => {
                    Keyboard.dismiss();
                    setupLater();
                  }}
                  textStyle={styles.fitnessText}
                  isDisabled={updateProfileMutation.isPending}
                />
              &lt;/View>
            &lt;/View>
          &lt;/View>

          &lt;ModalYearPicker
            visible={showYearModal}
            onClose={() => setShowYearModal(false)}
            title="Select Year"
            initialYear={
              watch('birthYear') ? parseInt(watch('birthYear'), 10) : new Date().getFullYear()
            }
            onYearSelected={year => {
              setValue('birthYear', String(year), {
                shouldValidate: true,
              });
              // Update age status when year is selected
              const currentYear = new Date().getFullYear();
              if (year >= 1920 &amp;&amp; year &lt;= currentYear) {
                const age = currentYear - year;
                setAgeStatus(age >= 18 ? 'ADULT' : 'JUNIOR');
              } else {
                setAgeStatus('');
              }
              setShowYearModal(false);
            }}
          />
        &lt;/BottomSheetScrollView>
      &lt;/KeyboardAvoidingView>
    &lt;/React.Fragment>
  );
};

export default CreateGoRaqtProfile;
</code></pre>
        </article>
    </section>




            </div>
            
            <!-- Footer removed via patch -->
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

<script src="scripts/search.js"> </script>


</body>
</html>
