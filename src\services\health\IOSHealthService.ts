import {Platform} from 'react-native';
import RNHealthKit from 'react-native-health';
import type {HealthKitPermissions, HealthValue} from 'react-native-health';
import {HealthServiceInterface} from './types';
import {format} from 'date-fns';

class IOSHealthService implements HealthServiceInterface {
  private isInitialized = false;
  private stepGoal = 10000; // Default step goal

  /**
   * Initialize Apple HealthKit
   */
  async initialize(): Promise<boolean> {
    if (Platform.OS !== 'ios') {
      return false;
    }

    if (this.isInitialized) {
      return true;
    }

    try {
      // Define permissions using the correct constants
      const permissions: HealthKitPermissions = {
        permissions: {
          read: [
            RNHealthKit.Constants.Permissions.Steps,
            RNHealthKit.Constants.Permissions.HeartRate,
            RNHealthKit.Constants.Permissions.ActiveEnergyBurned,
            RNHealthKit.Constants.Permissions.DistanceWalkingRunning,
          ],
          write: [],
        },
      };

      console.log('Initializing HealthKit with permissions:', JSON.stringify(permissions));

      return new Promise<boolean>(resolve => {
        try {
          // Initialize HealthKit with proper error handling
          RNHealthKit.initHealthKit(permissions, (error: string) => {
            if (error) {
              console.error('Error initializing Apple HealthKit:', error);
              resolve(false);
            } else {
              console.log('HealthKit initialized successfully');
              this.isInitialized = true;
              resolve(true);
            }
          });
        } catch (initError) {
          console.error('Exception during HealthKit initialization:', initError);
          resolve(false);
        }
      });
    } catch (error) {
      console.error('Error initializing Apple HealthKit:', error);
      return false;
    }
  }

  /**
   * Request permissions for Apple HealthKit
   */
  async requestPermissions(): Promise<boolean> {
    return this.initialize();
  }

  /**
   * Check if Apple HealthKit permissions are granted
   */
  async hasPermissions(): Promise<boolean> {
    if (Platform.OS !== 'ios') {
      return false;
    }

    // HealthKit doesn't provide a direct way to check permissions
    // We'll try to initialize and consider that as a check
    return this.initialize();
  }

  /**
   * Get step count for a specific date
   */
  async getStepCount(date: Date = new Date()): Promise<{count: number; date: string}> {
    if (Platform.OS !== 'ios' || !this.isInitialized) {
      await this.initialize();
    }

    try {
      // Create a new date object to avoid mutating the original date
      const dateObj = new Date(date);

      // Format the date as YYYY-MM-DD
      const formattedDate = format(dateObj, 'yyyy-MM-dd');

      // Create start and end date for the day
      const startDate = new Date(dateObj);
      startDate.setHours(0, 0, 0, 0);

      const endDate = new Date(dateObj);
      endDate.setHours(23, 59, 59, 999);

      // First try with the standard getStepCount method
      const options = {
        date: dateObj.toISOString(),
        includeManuallyAdded: true,
      };

      console.log('Fetching step count with options:', options);

      return new Promise<{count: number; date: string}>(resolve => {
        try {
          RNHealthKit.getStepCount(options, (error: string, results: HealthValue) => {
            if (error) {
              console.error('Error getting step count:', error);

              // Fallback to getSamples if getStepCount fails
              console.log('Trying fallback method for step count...');
              const sampleOptions = {
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
              };

              // Define the step count sample type
              interface StepCountSample {
                value: number;
                startDate: string;
                endDate: string;
                id?: string;
                sourceId?: string;
                sourceName?: string;
              }

              RNHealthKit.getDailyStepCountSamples(
                sampleOptions,
                (sampleError: string, sampleResults: StepCountSample[]) => {
                  if (sampleError) {
                    console.error('Error getting step count samples:', sampleError);
                    resolve({
                      count: 0,
                      date: formattedDate,
                    });
                  } else {
                    console.log('Step count sample results:', sampleResults);
                    let stepCount = 0;
                    if (sampleResults && sampleResults.length > 0) {
                      // Sum up all step counts for the day
                      stepCount = sampleResults.reduce(
                        (sum: number, sample: StepCountSample) => sum + sample.value,
                        0,
                      );
                    }

                    resolve({
                      count: stepCount,
                      date: formattedDate,
                    });
                  }
                },
              );
            } else {
              console.log('Step count results:', results);
              resolve({
                count: results.value || 0,
                date: formattedDate,
              });
            }
          });
        } catch (callError) {
          console.error('Exception during step count retrieval:', callError);
          resolve({
            count: 0,
            date: formattedDate,
          });
        }
      });
    } catch (error) {
      console.error('Error getting step count:', error);
      return {
        count: 0,
        date: format(date, 'yyyy-MM-dd'),
      };
    }
  }

  /**
   * Get heart rate for a specific date
   */
  async getHeartRate(
    date: Date = new Date(),
  ): Promise<{value: number; unit: string; date: string}> {
    if (Platform.OS !== 'ios' || !this.isInitialized) {
      await this.initialize();
    }

    try {
      // Create a new date object to avoid mutating the original date
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);

      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);

      const options = {
        startDate: startDate.toISOString(), // Start of the day
        endDate: endDate.toISOString(), // End of the day
        limit: 1, // Get only the most recent reading
        ascending: false, // Get the most recent first
      };

      // Define the heart rate sample type
      interface HeartRateSample {
        id?: string;
        value: number;
        startDate: string;
        endDate: string;
        sourceId?: string;
        sourceName?: string;
      }

      return new Promise<{value: number; unit: string; date: string}>(resolve => {
        RNHealthKit.getHeartRateSamples(options, (error: string, results: HeartRateSample[]) => {
          if (error) {
            console.error('Error getting heart rate:', error);
            resolve({
              value: 0,
              unit: 'bpm',
              date: format(date, 'yyyy-MM-dd'),
            });
          } else {
            // Get the most recent heart rate reading
            let heartRate = 0;
            if (results && results.length > 0) {
              heartRate = results[0].value;
              console.log('Heart rate sample:', results[0]);
            } else {
              console.log('No heart rate samples found');
            }

            resolve({
              value: heartRate,
              unit: 'bpm',
              date: format(date, 'yyyy-MM-dd'),
            });
          }
        });
      });
    } catch (error) {
      console.error('Error getting heart rate:', error);
      return {
        value: 0,
        unit: 'bpm',
        date: format(date, 'yyyy-MM-dd'),
      };
    }
  }

  /**
   * Get step count goal
   */
  async getStepCountGoal(): Promise<number> {
    // Apple HealthKit doesn't provide a direct API to get the step goal
    // We'll use the stored value or default
    return this.stepGoal;
  }

  /**
   * Set step count goal
   */
  async setStepCountGoal(goal: number): Promise<boolean> {
    this.stepGoal = goal;
    return true;
  }
}

export default IOSHealthService;
