{"tags": {"allowUnknownTags": true}, "plugins": ["plugins/markdown", "node_modules/better-docs", "node_modules/better-docs/typescript", "node_modules/better-docs/category", "node_modules/better-docs/component"], "babel": {"extensions": ["js", "jsx", "ts", "tsx"]}, "source": {"include": ["src/components", "src/hooks", "src/utils", "src/screens", "src/services", "src/config", "src/context", "src/navigation", "src/providers"], "includePattern": "\\.(js|jsx|ts|tsx)$", "excludePattern": "(^|\\/|\\\\)_"}, "opts": {"encoding": "utf8", "destination": "docs", "recurse": true, "verbose": true, "template": "node_modules/better-docs", "readme": "README.md"}, "templates": {"cleverLinks": true, "monospaceLinks": true, "search": true, "hideGenerator": true, "better-docs": {"name": "GoRaqt Documentation", "css": "styles/style.css"}}}