

<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> navigation/DrawerNavigator.tsx</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="styles/style.css">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
             
                <a href="index.html">
                    <h1 class="navbar-item">GoRaqt Documentation</h1>
                </a>
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    Documentation
                </a>
                
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
                <div class="search-wrapper">
                    <input id="search" type="text" placeholder="Search docs..." class="input">
                </div>
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Global</h3><ul><li><a href="global.html#HIDE_TAB_SCREENS">HIDE_TAB_SCREENS</a></li><li><a href="global.html#SENSITIVE_FIELDS">SENSITIVE_FIELDS</a></li><li><a href="global.html#addBreadcrumb">addBreadcrumb</a></li><li><a href="global.html#apiCall">apiCall</a></li><li><a href="global.html#authenticateWithBiometrics">authenticateWithBiometrics</a></li><li><a href="global.html#calculateAge">calculateAge</a></li><li><a href="global.html#checkAuthStatusApi">checkAuthStatusApi</a></li><li><a href="global.html#checkLocationPermission">checkLocationPermission</a></li><li><a href="global.html#cleanMemoryIfNeeded">cleanMemoryIfNeeded</a></li><li><a href="global.html#clearAllData">clearAllData</a></li><li><a href="global.html#configureStatusBar">configureStatusBar</a></li><li><a href="global.html#createHealthService">createHealthService</a></li><li><a href="global.html#deleteFcmTokenFromDB">deleteFcmTokenFromDB</a></li><li><a href="global.html#deleteNotification">deleteNotification</a></li><li><a href="global.html#disableBiometrics">disableBiometrics</a></li><li><a href="global.html#enableBiometrics">enableBiometrics</a></li><li><a href="global.html#forgotPasswordApi">forgotPasswordApi</a></li><li><a href="global.html#formatBirthdate">formatBirthdate</a></li><li><a href="global.html#getAddressFromCoordinates">getAddressFromCoordinates</a></li><li><a href="global.html#getCurrentLocationWithAddress">getCurrentLocationWithAddress</a></li><li><a href="global.html#getCurrentPosition">getCurrentPosition</a></li><li><a href="global.html#getCurrentRegion">getCurrentRegion</a></li><li><a href="global.html#getCurrentUserApi">getCurrentUserApi</a></li><li><a href="global.html#getNotificationsList">getNotificationsList</a></li><li><a href="global.html#getTabBarVisibility">getTabBarVisibility</a></li><li><a href="global.html#getUnreadNotificationsCount">getUnreadNotificationsCount</a></li><li><a href="global.html#getUserDetails">getUserDetails</a></li><li><a href="global.html#getUserProfile">getUserProfile</a></li><li><a href="global.html#handleNotificationNavigation">handleNotificationNavigation</a></li><li><a href="global.html#handleTextureError">handleTextureError</a></li><li><a href="global.html#initSentry">initSentry</a></li><li><a href="global.html#isBiometricsAvailable">isBiometricsAvailable</a></li><li><a href="global.html#isBiometricsEnabled">isBiometricsEnabled</a></li><li><a href="global.html#lastCacheClearTime">lastCacheClearTime</a></li><li><a href="global.html#logError">logError</a></li><li><a href="global.html#logMessage">logMessage</a></li><li><a href="global.html#loginWithEmail">loginWithEmail</a></li><li><a href="global.html#loginWithEmailApi">loginWithEmailApi</a></li><li><a href="global.html#logoutApi">logoutApi</a></li><li><a href="global.html#markAllNotificationsAsRead">markAllNotificationsAsRead</a></li><li><a href="global.html#markNotificationAsRead">markNotificationAsRead</a></li><li><a href="global.html#navigateBackFromExample">navigateBackFromExample</a></li><li><a href="global.html#navigateToOptimizedFlatListExample">navigateToOptimizedFlatListExample</a></li><li><a href="global.html#otpResendApi">otpResendApi</a></li><li><a href="global.html#otpVerifyApi">otpVerifyApi</a></li><li><a href="global.html#prefetchInitialQueries">prefetchInitialQueries</a></li><li><a href="global.html#prefetchPost">prefetchPost</a></li><li><a href="global.html#prefetchUser">prefetchUser</a></li><li><a href="global.html#refreshTokenApi">refreshTokenApi</a></li><li><a href="global.html#registerWithEmail">registerWithEmail</a></li><li><a href="global.html#registerWithEmailApi">registerWithEmailApi</a></li><li><a href="global.html#requestLocationPermission">requestLocationPermission</a></li><li><a href="global.html#resetPasswordApi">resetPasswordApi</a></li><li><a href="global.html#saveFcmTokenInDB">saveFcmTokenInDB</a></li><li><a href="global.html#schema">schema</a></li><li><a href="global.html#scrubObject">scrubObject</a></li><li><a href="global.html#scrubSensitiveData">scrubSensitiveData</a></li><li><a href="global.html#setCustomStatusBar">setCustomStatusBar</a></li><li><a href="global.html#setDarkStatusBar">setDarkStatusBar</a></li><li><a href="global.html#setLightStatusBar">setLightStatusBar</a></li><li><a href="global.html#setNavigationContext">setNavigationContext</a></li><li><a href="global.html#setStatusBarHidden">setStatusBarHidden</a></li><li><a href="global.html#setUserContext">setUserContext</a></li><li><a href="global.html#setupMemoryCleaning">setupMemoryCleaning</a></li><li><a href="global.html#setupTextureErrorHandlers">setupTextureErrorHandlers</a></li><li><a href="global.html#signInWithFacebook">signInWithFacebook</a></li><li><a href="global.html#signInWithGoogle">signInWithGoogle</a></li><li><a href="global.html#startTransaction">startTransaction</a></li><li><a href="global.html#styles">styles</a></li><li><a href="global.html#updateUserProfile">updateUserProfile</a></li><li><a href="global.html#useAuthStatus">useAuthStatus</a></li><li><a href="global.html#useCurrentUser">useCurrentUser</a></li><li><a href="global.html#useLogin">useLogin</a></li><li><a href="global.html#useRegister">useRegister</a></li><li><a href="global.html#useSafeArea">useSafeArea</a></li><li><a href="global.html#useSocialLogin">useSocialLogin</a></li><li><a href="global.html#useUpdateUserProfile">useUpdateUserProfile</a></li><li><a href="global.html#useUserProfile">useUserProfile</a></li></ul></div><div class="category"><h2>Components</h2><h3>Components</h3><ul><li><a href="Advertisement.html">Advertisement</a></li><li><a href="BiometricGate.html">BiometricGate</a></li><li><a href="CButton.html">CButton</a></li><li><a href="CreateGoRaqtProfile.html">CreateGoRaqtProfile</a></li><li><a href="CustomMapView.html">CustomMapView</a></li><li><a href="CustomMarker.html">CustomMarker</a></li><li><a href="NotificationCard.html">NotificationCard</a></li><li><a href="OfferBanner.html">OfferBanner</a></li><li><a href="Subscription.html">Subscription</a></li></ul></div><div class="category"><h2>Interfaces</h2><h3>Interfaces</h3><ul><li><a href="AdvertisementProps.html">AdvertisementProps</a></li><li><a href="ApiResponse.html">ApiResponse</a></li><li><a href="BiometricGateProps.html">BiometricGateProps</a></li><li><a href="CButtonProps.html">CButtonProps</a></li><li><a href="CreateProfileProps.html">CreateProfileProps</a></li><li><a href="CustomMarkerProps.html">CustomMarkerProps</a></li><li><a href="DrawerItemType.html">DrawerItemType</a></li><li><a href="ForgotPasswordScreenProps.html">ForgotPasswordScreenProps</a></li><li><a href="Notification.html">Notification</a></li><li><a href="NotificationCardProps.html">NotificationCardProps</a></li><li><a href="PaginationState.html">PaginationState</a></li><li><a href="PermissionsScreenProps.html">PermissionsScreenProps</a></li><li><a href="RouteParams.html">RouteParams</a></li><li><a href="SentryNavigationContainerProps.html">SentryNavigationContainerProps</a></li><li><a href="SocialRegisterRequest.html">SocialRegisterRequest</a></li><li><a href="SplashScreenProps.html">SplashScreenProps</a></li><li><a href="TermsOfServiceScreenProps.html">TermsOfServiceScreenProps</a></li><li><a href="UseLogoutReturn.html">UseLogoutReturn</a></li></ul><h3><a href="global.html">Global</a></h3></div><div class="category"><h2>Navigation</h2><h3>Components</h3><ul><li><a href="DrawerNavigator.html">DrawerNavigator</a></li><li><a href="SentryNavigationContainer.html">SentryNavigationContainer</a></li></ul></div><div class="category"><h2>Screens</h2><h3>Components</h3><ul><li><a href="CommunityDetails.html">CommunityDetails</a></li><li><a href="DirectFetchTest.html">DirectFetchTest</a></li><li><a href="EmailSignupScreen.html">EmailSignupScreen</a></li><li><a href="ForgotPasswordScreen.html">ForgotPasswordScreen</a></li><li><a href="LoginScreen.html">LoginScreen</a></li><li><a href="MyMatchesScreen.html">MyMatchesScreen</a></li><li><a href="NotificationsListScreen.html">NotificationsListScreen</a></li><li><a href="PermissionsScreen.html">PermissionsScreen</a></li><li><a href="SignupScreen.html">SignupScreen</a></li><li><a href="SplashScreen.html">SplashScreen</a></li><li><a href="TermsOfServiceScreen.html">TermsOfServiceScreen</a></li><li><a href="Verification.html">Verification</a></li></ul></div><div class="category"><h2>Utils</h2><h3>Global</h3><ul><li><a href="global.html#api">api</a></li><li><a href="global.html#socialLoginApi">socialLoginApi</a></li><li><a href="global.html#useLogout">useLogout</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Source</p>
                    <h1>navigation/DrawerNavigator.tsx</h1>
                </header>
                



    
    <section>
        <article>
            <pre class="prettyprint source linenums"><code>import React, {useCallback, useMemo, useState, useEffect} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, ScrollView} from 'react-native';
import {createDrawerNavigator} from '@react-navigation/drawer';
import TabNavigator from './TabNavigator';
import {useThemeStore} from '@/store/themeStore';
import {useAuthStore} from '@/store/authStore';
import {CButton, CImage, CustomModal, Icon, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import EditProfileScreen from '@/screens/drawer/EditProfileScreen';
import SearchInput from '@/components/SearchInput';
import OrderReserveEquipment from '@/components/Drawer/AssistantCoach/OrderReserveEquipment';
import InvitePlayers from '@/components/Drawer/AssistantCoach/InvitePlayers';
import CoachOptionsScreen from '@/components/Drawer/AssistantCoach/CoachOptions';
import GetCertified from '@/components/Drawer/AssistantCoach/GetCertified';
import MessageNotificationPreferences from '@/components/Drawer/AssistantCoach/MessageNotificationPreferences';
import WelcomeScreen from '@/components/Drawer/AssistantCoach/Welcome';
import PostAnAd from '@/components/Drawer/AssistantCoach/PostAnAd';
import ManageContent from '@/components/Drawer/AssistantCoach/ManageContent';
import ManageClasses from '@/components/Drawer/AssistantCoach/ManageClasses';
import ManageServiceRequest from '@/components/Drawer/AssistantCoach/ManageServiceRequest';
import CalendarScreen from '@/components/Drawer/AssistantCoach/Calendar';
import ManagePlayerAssetsScreen from '@/components/Drawer/AssistantCoach/ManagePlayerAssets';
import {useConfigStore} from '@/store';
import ProfileQrScreen from '@/screens/ProfileQrScreen';
import useTranslation from '@/hooks/useTranslation';
import {coachDrawerMenuList, drawerMenuList} from '@/config/staticData';
import DateScreen from '@/screens/tabs/SearchScreen/DateScreen';
import EquipmentReservationScreen from '@/screens/tabs/SearchScreen/EquipmentReservationScreen';
import RacquetSelector from '@/screens/tabs/RacquetsScreens/RacquetSelector';
import RacquetSelectorDetail from '@/screens/tabs/RacquetsScreens/RacquetSelectorDetail';
import {toaster} from '@/utils/commonFunctions';
import api from '@/services/api';
import {useConnection} from '@sendbird/uikit-react-native';
import EditCoachProfileScreen from '@/screens/EditCoachProfile';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import CertificateScreen from '@/screens/CertificateScreen';

// Community-related screens moved to CommunityStack

/**
 * @category Interfaces
 * @typedef {Object} DrawerItemType
 * @property {boolean} isVisible - Whether the drawer item should be displayed
 * @property {string} id - Unique identifier for the drawer item
 * @property {string} label - Display label for the drawer item (translation key)
 * @property {string} [screen] - Optional screen name to navigate to
 * @property {Function} [onPress] - Optional custom onPress handler
 */
interface DrawerItemType {
  isVisible: boolean;
  id: string;
  label: string;
  screen?: string;
  onPress?: () => void;
}

/**
 * @category Interfaces
 * @typedef {Object} DrawerParamList
 * @description Type definition for all possible drawer navigation screens and their parameters
 */
export type DrawerParamList = {
  welcome: undefined;
  OrderReserveEquipment: undefined;
  InvitePlayers: undefined;
  CoachOptions: undefined;
  getCertified: undefined;
  MessageNotificationPreferences: undefined;
  Settings: undefined;
  EditProfile: undefined;
  MainTabs: undefined;
  PostAnAd: undefined;
  ManageContent: undefined;
  ManageClasses: undefined;
  ManageServiceRequest: undefined;
  ManagePlayerAssets: undefined;
  ManageCommunity: undefined;
  Orders: undefined;
  Help: undefined;
  Rewards: undefined;
  RecycleBalls: undefined;
  ProfileQrScreen: undefined;
  DateScreen: undefined;
  EquipmentReservationScreen: undefined;
  RacquetCategory: {
    sportsTitle: string;
  };
  RacquetBrands: {
    sportsTitle: string;
    category: string;
  };
  RacquetSelector: undefined;
  RacquetSelectorDetail: undefined;
  PlayerConnectScreen: undefined;
  GoEats: undefined;
  GoTravel: undefined;
  GetCertified: undefined;
  // Community-related screens moved to CommunityStack
};

const Drawer = createDrawerNavigator();

/**
 * @category Function
 * @memberof DrawerNavigator
 * @description
 * Custom drawer content component that renders the drawer UI
 * Displays user profile, search functionality, and navigation menu items
 *
 * @param {Object} props - Component props
 * @param {Object} props.navigation - React Navigation object for screen navigation
 * @returns {JSX.Element} - The custom drawer content,
 */
const CustomDrawerContent = ({navigation}: any) => {
  const theme = useThemeStore();
  const {user} = useAuthStore();

  const {
    coachProfile,
    setCoachProfile,
    drawerMenuItems,
    setCoachAssistantEnabled,
    getCoachAssistantEnabled,
  } = useConfigStore();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const isCoachAssistantEnabled = getCoachAssistantEnabled();
  console.log('isCoachAssistantEnabled=====>>>>>', isCoachAssistantEnabled);
  const [filterData, setFilterData] = useState&lt;DrawerItemType[]>(
    isCoachAssistantEnabled ? coachDrawerMenuList : drawerMenuItems || drawerMenuList,
  );
  const [searchQuery, setSearchQuery] = useState&lt;string>('');

  const [loader, setLoader] = useState(false);

  const {t} = useTranslation();
  const {connect} = useConnection();

  // Define drawer menu items with their sub-items
  const drawerItems = useMemo&lt;DrawerItemType[]>(
    () => (isCoachAssistantEnabled ? coachDrawerMenuList : drawerMenuItems || drawerMenuList),
    [drawerMenuItems, isCoachAssistantEnabled],
  );

  // Update filterData when drawerItems changes
  useEffect(() => {
    setFilterData(drawerItems);
    connect(user?.id);
  }, [drawerItems]);

  /**
   * @category Function
   * @memberof DrawerNavigator
   * @description
   * Renders a drawer menu item with its label and navigation handler
   * @param {DrawerItemType} item - The drawer item configuration
   * @returns {JSX.Element} - The rendered menu item or null if not visible
   */

  // Render a drawer menu item with its label
  const renderMenuItem = (item: DrawerItemType) => {
    if (!item.isVisible) {
      return null;
    }

    /**
     * @category Function
     * @memberof DrawerNavigator
     * @description
     *  Handles the press event for a drawer menu item
     * Closes the drawer and navigates to the appropriate screen
     */
    const handleItemPress = () => {
      // Close the drawer first
      navigation.closeDrawer();

      // Navigate to the corresponding screen based on the item ID
      if (item.id === 'notifications') {
        navigation.navigate('Notifications' as never);
      } else if (item.id === 'refer_friend') {
        navigation.navigate('ReferFriend' as never);
      } else if (item.id === 'my_matches') {
        navigation.navigate('MyMatches' as never);
      } else if (item.id === 'manage_community') {
        navigation.navigate('ManageCommunity' as never);
      } else if (item.id === 'orders') {
        navigation.navigate('Orders' as never);
      } else if (item.id === 'help') {
        navigation.navigate('Help' as never);
      } else if (item.id === 'rewards') {
        navigation.navigate('Rewards' as never);
      } else if (item.id === 'recycleBalls') {
        navigation.navigate('RecycleBalls' as never);
      } else if (item.id === 'edit_coach_profile') {
        navigation.navigate('EditCoachProfile' as never);
      } else if (item.id === 'get_certified') {
        navigation.navigate('GetCertified');
      } else if (item.screen) {
        navigation.navigate(item.screen as never);
      } else if (item.onPress) {
        item.onPress();
      }
    };

    return (
      &lt;View key={item.id} style={styles(theme).menuItemContainer}>
        &lt;TouchableOpacity style={styles(theme).menuItem} onPress={handleItemPress}>
          &lt;Text style={styles(theme).menuItemText}>{t(item.label)}&lt;/Text>
        &lt;/TouchableOpacity>
      &lt;/View>
    );
  };

  const renderModalContent = () => {
    return (
      &lt;WelcomeScreen
        onPress={() => {
          setCoachProfile(true);
          setIsModalVisible(false);
          setCoachAssistantEnabled(true);
        }}
      />
    );
  };

  const handleSearch = useCallback(
    (query: string) => {
      const filtered = query
        ? drawerItems.filter(item => t(item.label).toLowerCase().includes(query.toLowerCase()))
        : drawerItems;
      setFilterData(filtered);
    },
    [drawerItems],
  );
  const clearSearch = () => setSearchQuery('');

  const availableToPlay = async () => {
    setLoader(true);
    try {
      const response = await api.post('/user/available-to-play');
      if (response.data.status) {
        toaster('success', 'You are now available to play');
      } else {
        toaster('error', 'Failed to make yourself available to play');
      }
    } catch (error) {
      console.log('error=====>>>>>', error);
      toaster('error', 'Failed to make yourself available to play');
    } finally {
      setLoader(false);
    }
  };
  const insets = useSafeAreaInsets();

  return (
    &lt;SafeAreaView
      style={[
        styles(theme).container,
        {
          paddingTop: insets.top,
          paddingBottom: insets.bottom,
          paddingLeft: insets.left,
          paddingRight: insets.right,
        },
      ]}>
      &lt;View style={styles(theme).headerContainer}>
        &lt;View style={styles(theme).userInfoContainer}>
          &lt;CImage
            source={
              isCoachAssistantEnabled
                ? user?.coach_profile_pic ||
                  'https://static.vecteezy.com/system/resources/previews/036/280/651/original/default-avatar-profile-icon-social-media-user-image-gray-avatar-icon-blank-profile-silhouette-illustration-vector.jpg'
                : user?.profile_pic ||
                  'https://static.vecteezy.com/system/resources/previews/036/280/651/original/default-avatar-profile-icon-social-media-user-image-gray-avatar-icon-blank-profile-silhouette-illustration-vector.jpg'
            }
            resizeMode="cover"
            style={styles(theme).avatar}
          />
          &lt;TouchableOpacity
            style={styles(theme).closeButton}
            onPress={() => navigation.closeDrawer()}>
            &lt;Icon name="close1-1" size={36} color={theme.colors.crimsonRed} />
          &lt;/TouchableOpacity>
        &lt;/View>
        &lt;View style={styles(theme).userTextContainer}>
          &lt;Typography color={theme.colors.activeColor} variant="userName">
            {user?.name || user?.display_name}
          &lt;/Typography>
          &lt;TouchableOpacity
            activeOpacity={0.8}
            style={styles(theme).emailContainer}
            onPress={() => navigation.navigate('EditProfile')}>
            &lt;Typography variant="bodyMedium" color={theme.colors.activeColor}>
              {user?.email || '<EMAIL>'}
            &lt;/Typography>
            &lt;Icon name="editpen" size={20} color={theme.colors.orange} />
          &lt;/TouchableOpacity>
          &lt;CButton
            variant="active"
            title={t('drawer.availableToPlay')}
            onPress={() => {
              // availableToPlay();
              toaster('info', 'coming soon', 'top');
            }}
            loading={loader}
            containerStyle={styles(theme).availBtn}
          />
          &lt;TouchableOpacity
            style={styles(theme).coach}
            onPress={() => {
              if (!isCoachAssistantEnabled &amp;&amp; coachProfile) {
                setCoachAssistantEnabled(true);
              } else {
                setCoachAssistantEnabled(false);
              }
              if (!coachProfile &amp;&amp; !isCoachAssistantEnabled) {
                setIsModalVisible(true);
              }
            }}>
            &lt;Typography variant="tryNow" color={theme.colors.activeColor}>
              {isCoachAssistantEnabled
                ? t('drawer.exitAssistantCoach')
                : t('drawer.assistantCoach')}
            &lt;/Typography>
          &lt;/TouchableOpacity>
        &lt;/View>
      &lt;/View>

      &lt;View style={styles(theme).searchContainer}>
        &lt;SearchInput
          variant="light"
          placeholder={t('common.search')}
          value={searchQuery}
          onChangeText={setSearchQuery}
          onClear={clearSearch}
          onSearch={handleSearch}
          debounceTime={300}
        />
      &lt;/View>

      &lt;ScrollView contentContainerStyle={styles(theme).scrollContainer}>
        {filterData?.map(renderMenuItem)}
      &lt;/ScrollView>
      &lt;CustomModal
        animationType="slide"
        variant="bottom"
        visible={isModalVisible}
        onClose={() => {
          setIsModalVisible(false);
        }}
        modalContainerStyle={{height: '100%'}}
        showCloseButtonRight={true}
        imageBg={true}>
        {renderModalContent()}
      &lt;/CustomModal>
    &lt;/SafeAreaView>
  );
};

/**
 * @component
 * @category Navigation
 * @description
 * Sets up the drawer navigation with custom content and all app screens
 * @returns {JSX.Element} - The configured drawer navigator
 */
const DrawerNavigator = () => {
  const theme = useThemeStore();

  return (
    &lt;Drawer.Navigator
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: theme.colors.black,
          width: '100%',
        },
        drawerType: 'front',
        drawerStatusBarAnimation: 'fade',
        overlayColor: theme.colors.semiTransparentBlack,
        swipeEnabled: true, // Re-enable drawer swipe since we have custom gesture handler
        swipeEdgeWidth: 20, // Small swipe area to minimize conflicts
      }}
      drawerContent={props => &lt;CustomDrawerContent {...props} />}>
      &lt;Drawer.Screen name="MainTabs" component={TabNavigator} />
      &lt;Drawer.Screen name="EditProfile" component={EditProfileScreen} />
      &lt;Drawer.Screen name="welcome" component={WelcomeScreen} />
      &lt;Drawer.Screen name="OrderReserveEquipment" component={OrderReserveEquipment} />
      &lt;Drawer.Screen name="InvitePlayers" component={InvitePlayers} />
      &lt;Drawer.Screen name="CoachOptions" component={CoachOptionsScreen} />
      &lt;Drawer.Screen name="getCertified" component={GetCertified} />
      &lt;Drawer.Screen
        name="MessageNotificationPreferences"
        component={MessageNotificationPreferences}
      />
      &lt;Drawer.Screen name="PostAnAd" component={PostAnAd} />
      &lt;Drawer.Screen name="ManageContent" component={ManageContent} />
      {/* &lt;Drawer.Screen name="EditCoachProfile" component={EditCoachProfile} /> */}
      &lt;Drawer.Screen name="ManageClasses" component={ManageClasses} />
      &lt;Drawer.Screen name="ManageServiceRequest" component={ManageServiceRequest} />
      &lt;Drawer.Screen name="Calendar" component={CalendarScreen} />
      &lt;Drawer.Screen name="ManagePlayerAssets" component={ManagePlayerAssetsScreen} />
      &lt;Drawer.Screen name="ProfileQrScreen" component={ProfileQrScreen} />
      &lt;Drawer.Screen name="DateScreen" component={DateScreen} />
      &lt;Drawer.Screen name="EquipmentReservationScreen" component={EquipmentReservationScreen} />
      &lt;Drawer.Screen name="RacquetSelector" component={RacquetSelector} />
      &lt;Drawer.Screen name="RacquetSelectorDetail" component={RacquetSelectorDetail} />
      &lt;Drawer.Screen name="EditCoachProfile" component={EditCoachProfileScreen} />
      &lt;Drawer.Screen name="GetCertified" component={CertificateScreen} />
    &lt;/Drawer.Navigator>
  );
};

// Styles
const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    headerContainer: {
      paddingHorizontal: 16,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Gunmetal,
      backgroundColor: theme.colors.background,
      // marginTop: 45,
    },
    userInfoContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },

    avatar: {
      width: 50,
      height: 50,
      borderRadius: 50,
    },
    userTextContainer: {
      marginTop: 15,
    },

    emailContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
    coach: {
      marginTop: 40,
    },
    userEmail: {
      fontSize: 14,
      color: theme.colors.lime,
      marginRight: 8,
    },
    closeButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.background,
    },
    searchContainer: {
      paddingHorizontal: 16,
      paddingBottom: 12,
    },
    searchBar: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 10,
      backgroundColor: theme.colors.primary,
    },
    searchText: {
      color: theme.colors.white,
      marginLeft: 8,
      fontSize: 16,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingBottom: 30,
    },
    menuItemContainer: {
      marginBottom: 8,
    },
    menuItem: {
      paddingVertical: 12,
      paddingHorizontal: 16,
    },
    menuItemText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.lime,
    },
    availBtn: {
      marginTop: 10,
    },
  });

export default DrawerNavigator;
</code></pre>
        </article>
    </section>




            </div>
            
            <!-- Footer removed via patch -->
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

<script src="scripts/search.js"> </script>


</body>
</html>
