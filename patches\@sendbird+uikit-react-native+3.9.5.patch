diff --git a/node_modules/@sendbird/uikit-react-native/src/components/ChannelInput/SendInput.tsx b/node_modules/@sendbird/uikit-react-native/src/components/ChannelInput/SendInput.tsx
index 7340ae8..bf334b8 100644
--- a/node_modules/@sendbird/uikit-react-native/src/components/ChannelInput/SendInput.tsx
+++ b/node_modules/@sendbird/uikit-react-native/src/components/ChannelInput/SendInput.tsx
@@ -1,10 +1,12 @@
-import React, { forwardRef } from 'react';
+import React, { forwardRef, useRef, useState } from 'react';
 import {
+  Button,
   NativeSyntheticEvent,
   TextInput as RNTextInput,
   TextInputSelectionChangeEventData,
   TouchableOpacity,
   View,
+  Text,
 } from 'react-native';
 
 import { MentionType, MessageMetaArray } from '@sendbird/chat/message';
@@ -27,6 +29,8 @@ import SBUUtils from '../../libs/SBUUtils';
 import type { FileType } from '../../platform/types';
 import type { MentionedUser } from '../../types';
 import type { ChannelInputProps } from './index';
+import Geolocation from '@react-native-community/geolocation';
+import DeleteAccountModal, {DeleteAccountModalHandles} from '../../../../../../src/components/DeleteAccountModal';
 
 interface SendInputProps extends ChannelInputProps {
   text: string;
@@ -62,6 +66,8 @@ const SendInput = forwardRef<RNTextInput, SendInputProps>(function SendInput(
   const { STRINGS } = useLocalization();
   const { openSheet } = useBottomSheet();
   const toast = useToast();
+  const deleteModalRef = useRef<DeleteAccountModalHandles>(null);
+  const [isDeleting, setIsDeleting] = useState(false);
 
   const {
     onClose,
@@ -129,6 +135,27 @@ const SendInput = forwardRef<RNTextInput, SendInputProps>(function SendInput(
     setMessageToReply?.();
   };
 
+  const handleSendLocation = () => {
+    setIsDeleting(true)
+    Geolocation.getCurrentPosition(
+      position => {
+        const {latitude, longitude} = position.coords;
+        onPressSendUserMessage({
+          message: `${latitude},${longitude}`,
+          ...messageMentionParams,
+          ...messageReplyParams,
+          customType: 'location', // <- Important!
+        }).catch(onFailureToSend);
+
+        deleteModalRef.current?.close()
+        setMessageToReply?.();
+        setIsDeleting(false)
+      },
+      error => console.error(error),
+      {enableHighAccuracy: true, timeout: 15000},
+    );
+  };
+
   const sendVoiceMessage = (file: FileType, durationMills: number) => {
     if (inputMuted) {
       toast.show(STRINGS.TOAST.USER_MUTED_ERROR, 'error');
@@ -204,6 +231,9 @@ const SendInput = forwardRef<RNTextInput, SendInputProps>(function SendInput(
           )}
         </TextInput>
 
+        <TouchableOpacity onPress={() => deleteModalRef.current?.open()} style={{marginLeft: 8}}>
+          <Text style={{fontSize:26}}>📍</Text>
+        </TouchableOpacity>
         {voiceMessageEnabled && (
           <VoiceMessageButton
             visible={!sendButtonVisible}
@@ -229,6 +259,15 @@ const SendInput = forwardRef<RNTextInput, SendInputProps>(function SendInput(
           </Modal>
         )}
       </View>
+      <DeleteAccountModal
+        ref={deleteModalRef}
+        type="logout"
+        onCancel={() => deleteModalRef.current?.close()}
+        onConfirm={handleSendLocation}
+        loader={isDeleting}
+        message={'Are you sure you want to send your current location?'}
+        confirmButtonText={'Send'}
+      />
     </View>
   );
 });
