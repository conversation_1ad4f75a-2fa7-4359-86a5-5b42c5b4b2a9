import React from 'react';
import {createGroupChannelBannedUsersFragment, useSendbirdChat} from '@sendbird/uikit-react-native';
import {useGroupChannel} from '@sendbird/uikit-chat-hooks';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation';

type NavigationProp = StackNavigationProp<RootStackParamList, 'SendBird'>;

const BannedList = ({route}: {route: any}) => {
  const GroupChannelBannedUsersFragment = createGroupChannelBannedUsersFragment();

  const {channelUrl} = route.params;
  const {sdk} = useSendbirdChat();
  const {channel} = useGroupChannel(sdk, channelUrl);
  const navigation = useNavigation<NavigationProp>();
  if (!channel) return null;

  return (
    <GroupChannelBannedUsersFragment
      channel={channel}
      onPressHeaderLeft={() => navigation.goBack()}
    />
  );
};

export default BannedList;
