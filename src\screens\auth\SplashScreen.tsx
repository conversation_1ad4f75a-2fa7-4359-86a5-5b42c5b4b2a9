import React, {useEffect, useState} from 'react';
import {StyleSheet, Animated, Dimensions, View} from 'react-native';
import {Images} from '../../config/images';
import {useThemeStore} from '@/store/themeStore';
import {CImage} from '@/components';

/**
 * @category Interfaces
 * @typedef {Object} SplashScreenProps
 * @property {Function} onComplete - Call when splashscreen animation completed.
 */
interface SplashScreenProps {
  onComplete: () => void;
}

/**
 * @component
 * @category Screens
 *
 * @description This splash screen appears with animation every time the app is launched.
 *
 * The screen receives the following props:
 *   - onComplete - Function which is called when splashscreen animation completed.
 *
 * @param {{onComplete: Function}} props
 *
 * @return {JSX.Element} Splash screen
 */
const SplashScreen: React.FC<SplashScreenProps> = ({onComplete}) => {
  const theme = useThemeStore();
  const [opacity] = useState(new Animated.Value(0));
  const [scale] = useState(new Animated.Value(0.8));

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.charcoal,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 999,
    },
    imageContainer: {
      width: Dimensions.get('window').width,
      height: Dimensions.get('window').height,
      position: 'absolute',
      top: 0,
      left: 0,
    },
    splashImage: {
      width: '100%',
      height: '100%',
    },
  });

  useEffect(() => {
    // Fade in animation
    Animated.timing(opacity, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();

    // Scale animation
    Animated.sequence([
      Animated.timing(scale, {
        toValue: 1.1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(scale, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // Complete animation after 2 seconds
    const timeout = setTimeout(() => {
      onComplete();
    }, 2000);

    return () => clearTimeout(timeout);
  }, [opacity, scale, onComplete]);

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.imageContainer,
          {
            opacity: opacity,
            transform: [{scale: scale}],
          },
        ]}>
        <CImage source={Images.splash} style={styles.splashImage} resizeMode="cover" />
      </Animated.View>
    </View>
  );
};

export default SplashScreen;
