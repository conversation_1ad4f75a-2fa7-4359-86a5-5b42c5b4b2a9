import {StyleSheet} from 'react-native';

const styles = (theme: any) =>
  StyleSheet.create({
    pillContainer: {
      flexGrow: 1,
      paddingBottom: 20,
    },
    separator: {
      height: 16,
    },
    headerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 5,
      marginBottom: 16,
    },

    innerView: {
      flexGrow: 1,
      borderWidth: 1,
      borderRadius: 8,
      borderColor: theme.colors.white,
      margin: 12,
      paddingHorizontal: 20,
    },
    closeButton: {
      position: 'absolute',
      top: 10,
      right: 10,
    },
    contentInnerView: {
      flex: 1,
      paddingHorizontal: 16,
    },
    submitButton: {
      backgroundColor: theme.colors.activeColor,
      borderRadius: 12,
      paddingVertical: 14,
      alignItems: 'center',
      marginTop: 24,
      marginBottom: 32,
    },
    submitButtonText: {
      color: theme.colors.black,
      fontWeight: 'bold',
      fontSize: 18,
    },
  });

export default styles;
