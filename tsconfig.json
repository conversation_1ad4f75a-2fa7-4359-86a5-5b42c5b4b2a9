{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@screens/*": ["src/screens/*"], "@utils/*": ["./src/utils/*"], "@data/*": ["./src/data/*"], "@config/*": ["src/config/*"], "@assets/*": ["./src/assets/*"], "@navigation/*": ["src/navigation/*"]}}, "include": ["src"], "exclude": ["node_modules", "babel.config.js"]}