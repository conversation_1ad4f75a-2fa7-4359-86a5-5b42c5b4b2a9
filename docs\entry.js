
    window.reactComponents = {};

    window.vueComponents = {};

  
      import React from "react";

      import ReactDOM from "react-dom";


      import ReactWrapper from '../node_modules/better-docs/lib/react-wrapper.js';

      window.React = React;

      window.ReactDOM = ReactDOM;

      window.ReactWrapper = ReactWrapper;

    
    import './styles/reset.css';

    import './styles/iframe.css';

  import Component0 from '../src/components/Advertisement/index.tsx';
reactComponents['Advertisement'] = Component0;

import Component1 from '../src/components/BiometricGate.tsx';
reactComponents['BiometricGate'] = Component1;

import Component2 from '../src/components/CButton/index.tsx';
reactComponents['CButton'] = Component2;

import Component3 from '../src/screens/CommunityDetails/index.tsx';
reactComponents['CommunityDetails'] = Component3;

import Component4 from '../src/components/createProfile/CreateGoRaqtProfile/index.tsx';
reactComponents['CreateGoRaqtProfile'] = Component4;

import Component5 from '../src/components/common/CustomMapView.tsx';
reactComponents['CustomMapView'] = Component5;

import Component6 from '../src/components/common/CustomMarker.tsx';
reactComponents['CustomMarker'] = Component6;

import Component7 from '../src/screens/auth/Login/DirectFetchTest.tsx';
reactComponents['DirectFetchTest'] = Component7;

import Component8 from '../src/navigation/DrawerNavigator.tsx';
reactComponents['DrawerNavigator'] = Component8;

import Component9 from '../src/screens/auth/EmailSignupScreen/index.tsx';
reactComponents['EmailSignupScreen'] = Component9;

import Component10 from '../src/screens/auth/ForgotPasswordScreen.tsx';
reactComponents['ForgotPasswordScreen'] = Component10;

import Component11 from '../src/screens/auth/Login/index.tsx';
reactComponents['LoginScreen'] = Component11;

import Component12 from '../src/screens/drawer/MyMatchesScreen.tsx';
reactComponents['MyMatchesScreen'] = Component12;

import Component13 from '../src/components/NotificationCard/index.tsx';
reactComponents['NotificationCard'] = Component13;

import Component14 from '../src/screens/Notification/index.tsx';
reactComponents['NotificationsListScreen'] = Component14;

import Component15 from '../src/components/common/OfferBanner.tsx';
reactComponents['OfferBanner'] = Component15;

import Component16 from '../src/screens/Permission/PermissionsScreen.tsx';
reactComponents['PermissionsScreen'] = Component16;

import Component17 from '../src/navigation/SentryNavigationContainer.tsx';
reactComponents['SentryNavigationContainer'] = Component17;

import Component18 from '../src/screens/auth/SignupScreen.tsx';
reactComponents['SignupScreen'] = Component18;

import Component19 from '../src/screens/auth/SplashScreen.tsx';
reactComponents['SplashScreen'] = Component19;

import Component20 from '../src/components/Subscription/index.tsx';
reactComponents['Subscription'] = Component20;

import Component21 from '../src/screens/TermsService/TermsOfServiceScreen.tsx';
reactComponents['TermsOfServiceScreen'] = Component21;

import Component22 from '../src/screens/auth/Verification.tsx';
reactComponents['Verification'] = Component22;