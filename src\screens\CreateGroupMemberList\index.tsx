import React, {useState} from 'react';
import {View, TouchableOpacity, FlatList, ActivityIndicator} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import Typography from '@/components/Typography';
import {useNavigation, RouteProp} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import {createStyles} from './styles';
import CInput from '@/components/CInput';
import {Icon, SafeAreaView} from '@/components';
import CImage from '@/components/CImage';
import useTranslation from '@/hooks/useTranslation';
import UploadComponent from '@/components/Cupload/Index';
import {useCreateUserGroup} from '@/hooks/queries/groups';
import {Controller, useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {useUploadFile} from '@/hooks/queries/useUsers';
import {toaster} from '@/utils/commonFunctions';
import {useAuthStore} from '@/store';

const schema = yup.object().shape({
  groupImage: yup.string().nullable(),
  groupName: yup.string().required('Group name is required'),
});

type NavigationProp = StackNavigationProp<CommunityStackParamList>;
type CreateGroupMemberListParams = {
  groupImage?: string;
  groupName?: string;
  members: Array<{id: string; name: string; profile_pic: string}>;
  groupType: 'Public' | 'Private' | 'Hidden';
  favoriteLocation: string;
  // add any other params you expect here
};
type CreateGroupMemberListRouteProp = RouteProp<
  {CreateGroupMemberList: CreateGroupMemberListParams},
  'CreateGroupMemberList'
>;

type FormValues = {
  groupImage: string | null;
  groupName: string;
  groupType: 'Public' | 'Private' | 'Hidden';
  location: string;
  tags: string | string[];
};

const CreateGroupMemberList: React.FC<{route: CreateGroupMemberListRouteProp}> = ({route}) => {
  const data = route.params;
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp>();
  const [selectedMembers, setSelectedMembers] = useState(data?.members);
  console.log('selectedMembers=====>>>>>', selectedMembers);
  const createGroupMutation = useCreateUserGroup();
  const imageUpload = useUploadFile();

  const {t} = useTranslation();

  const {user} = useAuthStore();
  console.log('user=====>>>>>', user);

  const {
    control,
    handleSubmit,
    formState: {errors},
    setValue,
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
    defaultValues: {
      groupImage: data?.groupImage || null,
      groupName: data?.groupName || '',
      groupType: data?.groupType || 'Public',
      location: '2', // this should be added static because currently we are sure what exact data comes hear
      tags: data?.tags || '',
    },
  });

  const onSubmit: (item: FormValues) => void = item => {
    console.log('item', item);
    const formattedData = {
      group_name: item?.groupName,
      group_type: item?.groupType,
      group_image: item?.groupImage,
      location: '2', // this should be added static because currently we are sure what exact data comes hear
      description: '',
      users: [
        ...selectedMembers,
        {
          id: user?.id,
          name: user?.name,
          image: user?.profile_pic,
        },
      ],
      tags: [item?.tags], // this should be added static because currently we are sure what exact data comes hear
    };

    createGroupMutation.mutate(formattedData as any, {
      onSuccess: response => {
        navigation.popToTop();
        toaster('success', response.message, 'top');
      },
      onError: error => {
        toaster('error', error.message, 'top');
      },
    });
  };

  const handleUpload = (imageObj: Array<any>) => {
    const files = imageObj.map(img => ({
      uri: img?.path,
      name: img?.filename,
      type: img?.mime,
      size: img?.size,
    }));
    imageUpload.mutate(
      {files},
      {
        onSuccess: response => {
          if (response?.status) {
            setValue('groupImage', response?.data[0]);
          }
        },
        onError: error => {
          toaster('error', error.message, 'top');
        },
      },
    );
  };

  return (
    <SafeAreaView includeTop={true} includeBottom={false}>
      <View style={styles.content}>
        <View style={styles.headerRow}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Typography variant="parkTitle" color={theme.colors.primary}>
              {t('common.back')}
            </Typography>
          </TouchableOpacity>
          <Typography variant="parkTitle" color={theme.colors.white}>
            {t('createGroupMemberListScreen.newGroup')}
          </Typography>
          {createGroupMutation.isPending ? (
            <ActivityIndicator size="small" color={theme.colors.primary} />
          ) : (
            <TouchableOpacity onPress={handleSubmit(onSubmit as any)}>
              <Typography variant="parkTitle" color={theme.colors.activeColor}>
                {t('common.create')}
              </Typography>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.groupNameContainer}>
          <Controller
            control={control}
            name="groupImage"
            render={({field: {value}, fieldState: {error}}) => (
              <UploadComponent
                onSelected={e => handleUpload(Array.isArray(e) ? e : [e])}
                value={value}
                allowCameraCapture={true}
                allowGallerySelection={true}
                containerStyle={styles.cameraCircle}
                imageContentStyle={styles.imageContent}
              />
            )}
          />
          <Controller
            control={control}
            name="groupName"
            render={({field: {onChange, value, onBlur}}) => (
              <View style={{flex: 1}}>
                <CInput
                  containerStyle={styles.groupNameInput}
                  placeholder={t('newGroupScreen.groupNamePlaceholder')}
                  inputStyle={styles.groupNameInput}
                  placeholderTextColorStyle={theme.colors.activeColor}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                />
              </View>
            )}
          />
        </View>
        {errors.groupName?.message && (
          <Typography style={[styles.errorText, {marginBottom: 20}]}>
            {errors.groupName?.message}
          </Typography>
        )}
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.groupSettings}
          // onPress={() =>
          //   navigation.navigate('NewGroup', {
          //     ...data,
          //     members: selectedMembers,
          //   })
          // }
        >
          <Typography variant="notificationText" color={theme.colors.white}>
            {t('createGroupMemberListScreen.groupSettings')}
          </Typography>
          <Icon name="Right-chevron" size={20} color={theme.colors.white} />
        </TouchableOpacity>

        <View style={styles.selectedMembersContainer}>
          <Typography
            style={styles.selectedMembersTitle}
            variant="notificationText"
            color={theme.colors.white}>
            {t('createGroupMemberListScreen.members')} {selectedMembers.length}{' '}
            {t('createGroupMemberListScreen.of')} 25
          </Typography>
          <FlatList
            data={selectedMembers}
            horizontal
            keyExtractor={item => item.id}
            contentContainerStyle={styles.memberList}
            renderItem={({item}) => (
              <View style={styles.memberContainer}>
                <View style={styles.memberAvatarContainer}>
                  <CImage source={{uri: item.profile_pic}} style={styles.memberAvatar} />
                  {selectedMembers?.length > 1 && (
                    <TouchableOpacity
                      style={styles.closeButton}
                      onPress={() => {
                        setSelectedMembers(prev => prev.filter(m => m.id !== item.id));
                      }}>
                      <Icon name="close" size={12} color={theme.colors.black} />
                    </TouchableOpacity>
                  )}
                </View>
                <Typography
                  style={styles.userName}
                  color={theme.colors.inputLabel}
                  variant="caption">
                  {item.name || item.display_name}
                </Typography>
              </View>
            )}
            showsHorizontalScrollIndicator={false}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default CreateGroupMemberList;
