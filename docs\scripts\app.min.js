"use strict";$().ready(function(){});var sidebarIsVisible=!1,toggleSidebar=function(e){var a=!(0<arguments.length&&void 0!==e)||e;$("#sidebarNav").toggleClass("sticky",a),$("#stickyNavbarOverlay").toggleClass("active",a),$("#hamburger").toggleClass("is-active"),sidebarIsVisible=a};$().ready(function(){$("#hamburger").click(function(){toggleSidebar(!sidebarIsVisible)}),$("#stickyNavbarOverlay").click(function(){sidebarIsVisible&&toggleSidebar(!1)})});var OFFSET=150;$().ready(function(){var o=$("#side-nav"),c=[];if($(".vertical-section").length||o.hide(),$(".vertical-section").each(function(e,a){var i=$(a),t=i.find("> h1").text();if(t){o.append($("<h3/>").text(t));var s=$("<ul></ul>");i.find(".members h4.name").each(function(e,a){var i=$(a),t=i.find(".code-name").clone().children().remove().end().text(),n=i.find("a").attr("href"),r=$('<a href="'.concat(n,'" />')).text(t);s.append($("<li></li>").append(r)),c.push({link:r,offset:i.offset().top})}),o.append(s)}else i.find(".members h4.name").each(function(e,a){var i=$(a),t=i.find(".code-name").clone().children().remove().end().text(),n=i.find("a").attr("href"),r=$('<a href="'.concat(n,'" />')).text(t);o.append(r),c.push({link:r,offset:i.offset().top})})}),!$.trim(o.text()))return o.hide();function e(){for(var e=n.scrollTop(),a=!1,i=c.length-1;0<=i;i--){var t=c[i];t.link.removeClass("is-active"),e+OFFSET>=t.offset?a?t.link.addClass("is-past"):(t.link.addClass("is-active"),a=!0):t.link.removeClass("is-past")}}var n=$("#main-content-wrapper");n.on("scroll",e),e(),c.forEach(function(e){e.link.click(function(){n.animate({scrollTop:e.offset-OFFSET+1},500)})})}),$().ready(function(){$("#sidebarNav a").each(function(e,a){var i=$(a).attr("href");window.location.pathname.match("/"+i)&&($(a).addClass("active"),$("#sidebarNav").scrollTop($(a).offset().top-150))})});