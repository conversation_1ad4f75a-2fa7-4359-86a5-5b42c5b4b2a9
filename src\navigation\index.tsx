import {NavigationContainerRef} from '@react-navigation/native';
import {createStackNavigator, StackScreenProps} from '@react-navigation/stack'; // ✅ Switched to stack
import React, {useState, forwardRef} from 'react';
import {useConfigStore} from '@/store/configStore';
import DrawerNavigator from './DrawerNavigator';
import ExamplesStack from './ExamplesStack';
import PermissionsScreen from '@/screens/Permission/PermissionsScreen';
import TermsOfServiceScreen from '@/screens/TermsService/TermsOfServiceScreen';
import LoginScreen from '@/screens/auth/Login';
import SplashScreen from '@/screens/Splash/SplashScreen';
import {
  EmailLoginScreen,
  EmailSignupScreen,
  ForgotPasswordScreen,
  SignupScreen,
} from '@/screens/auth';
import SettingsScreen from '@/screens/settings/SettingsScreen';
import BiometricsScreen from '@/screens/settings/BiometricsScreen';
import {NotificationsScreen, ReferFriendScreen, MyMatchesScreen} from '@/screens/drawer';
import NotificationsListScreen from '@/screens/Notification';
import {useAuthStore} from '@/store/authStore';
import CartScreen from '@/screens/tabs/SearchScreen/CartScreen';
import GoFit from '@/screens/GoFit';
import SentryNavigationContainer from './SentryNavigationContainer';
import TermsAndConditionsScreen from '@/screens/TermsAndConditions';
import {useQuery} from '@tanstack/react-query';
import api from '@/services/api';
import Verification from '@/screens/auth/Verification';
import CToast from '@/components/CToast';
import MyGroups from '@/screens/MyGroups';
import NewGroup from '@/components/Community/NewGroup';
import JoinGroups from '@/screens/JoinGroups';
import JoinGroupDetails from '@/screens/JoinGroupDetails';
import AddMembers from '@/components/Community/AddMembers';
import {PlayerConnectScreen} from '@/screens';
import GoEats from '@/screens/GoEats';
import GoTravel from '@/screens/GoTravel';
import CommunityDetails from '@/screens/CommunityDetails';
import CoachProfile from '@/screens/UpYourGame/CoachProfile';
import CommentScreen from '@/screens/CommentScreen';
import FindCoach from '@/screens/UpYourGame/FindCoach';
import FindClass from '@/screens/UpYourGame/FindClass';
import CreateGroupMemberList from '@/screens/CreateGroupMemberList';
import ChatScreen from '@/screens/Chat';
import PlayerConnectDateScreen from '@/screens/PlayerConnectDateScreen';
import sendBird from '@/screens/sendBird';
import GroupSetting from '@/screens/sendBird/GroupSetting';
import MemberList from '@/screens/sendBird/MemberList';
import GroupModeration from '@/screens/sendBird/GroupModeration';
import BannedList from '@/screens/sendBird/BannedList';
import MutedList from '@/screens/sendBird/MutedList';
import OperatorList from '@/screens/sendBird/OperatorList';
import ManageOperator from '@/screens/sendBird/ManageOperator';
import GroupList from '@/screens/GroupList';
import InviteUser from '@/screens/sendBird/InviteUser';
import RewardsScreen from '@/screens/Rewards';
import ManageCommunityScreen from '@/screens/ManageCommunity';
import OrdersScreen from '@/screens/Orders';
import HelpScreen from '@/screens/Help';
import RecycleBallsScreen from '@/screens/RecycleBalls';
import RacquetsBrandsScreen from '@/screens/tabs/RacquetsScreens/Brands';
import RacquetCategoryScreen from '@/screens/tabs/RacquetsScreens/RacqetsCategory';
import RequestList from '@/screens/RequestList';

export type RootStackParamList = {
  Splash: undefined;
  Permissions: undefined;
  TermsOfService: undefined;
  Login: {email?: string};
  Signup: undefined;
  EmailLogin: undefined;
  Verification: {email: string; password?: string; type?: 'forgotPassword'};
  EmailSignup: undefined;
  ForgotPassword: {email?: string};
  MainTabs: undefined;
  Drawer: {
    screen?: string;
    params?: {
      screen?: string;
      params?: {
        screen?: string;
        params?: any;
      };
    };
  };
  MyGroups: undefined;
  GroupList: {title?: string; type?: string};
  NewGroup: {groupName?: string};
  JoinGroups: undefined;
  JoinGroupDetails: {id: string; from?: string};
  AddMembers: {groupName: string};
  CreateGroupMemberList: {
    groupImage?: string;
    groupName?: string;
    members: Array<{id: string; name: string; image: string}>;
  };
  Chat: undefined;
  SendBird: {
    channelUrl: string;
    group_id: string;
  };
  GroupSetting: {
    channelUrl: string;
    group_id: string;
  };
  MemberList: {
    channelUrl: string;
  };
  GroupModeration: {
    channelUrl: string;
  };
  BannedList: {
    channelUrl: string;
  };
  MutedList: {
    channelUrl: string;
  };
  OperatorList: {
    channelUrl: string;
  };
  ManageOperator: {
    channelUrl: string;
  };
  InviteUser: {
    channelUrl: string;
  };
  CommunityDetails: {
    from?: string;
    title?: string;
    data?: any;
  };
  CommentScreen: {
    from?: string;
    title?: string;
    data?: any;
  };
  CoachProfile: undefined;
  FindCoach: undefined;
  FindClass: undefined;
  RacquetCategory: {sportsTitle: string};
  RacquetBrands: undefined;
  PlayerConnectDateScreen: undefined;
  PlayerConnectScreen: undefined;
  GoEats: undefined;
  GoTravel: undefined;
  Biometrics: undefined;
  Settings: undefined;
  Notifications: undefined;
  ReferFriend: undefined;
  MyMatches: undefined;
  NotificationsList: undefined;
  Rewards: undefined;
  ManageCommunity: undefined;
  Orders: undefined;
  Help: undefined;
  RecycleBalls: undefined;
  CartScreen: undefined;
  GoFit: undefined;
  Examples: undefined;
  TermsAndConditions: undefined;
  RequestList: {
    groupId?: string;
  };
};

export type RootStackScreenProps<T extends keyof RootStackParamList> = StackScreenProps<
  RootStackParamList,
  T
>;

interface DrawerMenuItem {
  id: string;
  label: string;
  title: string;
  screen?: string;
  isVisible: boolean;
}

interface ApiResponse {
  status: boolean;
  data: {
    language: string;
    json_data: DrawerMenuItem[];
  };
}

interface CommunityFeaturesResponse {
  status: boolean;
  data: {
    language: string;
    json_data: DrawerMenuItem[];
  };
}

const Stack = createStackNavigator<RootStackParamList>();

const navigationRef = React.createRef<NavigationContainerRef<any>>();
export {navigationRef};

const AppNavigator = forwardRef<NavigationContainerRef<RootStackParamList>>((props, ref) => {
  const {isAuthenticated} = useAuthStore();
  const {permissions, setPermissions} = useConfigStore();

  const [showSplash, setShowSplash] = useState(true);
  const [showPermissions, setShowPermissions] = useState(false);
  const [showTerms, setShowTerms] = useState(false);

  const handleSplashComplete = () => {
    setShowSplash(false);
    if (
      permissions?.location !== true ||
      permissions?.bluetooth !== true ||
      permissions?.notification !== true
    ) {
      setShowPermissions(true);
    } else if (
      !permissions?.termsAndConditions &&
      permissions?.location === true &&
      permissions?.bluetooth === true &&
      permissions?.notification === true
    ) {
      setShowTerms(true);
    }
  };

  const handlePermissionsComplete = () => {
    setShowPermissions(false);
    if (!permissions?.termsAndConditions) {
      setShowTerms(true);
    }
  };

  const handleTermsAccepted = () => {
    setShowTerms(false);
    setPermissions({...permissions, termsAndConditions: true});
  };

  // Fetch app configurations
  useQuery<ApiResponse>({
    queryKey: ['app-config'],
    queryFn: async () => {
      try {
        const response = await api.get('/app-configuration/app_features');
        if (response.data.status) {
          useConfigStore.getState().setDrawerMenuItems(response.data.data.json_data);
          return response.data;
        }
      } catch (error) {
        console.error('Error fetching app config:', error);
        return null;
      }
    },
  });

  // Fetch community features
  useQuery<CommunityFeaturesResponse>({
    queryKey: ['community-features'],
    queryFn: async () => {
      try {
        const response = await api.get('/app-configuration/community_features');
        if (response.data.status) {
          useConfigStore.getState().setCommunityFeatures(response.data.data.json_data);
          return response.data;
        }
      } catch (error) {
        console.error('Error fetching community features:', error);
        return null;
      }
    },
  });

  const stackProps: any = {animation: 'slide_from_right', presentation: 'transparentModal'};

  return (
    <SentryNavigationContainer
      ref={navigationRef as unknown as React.RefObject<NavigationContainerRef<any>>}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: true,
          gestureDirection: 'horizontal', // 👈 Required for swipe-back on Android
        }}>
        {showSplash ? (
          <Stack.Screen
            name="Splash"
            options={{animation: 'fade', presentation: 'transparentModal'}}>
            {() => <SplashScreen onComplete={handleSplashComplete} />}
          </Stack.Screen>
        ) : showPermissions ? (
          <Stack.Screen
            name="Permissions"
            options={{animation: 'fade', presentation: 'transparentModal'}}>
            {() => <PermissionsScreen onComplete={handlePermissionsComplete} />}
          </Stack.Screen>
        ) : showTerms ? (
          <Stack.Screen
            name="TermsOfService"
            options={{animation: 'fade', presentation: 'transparentModal'}}>
            {() => <TermsOfServiceScreen onAccept={handleTermsAccepted} />}
          </Stack.Screen>
        ) : !isAuthenticated ? (
          <>
            <Stack.Screen
              name="Signup"
              component={SignupScreen}
              options={{
                animation: 'fade',
                // presentation: 'transparentModal',
              }}
            />
            <Stack.Screen
              name="Login"
              component={LoginScreen}
              options={{
                animation: 'fade_from_bottom',
                // presentation: 'transparentModal',
              }}
            />
            <Stack.Screen
              name="EmailLogin"
              component={EmailLoginScreen}
              options={{
                animation: 'fade',
                // presentation: 'transparentModal',
              }}
            />
            <Stack.Screen
              name="EmailSignup"
              component={EmailSignupScreen}
              options={{
                animation: 'fade_from_bottom',
                // presentation: 'transparentModal',
              }}
            />
            <Stack.Screen
              name="ForgotPassword"
              component={ForgotPasswordScreen}
              options={{
                animation: 'fade',
                // presentation: 'transparentModal',
              }}
            />
            <Stack.Screen
              name="Verification"
              component={Verification}
              options={{
                animation: 'fade',
                // presentation: 'transparentModal',
              }}
            />
            <Stack.Screen
              name="TermsAndConditions"
              component={TermsAndConditionsScreen}
              options={{
                animation: 'fade',
                //  presentation: 'transparentModal'
              }}
            />
          </>
        ) : (
          <>
            <Stack.Screen name="Drawer" component={DrawerNavigator} />
            <Stack.Screen name="MyGroups" component={MyGroups} options={stackProps} />
            <Stack.Screen name="NewGroup" component={NewGroup} options={stackProps} />
            <Stack.Screen name="JoinGroups" component={JoinGroups} options={stackProps} />
            <Stack.Screen
              name="JoinGroupDetails"
              component={JoinGroupDetails}
              options={stackProps}
            />
            <Stack.Screen name="GroupList" component={GroupList} options={stackProps} />
            <Stack.Screen name="AddMembers" component={AddMembers} options={stackProps} />
            <Stack.Screen
              name="PlayerConnectScreen"
              component={PlayerConnectScreen}
              options={stackProps}
            />
            <Stack.Screen name="GoEats" component={GoEats} options={stackProps} />
            <Stack.Screen name="GoTravel" component={GoTravel} options={stackProps} />
            <Stack.Screen name="GoFit" component={GoFit} options={stackProps} />
            <Stack.Screen
              name="CommunityDetails"
              component={CommunityDetails}
              options={stackProps}
            />
            <Stack.Screen name="CommentScreen" component={CommentScreen} options={stackProps} />
            <Stack.Screen name="CoachProfile" component={CoachProfile} options={stackProps} />
            <Stack.Screen name="FindCoach" component={FindCoach} options={stackProps} />
            <Stack.Screen name="FindClass" component={FindClass} options={stackProps} />
            <Stack.Screen
              name="RacquetCategory"
              component={RacquetCategoryScreen as any}
              options={stackProps}
            />
            <Stack.Screen
              name="CreateGroupMemberList"
              component={CreateGroupMemberList as any}
              options={stackProps}
            />
            <Stack.Screen
              name="RacquetBrands"
              component={RacquetsBrandsScreen as any}
              options={stackProps}
            />
            <Stack.Screen name="Chat" component={ChatScreen} options={stackProps} />
            <Stack.Screen name="SendBird" component={sendBird} options={stackProps} />
            <Stack.Screen name="GroupSetting" component={GroupSetting} options={stackProps} />
            <Stack.Screen name="MemberList" component={MemberList} options={stackProps} />
            <Stack.Screen name="GroupModeration" component={GroupModeration} options={stackProps} />
            <Stack.Screen name="BannedList" component={BannedList} options={stackProps} />
            <Stack.Screen name="MutedList" component={MutedList} options={stackProps} />
            <Stack.Screen name="OperatorList" component={OperatorList} options={stackProps} />
            <Stack.Screen name="ManageOperator" component={ManageOperator} options={stackProps} />
            <Stack.Screen name="InviteUser" component={InviteUser} options={stackProps} />
            <Stack.Screen
              name="PlayerConnectDateScreen"
              component={PlayerConnectDateScreen}
              options={stackProps}
            />
            <Stack.Screen name="Settings" component={SettingsScreen} options={stackProps} />
            <Stack.Screen name="Biometrics" component={BiometricsScreen} options={stackProps} />
            <Stack.Screen
              name="Notifications"
              component={NotificationsScreen}
              options={stackProps}
            />
            <Stack.Screen
              name="NotificationsList"
              component={NotificationsListScreen}
              options={stackProps}
            />
            <Stack.Screen name="Rewards" component={RewardsScreen} options={stackProps} />
            <Stack.Screen
              name="ManageCommunity"
              component={ManageCommunityScreen}
              options={stackProps}
            />
            <Stack.Screen name="Orders" component={OrdersScreen} options={stackProps} />
            <Stack.Screen name="CartScreen" component={CartScreen} options={stackProps} />
            <Stack.Screen name="Help" component={HelpScreen} options={stackProps} />
            <Stack.Screen name="RecycleBalls" component={RecycleBallsScreen} options={stackProps} />
            <Stack.Screen name="ReferFriend" component={ReferFriendScreen} options={stackProps} />
            <Stack.Screen name="MyMatches" component={MyMatchesScreen} options={stackProps} />
            <Stack.Screen name="Examples" component={ExamplesStack} options={stackProps} />
            <Stack.Screen
              name="TermsAndConditions"
              component={TermsAndConditionsScreen}
              options={stackProps}
            />
            <Stack.Screen name="RequestList" component={RequestList} options={stackProps} />
          </>
        )}
      </Stack.Navigator>
    </SentryNavigationContainer>
  );
});

AppNavigator.displayName = 'AppNavigator';

export {AppNavigator};
