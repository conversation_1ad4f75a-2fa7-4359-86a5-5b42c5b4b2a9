import React, {useState} from 'react';
import {View, StyleSheet, ImageBackground} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import Typography from '@/components/Typography';
import {Images} from '@/config';
import CButton from '@/components/CButton';
import {Icons} from '@/config/icons';
import {useEnhancedNetwork} from '@/context/EnhancedNetworkContext';
import {SafeAreaView} from '@/components';

interface NoInternetScreenProps {
  onRetry?: () => void;
}

const NoInternetScreen: React.FC<NoInternetScreenProps> = ({onRetry}) => {
  const theme = useThemeStore();
  const [isRetrying, setIsRetrying] = useState(false);
  const {checkConnection} = useEnhancedNetwork();

  const handleRetry = async () => {
    if (isRetrying) return;

    setIsRetrying(true);

    try {
      // Use the enhanced network context to check connection
      const isConnected = await checkConnection();

      if (isConnected) {
        // If we're connected, call the onRetry callback
        if (onRetry) {
          onRetry();
        }
      }
    } catch (error) {
      console.error('Error checking connection:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  return (
    <ImageBackground source={Images.gradientBg} style={styles.backgroundImage}>
      {/* <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} /> */}
      <SafeAreaView includeBottom={false} style={styles.container}>
        <View style={styles.content}>
          <View style={styles.iconContainer}>
            <Icons.Feather name="wifi-off" size={80} color={theme.colors.white} />
          </View>

          <Typography
            variant="title"
            color={theme.colors.white}
            align="center"
            style={styles.title}>
            No Internet Connection
          </Typography>

          <Typography
            variant="body"
            color={theme.colors.white}
            align="center"
            style={styles.message}>
            Please check your internet connection and try again
          </Typography>

          <CButton
            title={isRetrying ? 'Checking...' : 'Retry'}
            variant="primary"
            onPress={handleRetry}
            loading={isRetrying}
            isDisabled={isRetrying}
            containerStyle={styles.retryButton}
          />
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    width: '80%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    marginBottom: 24,
  },
  title: {
    marginBottom: 16,
  },
  message: {
    marginBottom: 32,
  },
  retryButton: {
    width: '100%',
    marginTop: 16,
  },
});

export default NoInternetScreen;
