import React, {useEffect, useState, useCallback} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, SafeAreaView, Image, Alert} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {authenticateWithBiometrics, isBiometricsEnabled} from '@utils/biometrics';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import {Images} from '@/config';
import CImage from '@/components/CImage';

/**
 * @category Interfaces
 * @typedef {Object} BiometricGateProps
 * @property {React.ReactNode} children - The child elements rendered inside the biometric gate
 */
interface BiometricGateProps {
  children: React.ReactNode;
}

/**
 * @component
 * @category Components
 * @description
 * A component that acts as a biometric authentication gate for accessing its children.
 *
 * This component checks if biometric authentication is enabled and, if so, prompts
 * the user to authenticate using biometrics before granting access to its children.
 * It displays a loading state while checking biometric settings and shows an
 * authentication screen if biometrics are enabled but not authenticated.
 *
 * @param {BiometricGateProps} props - The properties for the BiometricGate component
 * @returns {JSX.Element | null} - Returns the children if authenticated or biometrics are not enabled,
 * or a biometric authentication screen if biometrics are enabled but not authenticated.
 */

const BiometricGate: React.FC<BiometricGateProps> = ({children}) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [biometricsEnabled, setBiometricsEnabled] = useState(false);
  const [loading, setLoading] = useState(true);
  const theme = useThemeStore();

  /**
   * @function promptBiometricAuth
   * @memberof BiometricGate
   * @description
   * Prompts the user for biometric authentication.
   * If authentication fails, it shows an alert and allows the user to retry.
   * If an error occurs during authentication, it shows an error alert.
   * @returns {Promise<void>} - A promise that resolves when the authentication process is complete.
   */
  const promptBiometricAuth = useCallback(async () => {
    try {
      const success = await authenticateWithBiometrics('Authenticate to access GoRaqt');
      setIsAuthenticated(success);

      if (!success) {
        // Authentication failed
        Alert.alert('Authentication Failed', 'Please try again to access the app.', [
          {text: 'Try Again', onPress: () => promptBiometricAuth()},
        ]);
      }
    } catch (error) {
      console.error('Error during authentication:', error);
      Alert.alert(
        'Authentication Error',
        'An error occurred during authentication. Please try again.',
        [{text: 'Try Again', onPress: () => promptBiometricAuth()}],
      );
    }
  }, []);

  useEffect(() => {
    const checkBiometrics = async () => {
      try {
        const enabled = await isBiometricsEnabled();
        setBiometricsEnabled(enabled);

        if (!enabled) {
          // If biometrics are not enabled, skip authentication
          setIsAuthenticated(true);
        } else {
          // Automatically trigger authentication when biometrics are enabled
          promptBiometricAuth();
        }
      } catch (error) {
        console.error('Error checking biometrics:', error);
        setIsAuthenticated(true); // Allow access on error
      } finally {
        setLoading(false);
      }
    };

    checkBiometrics();
  }, [promptBiometricAuth]);

  // Show content immediately if biometrics are not enabled or after successful authentication
  if (loading) {
    return null; // Show nothing while loading
  }

  if (!biometricsEnabled || isAuthenticated) {
    return <>{children}</>;
  }

  // Show biometric authentication screen only if biometrics are enabled and not authenticated
  return (
    <SafeAreaView style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <View style={styles.content}>
        <View style={styles.logoContainer}>
          <CImage source={Images.logo} style={styles.logo} resizeMode="contain" />
        </View>

        <Text style={[styles.title, {color: theme.colors.text}]}>Unlock GoRaqt</Text>

        <Text style={[styles.description, {color: theme.colors.text}]}>
          Please authenticate to access your account
        </Text>

        <TouchableOpacity
          style={[styles.authButton, {backgroundColor: theme.colors.primary}]}
          onPress={promptBiometricAuth}>
          <FontAwesome name="fingerprint" size={30} color={theme.colors.white} />
        </TouchableOpacity>

        <Text style={[styles.buttonText, {color: theme.colors.text}]}>Tap to authenticate</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  logoContainer: {
    marginBottom: 40,
  },
  logo: {
    width: 120,
    height: 120,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 40,
    opacity: 0.8,
  },
  authButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  buttonText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default BiometricGate;
