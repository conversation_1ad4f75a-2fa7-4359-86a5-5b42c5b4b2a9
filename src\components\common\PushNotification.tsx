import {useEffect} from 'react';
import {AuthorizationStatus, getMessaging} from '@react-native-firebase/messaging';
import {getApp} from '@react-native-firebase/app';
import PushNotification from 'react-native-push-notification';
import {PermissionsAndroid, Platform} from 'react-native';
import {useNotificationStore} from '@/store/notificationStore';
import {handleNotificationNavigation, showNotification} from './NotificationHelper';
import {tokenStorage} from '@/services/api';
import {openSettings} from 'react-native-permissions';
import {saveFcmTokenInDB} from '@/services/notificationsApi';
// Store FCM token using MMKV
export const storeFCMToken = async (token: string | null) => {
  try {
    const notificationStore = useNotificationStore.getState();
    notificationStore.setFcmToken(token);
    console.log('FCM Token stored successfully:', token);
  } catch (error) {
    console.error('Error storing FCM token:', error);
  }
};

export const getFCMToken = async () => {
  try {
    const app = getApp();
    const messaging = getMessaging(app);
    const token = await messaging.getToken();
    console.log('FCM Token', token);
    return token;
  } catch (e) {
    console.log('FCM token error =>', e);
  }
};

export const deleteFcmToken = async () => {
  try {
    const app = getApp();
    const messaging = getMessaging(app);
    await messaging.deleteToken();
    console.log('FCM Token deleted');
    storeFCMToken('');
  } catch (e) {
    console.log('Error deleting FCM token:', e);
  }
};

export const requestNotificationPermission = async () => {
  const app = getApp();
  const messaging = getMessaging(app);

  if (Platform.OS === 'ios') {
    const authStatus = await messaging.requestPermission();
    return (
      authStatus === AuthorizationStatus.AUTHORIZED ||
      authStatus === AuthorizationStatus.PROVISIONAL
    );
  } else if (Platform.OS === 'android') {
    const status = await PermissionsAndroid.check(
      PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
    );

    if (!status) {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
      );

      if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
        // Open app settings manually
        console.log('Permission denied permanently. Opening settings...');
        openSettings().catch(() => {
          console.warn('Unable to open settings');
        });
        return false;
      }

      return granted === PermissionsAndroid.RESULTS.GRANTED;
    }

    return true; // Already granted
  }

  return false;
};

const RemotePushNotification = () => {
  const notificationStore = useNotificationStore();
  const accessToken = tokenStorage.getString('accessToken');

  useEffect(() => {
    const setup = async () => {
      const app = getApp();
      const messaging = getMessaging(app);

      // const hasPermission = await requestNotificationPermission();
      // if (hasPermission) {
      //   console.log('Notification permission granted');
      // } else {
      //   console.log('Notification permission denied');
      //   return;
      // }

      PushNotification.configure({
        onNotification: function (notification: any) {
          console.log('notification configure ===>', notification);
          if (notification?.userInteraction && notification?.foreground) {
            handleNotificationNavigation(notification);
          }
        },
        popInitialNotification: true,
        requestPermissions: false, // already handled manually
      });

      PushNotification.createChannel(
        {
          channelId: '1',
          channelName: 'Goraqt',
          channelDescription: 'Goraqt default channel',
          soundName: 'default',
          importance: 4,
          vibrate: true,
        },
        created => console.log(`createChannel returned '${created}'`),
      );

      messaging.onNotificationOpenedApp((remoteMessage: any) => {
        const {data, notification} = remoteMessage;
        console.log('remoteMessage background state:', remoteMessage);
        handleNotificationNavigation(remoteMessage);

        if (data?.action) {
          notificationStore.setLastNotification({
            title: notification?.title,
            body: notification?.body,
            data,
            action: data?.action,
            meta: data?.meta,
          });
        }
      });

      const initialNotification = await messaging.getInitialNotification();
      if (initialNotification) {
        console.log('remoteMessage quit state:', initialNotification);
        setTimeout(() => {
          handleNotificationNavigation(initialNotification);
        }, 4000);
      }

      const token = await messaging.getToken();
      console.log('FCM Token:', token);
      saveFcmTokenInDB();
      storeFCMToken(token);

      messaging.onTokenRefresh(newToken => {
        console.log('FCM Token refreshed:', newToken);
        storeFCMToken(newToken);
      });
    };
    if (accessToken) {
      setup();
    }
  }, [accessToken]);

  useEffect(() => {
    const app = getApp();
    const messaging = getMessaging(app);

    const unsubscribe = messaging.onMessage(async remoteMessage => {
      console.log('onMessage ===>', remoteMessage);
      showNotification(remoteMessage, true);
    });

    return unsubscribe;
  }, []);

  return null;
};

export default RemotePushNotification;
