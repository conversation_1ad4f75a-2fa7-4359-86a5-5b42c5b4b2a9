import React, {useState, useMemo, useRef, useEffect, useCallback} from 'react';
import {Text, TouchableOpacity, StyleSheet, Dimensions, ScrollView, View} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import CustomModal from '../Modal';

interface YearPickerModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  initialYear?: number;
  onYearSelected: (year: number) => void;
}

const ITEM_HEIGHT = 50;

const ModalYearPicker: React.FC<YearPickerModalProps> = ({
  visible,
  onClose,
  title,
  initialYear = new Date().getFullYear(),
  onYearSelected,
}) => {
  const theme = useThemeStore();

  const years = useMemo(() => {
    const currentYear = new Date().getFullYear();
    return Array.from({length: 60}, (_, i) => currentYear - 59 + i);
  }, []);

  const initialIndex = useMemo(() => {
    const index = years.indexOf(initialYear);
    return index >= 0 ? index : years.length - 1; // Default to current year
  }, [years, initialYear]);

  const scrollViewRef = useRef<ScrollView>(null);
  const [selectedIndex, setSelectedIndex] = useState(initialIndex);
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInitializedRef = useRef(false);

  // Reset and initialize when modal becomes visible
  useEffect(() => {
    if (visible) {
      const index = years.indexOf(initialYear);
      const validIndex = index >= 0 ? index : years.length - 1;

      setSelectedIndex(validIndex);
      setIsScrolling(false);
      isInitializedRef.current = false;

      // Initialize scroll position after a short delay
      const initTimer = setTimeout(() => {
        if (scrollViewRef.current && !isInitializedRef.current) {
          scrollViewRef.current.scrollTo({
            y: validIndex * ITEM_HEIGHT,
            animated: false,
          });
          isInitializedRef.current = true;
        }
      }, 100);

      return () => clearTimeout(initTimer);
    } else {
      isInitializedRef.current = false;
    }
  }, [visible, initialYear, years]);

  // Debounced scroll handler
  const handleScroll = useCallback(
    (event: any) => {
      if (!isInitializedRef.current) return;

      const offsetY = event.nativeEvent.contentOffset.y;
      const index = Math.round(offsetY / ITEM_HEIGHT);

      // Clear existing timeout
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // Only update if index is valid and different
      if (index >= 0 && index < years.length && index !== selectedIndex) {
        setSelectedIndex(index);
      }
    },
    [selectedIndex, years.length],
  );

  // Handle scroll begin
  const handleScrollBeginDrag = useCallback(() => {
    setIsScrolling(true);
  }, []);

  // Handle scroll end with snapping
  const handleMomentumScrollEnd = useCallback(
    (event: any) => {
      if (!isInitializedRef.current) return;

      const offsetY = event.nativeEvent.contentOffset.y;
      const index = Math.round(offsetY / ITEM_HEIGHT);

      if (index >= 0 && index < years.length) {
        setSelectedIndex(index);

        // Ensure exact positioning after momentum ends
        scrollTimeoutRef.current = setTimeout(() => {
          if (scrollViewRef.current) {
            scrollViewRef.current.scrollTo({
              y: index * ITEM_HEIGHT,
              animated: true,
            });
          }
          setIsScrolling(false);
        }, 50);
      }
    },
    [years.length],
  );

  // Handle year selection by tapping
  const handleYearPress = useCallback(
    (index: number) => {
      if (index === selectedIndex) return; // Avoid unnecessary updates

      setSelectedIndex(index);
      setIsScrolling(true);

      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({
          y: index * ITEM_HEIGHT,
          animated: true,
        });
      }

      // Reset scrolling state after animation
      setTimeout(() => setIsScrolling(false), 300);
    },
    [selectedIndex],
  );

  const handleConfirm = useCallback(() => {
    onYearSelected(years[selectedIndex]);
    onClose();
  }, [onYearSelected, onClose, years, selectedIndex]);

  // Cleanup timeouts
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  const renderYearItem = useCallback(
    (year: number, index: number) => {
      const isSelected = index === selectedIndex;
      const distanceFromCenter = Math.abs(index - selectedIndex);

      return (
        <TouchableOpacity
          key={year.toString()}
          style={styles(theme).item}
          onPress={() => handleYearPress(index)}
          activeOpacity={0.7}>
          <Text
            style={[
              styles(theme).itemText,
              isSelected
                ? styles(theme).selectedText
                : distanceFromCenter <= 1
                  ? styles(theme).nearbyText
                  : styles(theme).farText,
            ]}>
            {year}
          </Text>
        </TouchableOpacity>
      );
    },
    [selectedIndex, theme, handleYearPress],
  );

  return (
    <CustomModal
      visible={visible}
      onClose={onClose}
      variant="bottom"
      title={title}
      titleStyle={{color: theme.colors.text}}
      modalContainerStyle={styles(theme).modalContent}>
      <View style={styles(theme).pickerContainer}>
        {/* Highlight box */}
        <View style={styles(theme).highlightOverlay} pointerEvents="none" />

        <ScrollView
          ref={scrollViewRef}
          snapToInterval={ITEM_HEIGHT}
          decelerationRate="fast"
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}
          onScroll={handleScroll}
          onScrollBeginDrag={handleScrollBeginDrag}
          onMomentumScrollEnd={handleMomentumScrollEnd}
          contentContainerStyle={styles(theme).scrollContent}>
          {years.map(renderYearItem)}
        </ScrollView>
      </View>

      <TouchableOpacity
        style={[styles(theme).confirmButton, {borderColor: theme.colors.activeColor}]}
        onPress={handleConfirm}>
        <Text style={[styles(theme).confirmText, {color: theme.colors.activeColor}]}>Done</Text>
      </TouchableOpacity>
    </CustomModal>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    modalContent: {
      backgroundColor: theme.colors.blackBg,
      padding: 20,
      alignItems: 'center',
      justifyContent: 'center',
      width: Dimensions.get('window').width,
      maxHeight: Dimensions.get('window').height * 0.6,
      height: Dimensions.get('window').height * 0.5,
    },
    pickerContainer: {
      height: ITEM_HEIGHT * 5,
      width: '60%',
      position: 'relative',
      overflow: 'hidden',
    },
    scrollContent: {
      paddingVertical: ITEM_HEIGHT * 2,
    },
    item: {
      height: ITEM_HEIGHT,
      justifyContent: 'center',
      alignItems: 'center',
      width: '100%',
      paddingVertical: 5,
    },
    itemText: {
      fontSize: 20,
      textAlign: 'center',
    },
    selectedText: {
      fontWeight: '700',
      color: theme.colors.activeColor,
      fontSize: 22,
    },
    nearbyText: {
      color: theme.colors.text,
      fontWeight: '500',
    },
    farText: {
      color: theme.colors.bgGrey,
      fontWeight: '400',
    },
    highlightOverlay: {
      position: 'absolute',
      top: ITEM_HEIGHT * 2,
      height: ITEM_HEIGHT,
      left: 0,
      right: 0,
      borderTopWidth: 1,
      borderBottomWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.3)',
      zIndex: 1,
    },
    confirmButton: {
      marginTop: 10,
      paddingVertical: 12,
      paddingHorizontal: 40,
      borderWidth: 1,
      borderRadius: 20,
    },
    confirmText: {
      fontSize: 16,
      fontWeight: '600',
    },
  });

export default ModalYearPicker;
