import React, {useRef, useEffect, useState} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  View,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {styles as createStyles} from './styles';
import {Controller, useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {useUpdateUserProfile, useUserProfile, useCheckDisplayName} from '@/hooks/queries/useUser';
import {useAuthStore} from '@/store/authStore';
import CInput from '@components/CInput';
import CButton from '@components/CButton/index';
import RadioSelect from '@components/RadioSelect/index';
import Typography from '@/components/Typography';
import {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import {debounce, isBoolean} from 'lodash';
import useTranslation from '@/hooks/useTranslation';
import {Icon} from '@/components';
import {toaster} from '@/utils/commonFunctions';
import {useRandomName} from '@/hooks/queries/useUsers';
import {useQueryClient} from '@tanstack/react-query';
import ModalYearPicker from '@/components/ModalYearPicker';
import {createProfileRadioData} from '@/config/staticData';

/**
 * @category Interfaces
 * @typedef {Object} CreateProfileProps
 * @property {Function} completeProfile - User completed their profile.
 * @property {Function} setupLater - User want to setup profile later.
 */
interface CreateProfileProps {
  completeProfile: () => void;
  setupLater: () => void;
}

interface FormData {
  fullName: string;
  email?: string;
  birthYear: string;
  userType: string;
  fitnessLevel: string;
  randomName?: string;
}

/**
 * @component
 * @category Components
 *
 * @description Profile setup after user completed login and authenticated.
 *
 * The Component receives the following props:
 *   - completeProfile - User completed their profile.
 *   - setupLater - User want to setup profile later.
 *
 * @param {{completeProfile: Function, setupLater: Function}} props
 *
 * @return {JSX.Element} Create profile component
 */

const CreateGoRaqtProfile = (props: CreateProfileProps) => {
  const {completeProfile = () => {}, setupLater = () => {}} = props;

  const theme = useThemeStore();
  const styles = createStyles(theme);
  const {user, login, isApiStatus} = useAuthStore();
  console.log('user', user);
  const updateProfileMutation = useUpdateUserProfile();
  const {data: userProfileData, isLoading} = useUserProfile();
  const [displayName, setDisplayName] = useState('');
  const [ageStatus, setAgeStatus] = useState<string>('');
  const {data: nameCheck, isLoading: isCheckingName} = useCheckDisplayName(displayName);
  // const {data: randomNames, isLoading: isLoadingRandomNames, refetch} = useGetRandomNames();
  const randomNameApi = useRandomName();
  const queryClient = useQueryClient();
  const cachedData = queryClient.getQueryData(['randomName']);

  const {t} = useTranslation();
  const fullNameInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const birthYearInputRef = useRef<TextInput>(null);

  const [showYearModal, setShowYearModal] = useState(false);

  const schema = yup.object({
    fullName: yup.string().required(t('createProfile.nameError')),
    email: yup.string().email(t('createProfile.emailFormatError')).optional(),
    birthYear: yup
      .string()
      .required(t('createProfile.birthYearRequired'))
      .matches(/^[0-9]{4}$/, t('createProfile.birthYearformatErr'))
      .test('is-valid-year', t('createProfile.birthYearRangeErr'), value => {
        const numValue = parseInt(value, 10);
        const currentYear = new Date().getFullYear();
        if (numValue > currentYear) {
          return false;
        }
        return !isNaN(numValue) && numValue >= currentYear - 59 && numValue <= currentYear;
      }),
    userType: yup.string().required(t('createProfile.userTypeError')),
    fitnessLevel: yup.string().required(t('createProfile.fitnessLevelError')),
    randomName: yup.string().optional(),
  });

  const {
    control,
    handleSubmit,
    formState: {errors},
    setValue,
    watch,
  } = useForm<FormData>({
    resolver: yupResolver<FormData, any, FormData>(schema),
    defaultValues: {
      fullName: user?.display_name || user?.name || '',
      email: user?.email || '',
      birthYear: user?.birthyear ? String(user.birthyear) : '',
      userType: user?.user_type || '',
      fitnessLevel: user?.fitness_level || '',
      randomName: '',
    },
  });

  // Prefill form with user data if available
  useEffect(() => {
    if (user) {
      handleRandomName();

      setValue('fullName', user?.display_name || user?.name || '');
      setValue('email', user?.email || '');
      if (user.birthyear) {
        setValue('birthYear', String(user.birthyear));
      }
    }

    // If we have profile data from the API, use it to prefill the form
    // if (userProfileData?.data) {
    //   const profileUser = userProfileData.data;

    //   if (profileUser.name) {
    //     setValue('fullName', profileUser.name);
    //   }

    //   if (profileUser.user_type) {
    //     setValue('userType', profileUser.user_type);
    //   }

    //   if (profileUser.fitness_level) {
    //     setValue('fitnessLevel', profileUser.fitness_level);
    //   }

    //   if (profileUser.birthdate) {
    //     const age = calculateAge(profileUser.birthdate);
    //     setValue('age', String(age));
    //   }
    // }
  }, [user, userProfileData, setValue]);

  // Get the current values for the radio buttons
  const userTypeValue = watch('userType');
  const fitnessLevelValue = watch('fitnessLevel');
  const randomNameValue = watch('randomName');

  /**
   * @function onSubmit
   * @memberof CreateGoRaqtProfile
   * @description
   * Handles form submission and create profile process.
   * @param {FormData} data - Form data containing name, user type, fitness level and birth year.
   */

  const onSubmit = (data: FormData) => {
    Keyboard.dismiss();

    const birthdate = new Date();
    birthdate.setFullYear(parseInt(data.birthYear, 10));
    birthdate.setMonth(0);
    birthdate.setDate(1);

    // Prepare data for API
    const updateData = {
      name: data.fullName,
      user_type: data.userType as 'player' | 'coach' | 'both',
      fitness_level: data.fitnessLevel as
        | 'slow_and_steady'
        | 'workout_warrior'
        | 'can_keep_rally'
        | 'play_competitively',
      // birthdate: birthdate.toISOString().split('T')[0],
      birthyear: data.birthYear,
      is_chat_on: true, // Default values
      is_notification_on: true, // Default values
    };

    // Call the update profile API
    if (isApiStatus) {
      updateProfileMutation.mutate(updateData, {
        onSuccess: data => {
          if (data?.data) {
            login(data.data);
          }
          toaster('success', data.message, 'top');
          completeProfile();
        },
        onError: error => {
          toaster('error', error.message, 'top');
        },
      });
    } else {
      completeProfile();
    }
  };

  /**
   * @function handleRandomName
   * @memberof CreateGoRaqtProfile
   * @description
   * Generate random display name, user can choose that name from the list.
   */
  const handleRandomName = () => {
    const {userType, fitnessLevel, birthYear} = control._formValues;
    const data =
      userType || fitnessLevel || birthYear
        ? {
            ...(userType && {user_type: userType}),
            ...(fitnessLevel && {fitness_level: fitnessLevel}),
            ...(birthYear && {birthyear: birthYear}),
          }
        : {};
    if (isApiStatus) {
      randomNameApi.mutate(data as any, {
        onSuccess: response => {
          if (!response?.status) {
            toaster('error', response.message, 'top');
          }
        },
        onError: error => {
          toaster('error', error.message, 'top');
        },
      });
    } else {
      toaster('success', 'Random name generation API called', 'top');
    }
  };

  return (
    <React.Fragment>
      <View style={styles.titleContainer}>
        <Typography variant="subtitle2" style={styles.title}>
          {t('createProfile.createProfileTitle')}
        </Typography>
      </View>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}>
        <BottomSheetScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}>
          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <Controller
                control={control}
                name="fullName"
                render={({field: {onChange, onBlur, value}}) => (
                  <CInput
                    label={t('createProfile.name')}
                    showLabel={true}
                    variant="light"
                    placeholder={t('createProfile.namePlaceholder')}
                    value={value}
                    onChangeText={text => {
                      onChange(text);
                      const debouncedCheck = debounce((name: string) => {
                        setDisplayName(name);
                      }, 1000);
                      debouncedCheck(text);
                    }}
                    onBlur={onBlur}
                    hasError={
                      !!errors.fullName ||
                      (value.length > 0 && isBoolean(nameCheck?.status) && !nameCheck?.status)
                    }
                    error={
                      errors.fullName?.message ||
                      (value.length > 0 && isBoolean(nameCheck?.status) && !nameCheck?.status
                        ? nameCheck?.message
                        : '')
                    }
                    inputStyle={styles.input}
                    containerStyle={{marginBottom: 0}}
                    ref={fullNameInputRef}
                    returnKeyType="next"
                    onSubmitEditing={() => emailInputRef.current?.focus()}
                    blurOnSubmit={false}
                    useBottomSheetInput={true}
                    editable={!isCheckingName}
                    isLoading={isCheckingName}
                  />
                )}
              />
            </View>

            {/* <View style={styles.inputContainer}>
              <Controller
                control={control}
                name="email"
                render={({field: {onChange, onBlur, value}}) => (
                  <CInput
                    label={t('createProfile.email')}
                    showLabel={false}
                    placeholder={t('createProfile.emailPlaceholder')}
                    placeholderTextColor={theme.colors.placeholder}
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    hasError={!!errors.email}
                    error={errors.email?.message}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    inputStyle={[styles.input, {color: theme.colors.placeholder}]}
                    containerStyle={{marginBottom: 0}}
                    ref={emailInputRef}
                    returnKeyType="next"
                    onSubmitEditing={() => ageInputRef.current?.focus()}
                    blurOnSubmit={false}
                    editable={false} // Make email field read-only
                    useBottomSheetInput={true}
                  />
                )}
              />
            </View> */}

            <View style={[styles.inputContainer]}>
              <View style={styles.randomName}>
                <Typography variant="bodyMedium" style={styles.radioTitle}>
                  {t('createProfile.randomNameGenerator')}
                  {randomNameApi?.isPending && (
                    <ActivityIndicator
                      size={14}
                      style={{paddingLeft: 10}}
                      color={theme.colors.white}
                    />
                  )}
                </Typography>
                <TouchableOpacity
                  activeOpacity={0.7}
                  onPress={() => !randomNameApi?.isPending && handleRandomName()}>
                  <Icon name="refresh" color={theme.colors.white} size={18} />
                </TouchableOpacity>
              </View>
              {/* <Controller
                control={control}
                name="randomName"
                render={({field: {onChange, value}}) => (
                  <View style={[styles.radioContainer]}>
                    {randomNames?.data?.map((option, index) => (
                      <RadioSelect
                        containerStyle={styles.fitnessRadio}
                        key={index}
                        label={option}
                        selected={randomNameValue === option}
                        onPress={() => {
                          Keyboard.dismiss();
                          setDisplayName(option);
                          setValue('fullName', option, {
                            shouldValidate: true,
                          });
                          onChange(option); // Call onChange to update the form value
                        }}
                      />
                    ))}
                  </View>
                )}
              /> */}

              <Controller
                control={control}
                name="randomName"
                render={({field: {onChange, value}}) => (
                  <View style={[styles.btnContainer]}>
                    {cachedData?.data?.map((option, index) => (
                      <CButton
                        containerStyle={styles.fitnessBtn}
                        textStyle={styles.fitnessText}
                        key={index}
                        title={option}
                        variant="primary"
                        onPress={() => {
                          Keyboard.dismiss();
                          setDisplayName(option);
                          setValue('fullName', option, {
                            shouldValidate: true,
                          });
                          onChange(option); // Call onChange to update the form value
                        }}
                      />
                    ))}
                  </View>
                )}
              />
              {/* {errors.randomName && (
                <Typography variant="body" style={styles.errorText}>
                  {errors.randomName.message}
                </Typography>
              )} */}
            </View>

            <View style={styles.inputContainer}>
              <Controller
                control={control}
                name="birthYear"
                render={({field: {onChange, onBlur, value}}) => (
                  <View>
                    <TouchableOpacity activeOpacity={1} onPress={() => setShowYearModal(true)}>
                      <View pointerEvents="none">
                        <CInput
                          label={t('createProfile.birthYear')}
                          showLabel={true}
                          variant="light"
                          placeholder={t('createProfile.birthYearPlaceholder')}
                          value={value}
                          onChangeText={text => {
                            onChange(text);
                            if (text.length === 4) {
                              const birthYear = parseInt(text, 10);
                              const currentYear = new Date().getFullYear();
                              if (birthYear > currentYear) {
                                setAgeStatus('');
                                setValue('birthYear', text, {
                                  shouldValidate: true,
                                });
                              } else if (birthYear >= 1920 && birthYear <= currentYear) {
                                const age = currentYear - birthYear;
                                setAgeStatus(age >= 18 ? 'ADULT' : 'JUNIOR');
                                setValue('birthYear', text, {
                                  shouldValidate: true,
                                });
                              } else {
                                setAgeStatus('');
                                setValue('birthYear', text, {
                                  shouldValidate: true,
                                });
                              }
                            } else {
                              setAgeStatus('');
                              setValue('birthYear', text, {
                                shouldValidate: true,
                              });
                            }
                          }}
                          editable={false}
                          onBlur={onBlur}
                          hasError={!!errors.birthYear}
                          error={errors.birthYear?.message}
                          keyboardType="numeric"
                          inputStyle={styles.input}
                          containerStyle={{marginBottom: 0}}
                          ref={birthYearInputRef}
                          returnKeyType="done"
                          blurOnSubmit={false}
                          useBottomSheetInput={true}
                          maxLength={4}
                        />
                        {ageStatus && (
                          <Typography
                            variant="subtitle"
                            style={{
                              fontWeight: '700',
                              fontSize: 16,
                              minWidth: 80,
                              textAlign: 'right',
                              position: 'absolute',
                              right: 15,
                              top: '50%',
                              // bottom: 0,
                              color: theme.colors.primary,
                            }}>
                            {ageStatus}
                          </Typography>
                        )}
                      </View>
                    </TouchableOpacity>
                  </View>
                )}
              />
            </View>

            <View style={styles.inputContainer}>
              <Typography variant="bodyMedium" style={styles.radioTitle}>
                {t('createProfile.describe')}
              </Typography>
              <Controller
                control={control}
                name="userType"
                render={({field: {onChange, value}}) => (
                  <View style={styles.radioContainer}>
                    {createProfileRadioData?.bestDescribeOptions.map(option => (
                      <RadioSelect
                        key={option.value}
                        label={t(option.label)}
                        selected={userTypeValue === option.value}
                        onPress={() => {
                          Keyboard.dismiss();
                          setValue('userType', option.value, {
                            shouldValidate: true,
                          });
                          onChange(option.value); // Call onChange to update the form value
                        }}
                      />
                    ))}
                  </View>
                )}
              />
              {errors.userType && (
                <Typography variant="body" style={styles.errorText}>
                  {errors.userType.message}
                </Typography>
              )}
            </View>

            <View style={styles.inputContainer}>
              <Typography variant="bodyMedium" style={styles.radioTitle}>
                {t('createProfile.describeFitness')}
              </Typography>
              <Controller
                control={control}
                name="fitnessLevel"
                render={({field: {onChange, value}}) => (
                  <View style={styles.radioContainer}>
                    {createProfileRadioData?.describeFitnessOptions.map(option => (
                      <RadioSelect
                        containerStyle={styles.fitnessRadio}
                        key={option.value}
                        label={t(option.label)}
                        selected={fitnessLevelValue === option.value}
                        onPress={() => {
                          Keyboard.dismiss();
                          setValue('fitnessLevel', option.value, {
                            shouldValidate: true,
                          });
                          onChange(option.value); // Call onChange to update the form value
                        }}
                      />
                    ))}
                  </View>
                )}
              />
              {errors.fitnessLevel && (
                <Typography variant="body" style={styles.errorText}>
                  {errors.fitnessLevel.message}
                </Typography>
              )}
            </View>
            <View style={styles.buttonContainer}>
              <View style={styles.flex1}>
                <CButton
                  title={t('createProfile.submitBtn')}
                  variant="primary"
                  onPress={handleSubmit(onSubmit)}
                  textStyle={styles.fitnessText}
                  loading={updateProfileMutation.isPending}
                  isDisabled={updateProfileMutation.isPending}
                />
              </View>
              <View style={styles.flex1}>
                <CButton
                  title={t('createProfile.setupLaterBtn')}
                  variant="primary"
                  onPress={() => {
                    Keyboard.dismiss();
                    setupLater();
                  }}
                  textStyle={styles.fitnessText}
                  isDisabled={updateProfileMutation.isPending}
                />
              </View>
            </View>
          </View>

          <ModalYearPicker
            visible={showYearModal}
            onClose={() => setShowYearModal(false)}
            title="Select Year"
            initialYear={
              watch('birthYear') ? parseInt(watch('birthYear'), 10) : new Date().getFullYear()
            }
            onYearSelected={year => {
              setValue('birthYear', String(year), {
                shouldValidate: true,
              });
              // Update age status when year is selected
              const currentYear = new Date().getFullYear();
              if (year >= 1920 && year <= currentYear) {
                const age = currentYear - year;
                setAgeStatus(age >= 18 ? 'ADULT' : 'JUNIOR');
              } else {
                setAgeStatus('');
              }
              setShowYearModal(false);
            }}
          />
        </BottomSheetScrollView>
      </KeyboardAvoidingView>
    </React.Fragment>
  );
};

export default CreateGoRaqtProfile;
