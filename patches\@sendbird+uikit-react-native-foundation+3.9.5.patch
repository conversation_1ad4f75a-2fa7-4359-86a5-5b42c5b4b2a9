diff --git a/node_modules/@sendbird/uikit-react-native-foundation/src/components/Modal/index.tsx b/node_modules/@sendbird/uikit-react-native-foundation/src/components/Modal/index.tsx
index fc79839..f45f649 100644
--- a/node_modules/@sendbird/uikit-react-native-foundation/src/components/Modal/index.tsx
+++ b/node_modules/@sendbird/uikit-react-native-foundation/src/components/Modal/index.tsx
@@ -13,12 +13,15 @@ import {
   TouchableWithoutFeedback,
   ViewStyle,
   useWindowDimensions,
+  BackHandler
 } from 'react-native';
  
+import Reanimated, { FadeIn, FadeOut } from 'react-native-reanimated';
 import createStyleSheet from '../../styles/createStyleSheet';
 import useHeaderStyle from '../../styles/useHeaderStyle';
 import useUIKitTheme from '../../theme/useUIKitTheme';
  
+const AnimatedPressable = Reanimated.createAnimatedComponent(Pressable);
 type ModalAnimationType = 'slide' | 'slide-no-gesture' | 'fade';
 type Props = {
   type?: ModalAnimationType;
@@ -51,20 +54,90 @@ const Modal = ({
   const { content, backdrop, showTransition, hideTransition } = useModalAnimation(type);
   const panResponder = useModalPanResponder(type, content.translateY, showTransition, onClose);
   const { topInset } = useHeaderStyle();
+  const didMountRef = useRef(false);
  
   const [modalVisible, setModalVisible] = useState(false);
-  const showAction = () => setModalVisible(true);
+  const showAction = () => {
+        if (Platform.OS === 'android') {
+          showTransition(() => setModalVisible(true))
+        } else {
+         setModalVisible(true)
+        }
+  };  
   const hideAction = () => hideTransition(() => setModalVisible(false));
  
   const { width, height } = useWindowDimensions();
-
   useEffect(() => {
     if (visible) showAction();
     else hideAction();
-  }, [visible]);
+  }, [visible,didMountRef]);
  
   useOnDismiss(modalVisible, onDismiss);
  
+    const handleClose = () => {
+        onClose();
+        onDismiss?.();
+      }
+   
+      useEffect(() => {
+        if (!visible) {
+          return;
+        }
+   
+        const hardwareBackPress = () => {
+          handleClose();
+          return true;
+        }
+   
+        const subscription = BackHandler.addEventListener('hardwareBackPress', hardwareBackPress);
+        return () => {
+          subscription.remove();
+        };
+      }, [visible]);
+   
+      if (Platform.OS === 'android') {
+        if (visible) {
+          return (
+            <AnimatedPressable
+              style={[StyleSheet.absoluteFill, {
+                backgroundColor: palette.onBackgroundLight03,
+              }]}
+              onPress={() => {
+                if (!disableBackgroundClose) {
+                  handleClose();
+                }
+              }}
+              entering={FadeIn}
+              exiting={FadeOut}>
+              <KeyboardAvoidingView
+                // NOTE: This is trick for Android.
+                //  When orientation is changed on Android, the offset that to avoid soft-keyboard is not updated normally.
+                key={Platform.OS === 'android' && enableKeyboardAvoid ? `${width}-${height}` : undefined}
+                enabled={enableKeyboardAvoid}
+                style={styles.background}
+                behavior={Platform.select({ ios: 'padding', default: 'height' })}
+                pointerEvents={'box-none'}
+                keyboardVerticalOffset={enableKeyboardAvoid && statusBarTranslucent ? -topInset : 0}
+              >
+                <Animated.View  
+                  style={[
+                    styles.background,
+                    backgroundStyle,
+                    { opacity: content.opacity, transform: [{ translateY: content.translateY }] },
+                  ]}
+                  pointerEvents={'box-none'}
+                  {...panResponder.panHandlers}
+                >
+                  {children}
+                </Animated.View>  
+              </KeyboardAvoidingView>
+            </AnimatedPressable>
+          )
+        }
+       
+        return null
+      }
+ 
   return (
     <RNModal
       statusBarTranslucent={statusBarTranslucent}
