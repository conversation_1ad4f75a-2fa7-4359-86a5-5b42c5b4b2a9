import React, {useState, useEffect, useRef, useMemo, useCallback} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {
  ActivityIndicator,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import Tabs from '../Tabs';
import PlayerCard from '../PlayerCard';
import CButton from '../CButton';
import {styles as createStyles} from './styles';
import {
  BottomSheetFlatList,
  BottomSheetScrollView,
  BottomSheetTextInput,
} from '@gorhom/bottom-sheet';
import Typography from '../Typography';
import {CInput, CustomModal, Icon, NoData, RadioSelect} from '@/components';
import SearchInput from '../SearchInput';
import RatingSlider from '../RatingSlider';
import GroupCard from '../GroupCard';
import {Controller, useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {findPayerTabs} from '@/config/staticData';
import useTranslation from '@/hooks/useTranslation';
import useDebounce from '@/hooks/useDebounce';
import {useInfiniteQuery} from '@tanstack/react-query';
import CLoader from '../CLoader';
import RefreshControl from '../RefreshControl';
import {playerConnectList} from '@/hooks/queries/playerConnect';

type Player = {
  id: string;
  name: string;
  rating: string;
  location: string;
  image: string;
  color?: string;
  isPremium?: boolean;
};

interface FindPlayersProps {
  onConfirm?: (players: Player[]) => void;
  initialSelectedPlayers?: Player[];
  onInvitePress?: () => void;
}

interface Group {
  id: number;
  name: string;
  members: number;
  highlighted: boolean;
  type: string;
  locked: boolean;
}

interface FormData {
  name: string;
  email: string;
  phoneNumber: string;
  rating?: number | null;
}

interface MembersData {
  id: string;
  group_name: string;
  total_members: number;
  highlighted: boolean;
  type: string;
  locked: boolean;
  group_image: string;
}

interface ApiResponse {
  status: boolean;
  data: MembersData[];
  pagination: {
    total: number;
    totalPages: number;
    page: number;
    perPage: number;
  };
}

interface Member {
  id: string;
  profile_pic?: string;
  [key: string]: any;
}

const FindPlayers: React.FC<FindPlayersProps> = ({
  onConfirm,
  initialSelectedPlayers = [],
  onInvitePress = () => {},
}) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const {t} = useTranslation();
  const [activeTab, setActiveTab] = useState(t('findPlayer.findPlayerTabs.schedulePlay'));
  const [selectedPlayers, setSelectedPlayers] = useState<Player[]>(initialSelectedPlayers);
  const [selectedGroups, setSelectedGroups] = useState<Group[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isInviteModalVisible, setIsInviteModalVisible] = useState(false);

  const nameInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const phoneNumberInputRef = useRef<TextInput>(null);
  const ratingNumberInputRef = useRef<TextInput>(null);

  const [search, setSearch] = React.useState<string | null>(null);
  const [searchValue, setSearchValue] = useState('');
  const [appliedFilters, setAppliedFilters] = useState<string[]>([]);
  const [utrRating, setUtrRating] = useState(0);
  const [appliedUtrRating, setAppliedUtrRating] = useState(0);
  const [selectedValues, setSelectedValues] = React.useState<string[]>([]);

  const clearSearch = () => {
    setSearchQuery('');
    setSearch(null); // Clear debounced search
  };

  const options = [
    {label: 'Tennis', value: 'tennis'},
    {label: 'Pickleball', value: 'pickleball'},
    {label: 'Plateform Tennis', value: 'plateform-tennis'},
    {label: 'Padel', value: 'padel'},
  ];
  const options2 = [
    {label: 'Friends', value: 'friends'},
    {label: 'Invited', value: 'invited'},
    {label: 'Sponsored', value: 'sponsored'},
  ];

  // Toggle selection for filters
  const toggleSelection = (value: string) => {
    setSelectedValues(prev =>
      prev.includes(value) ? prev.filter(v => v !== value) : [...prev, value],
    );
  };

  // Initialize with initial players data on mount
  useEffect(() => {
    if (initialSelectedPlayers && initialSelectedPlayers.length > 0) {
      setSelectedPlayers(initialSelectedPlayers);
    }
  }, [initialSelectedPlayers]);

  const handlePlayerSelect = (player: Player) => {
    if (selectedPlayers.some(p => p.id === player.id)) {
      setSelectedPlayers(selectedPlayers.filter(p => p.id !== player.id));
    } else {
      setSelectedPlayers([...selectedPlayers, player]);
    }
  };

  const handleGroupSelect = (group: Group) => {
    if (selectedGroups.some(g => g.id === group.id)) {
      setSelectedGroups(selectedGroups.filter(g => g.id !== group.id));
    } else {
      setSelectedGroups([...selectedGroups, group]);
    }
  };

  const renderItem = ({item}: {item: any}) => {
    if (activeTab === t('findPlayer.findPlayerTabs.group')) {
      return (
        <GroupCard
          name={item.group_name}
          members={item.total_members}
          highlighted={item.highlighted}
          locked={item.locked}
          containerStyle={{
            borderWidth: 1,
            borderColor: theme.colors.divider,
          }}
          onSelect={() => handleGroupSelect(item)}
          isSelected={selectedGroups.some(g => g.id === item.id)}
        />
      );
    } else {
      return (
        <PlayerCard
          key={item.id}
          playerData={item}
          onSelect={() => handlePlayerSelect(item)}
          isSelected={selectedPlayers.some(p => p.id === item.id)}
        />
      );
    }
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm(selectedPlayers);
    }
  };

  const schema = yup.object({
    name: yup.string().required(t('findPlayer.nameRequired')),
    email: yup.string().email(t('findPlayer.emailInvalid')).required(t('findPlayer.emailRequired')),
    phoneNumber: yup.string().required(t('findPlayer.phoneRequired')),
    rating: yup.number().nullable().optional(),
  });

  const {
    control,
    handleSubmit,
    formState: {errors},
    setValue,
    watch,
  } = useForm<FormData>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      name: '',
      email: '',
      phoneNumber: '',
      rating: null,
    },
  });

  const onSubmit = (data: FormData) => {
    Keyboard.dismiss();
    setIsInviteModalVisible(true);
  };

  const debouncedSearch = useDebounce(search, 500);
  const filterSelectedVal = appliedFilters.join(',');

  const {
    data: playersData,
    isLoading,
    refetch,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isRefetching,
  } = useInfiniteQuery<ApiResponse>({
    queryKey: ['players-list', debouncedSearch, filterSelectedVal, appliedUtrRating, activeTab],
    gcTime: 0,
    queryFn: async ({pageParam = 1}) => {
      const response = await playerConnectList(
        pageParam as number,
        debouncedSearch,
        appliedUtrRating,
        activeTab === 'Schedule Play'
          ? 'schedule_play'
          : activeTab === 'Nearby'
            ? 'nearby'
            : activeTab === 'Friends'
              ? 'friends'
              : activeTab === 'Group'
                ? 'groups'
                : '',
        filterSelectedVal,
        undefined,
        undefined,
      );
      return (
        response || {
          data: [],
          pagination: {total: 0, totalPages: 0, page: 1, perPage: 10},
        }
      );
    },
    getNextPageParam: lastPage => {
      console.log('lastPage=====>>>>>', lastPage);
      if (lastPage?.pagination?.page < lastPage?.pagination?.totalPages) {
        return lastPage?.pagination?.page + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });

  const UsersListData = useMemo(() => {
    return (
      playersData?.pages.flatMap(page =>
        page.data.map((user: any) => ({
          ...user,
          image: user.profile_pic,
        })),
      ) || []
    );
  }, [playersData?.pages]);

  const FooterComponent = () => {
    if (!hasNextPage) {
      return null;
    }
    return <ActivityIndicator color={theme.colors.activeColor} size="small" />;
  };

  return (
    <>
      <Typography variant="findTitle" color={theme.colors.text} style={{marginBottom: 10}}>
        {/* {activeTab !== t('findPlayer.findPlayerTabs.invite')
          ? t('findPlayer.title')
          : t('findPlayer.invitePlayers')} */}
        {activeTab === t('findPlayer.findPlayerTabs.schedulePlay')
          ? t('findPlayer.scheduleTitle')
          : activeTab === t('findPlayer.findPlayerTabs.group')
            ? t('findPlayer.findGroup')
            : t('findPlayer.title')}
      </Typography>
      {activeTab !== t('findPlayer.findPlayerTabs.invite') && (
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Icon name="search-1" size={25} color={theme.colors.primary} />
            <BottomSheetTextInput
              style={styles.searchInput}
              placeholder={t('findPlayer.searchPlaceHolder')}
              placeholderTextColor={theme.colors.primary}
              onChangeText={setSearch}
              value={search}
            />
            {search && (
              <TouchableOpacity onPress={clearSearch}>
                <Icon name="close" size={20} color={theme.colors.red} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}

      <Tabs
        tabs={findPayerTabs.map(item => t(item.title))}
        activeTab={activeTab}
        onTabPress={tab => {
          setActiveTab(tab);
          setSelectedPlayers([]);
          setSelectedGroups([]);
        }}
        listContainerStyle={{
          marginTop: 20,
          marginBottom: 8,
        }}
      />
      {activeTab !== t('findPlayer.findPlayerTabs.invite') && (
        <View style={styles.titleContainer}>
          <Typography variant="parkTitle" color={theme.colors.text}>
            {activeTab}
          </Typography>
          <TouchableOpacity activeOpacity={0.7} onPress={() => setIsModalVisible(true)}>
            <Icon name="filter" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      )}
      {activeTab === t('findPlayer.findPlayerTabs.invite') ? (
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}>
          <BottomSheetScrollView
            contentContainerStyle={styles.scrollContainer}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}>
            <View style={styles.form}>
              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="name"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('findPlayer.name')}
                      variant="dark"
                      showLabel={false}
                      placeholder={t('findPlayer.namePlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.name}
                      error={errors.name?.message}
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={nameInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => emailInputRef.current?.focus()}
                      blurOnSubmit={false}
                      useBottomSheetInput={true}
                    />
                  )}
                />
              </View>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="email"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('findPlayer.email')}
                      showLabel={false}
                      variant="dark"
                      placeholder={t('findPlayer.emailPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.email}
                      error={errors.email?.message}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      inputStyle={[styles.input]}
                      containerStyle={{marginBottom: 0}}
                      ref={emailInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => phoneNumberInputRef.current?.focus()}
                      blurOnSubmit={false}
                      useBottomSheetInput={true}
                    />
                  )}
                />
              </View>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="phoneNumber"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('findPlayer.phoneNumber')}
                      showLabel={false}
                      variant="dark"
                      placeholder={t('findPlayer.phonePlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.phoneNumber}
                      error={errors.phoneNumber?.message}
                      keyboardType="numeric"
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={phoneNumberInputRef}
                      returnKeyType="done"
                      blurOnSubmit={false}
                      useBottomSheet
                      ManaInput={true}
                      onSubmitEditing={() => ratingNumberInputRef.current?.focus()}
                      maxLength={10}
                    />
                  )}
                />
              </View>
              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="rating"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('findPlayer.rating')}
                      showLabel={false}
                      variant="dark"
                      placeholder={t('findPlayer.ratingPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value?.toString() || ''}
                      onChangeText={text => onChange(text ? Number(text) : null)}
                      onBlur={onBlur}
                      hasError={!!errors.rating}
                      error={errors.rating?.message}
                      keyboardType="numeric"
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={ratingNumberInputRef}
                      returnKeyType="done"
                      blurOnSubmit={false}
                      useBottomSheetInput={true}
                      onSubmitEditing={() => handleSubmit(onSubmit)()}
                    />
                  )}
                />
              </View>
              <CButton
                title={t('findPlayer.findPlayerTabs.invite')}
                variant="primary"
                onPress={handleSubmit(onSubmit)}
                containerStyle={styles.inviteBtn}
                textStyle={styles.inviteBtnText}
              />
            </View>
          </BottomSheetScrollView>
        </KeyboardAvoidingView>
      ) : isLoading ? (
        <CLoader />
      ) : (
        <BottomSheetFlatList
          data={UsersListData}
          renderItem={renderItem}
          keyExtractor={item => item.id.toString()} // Ensure unique keys
          showsVerticalScrollIndicator={false}
          bounces={false}
          contentContainerStyle={styles.scrollView}
          scrollEnabled={true}
          nestedScrollEnabled={true}
          onEndReachedThreshold={0.5} // Trigger earlier for smoother pagination
          keyboardShouldPersistTaps="handled"
          style={styles.listContainer}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          removeClippedSubviews={Platform.OS === 'android'}
          updateCellsBatchingPeriod={50}
          windowSize={21}
          onEndReached={() => {
            if (hasNextPage && !isFetchingNextPage && !isLoading) {
              fetchNextPage();
            }
          }}
          ListEmptyComponent={!isLoading ? <NoData /> : null}
          refreshControl={<RefreshControl refreshing={isRefetching} onRefresh={() => refetch()} />}
          ListFooterComponent={FooterComponent}
        />
      )}

      {activeTab === t('findPlayer.findPlayerTabs.invite') ? null : (
        <CButton
          containerStyle={{
            marginBottom: 20,
          }}
          title={`${t('findPlayer.accept')} ${
            selectedPlayers.length > 0 ? `(${selectedPlayers.length})` : ''
          }`}
          onPress={handleConfirm}
          isDisabled={
            activeTab === t('findPlayer.findPlayerTabs.group')
              ? selectedGroups.length === 0
              : selectedPlayers.length === 0
          }
        />
      )}

      <CustomModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        variant="bottom"
        showCloseButton={true}
        title="Filter results">
        <View>
          <Typography variant="subtitle" style={{color: theme.colors.offWhite}}>
            {t('addMembersScreen.show')}
          </Typography>
          {options.map(option => (
            <RadioSelect
              key={option.value}
              label={option.label}
              selected={selectedValues.includes(option.value)}
              onPress={() => toggleSelection(option.value)}
            />
          ))}

          {/* Rating Slider */}
          <RatingSlider min={0} max={20} value={utrRating} onChange={setUtrRating} />

          {options2.map(option => (
            <RadioSelect
              key={option.value}
              label={option.label}
              selected={selectedValues.includes(option.value)}
              onPress={() => toggleSelection(option.value)}
            />
          ))}

          <SearchInput
            variant="light"
            placeholder={t('addMembersScreen.searchEvent')}
            containerStyle={styles.filterInput}
            inputStyle={{color: theme.colors.primary}}
            value={searchValue}
            placeholderTextColor={theme.colors.primary}
            onChangeText={setSearchValue}
            iconColor={theme.colors.primary}
            onClear={() => setSearchValue('')}
          />

          <View style={styles.buttonContainer}>
            <CButton
              title={t('addMembersScreen.results')}
              onPress={() => {
                setAppliedFilters(selectedValues);
                setAppliedUtrRating(utrRating);
                setSearch(searchValue);
                setIsModalVisible(false);
                refetch();
              }}
              variant="primary"
              containerStyle={{
                height: 70,
                width: '49%',
              }}
            />
            <CButton
              title={t('common.clearFilters')}
              onPress={() => {
                setAppliedFilters([]);
                setSelectedValues([]);
                setAppliedUtrRating(0);
                setUtrRating(0);
                setSearchValue('');
                setSearch(null);
                if (
                  appliedFilters.length > 0 ||
                  selectedValues.length > 0 ||
                  utrRating > 0 ||
                  appliedUtrRating > 0
                ) {
                  refetch();
                }
                setIsModalVisible(false);
              }}
              variant="outline"
              containerStyle={{
                width: '49%',
                backgroundColor: theme.colors.darkGray,
                borderWidth: 0,
              }}
            />
          </View>
        </View>
      </CustomModal>

      <CustomModal
        visible={isInviteModalVisible}
        onClose={() => setIsInviteModalVisible(false)}
        modalContainerStyle={{
          justifyContent: 'center',
          alignItems: 'center',
          minWidth: '85%',
          paddingHorizontal: 40,
          paddingTop: 40,
        }}>
        <View style={{alignItems: 'center', gap: 25, width: '100%'}}>
          <Icon name="arrow" size={150} color={theme.colors.activeColor} />
          <Typography variant="subtitle" style={{textAlign: 'center', color: theme.colors.text}}>
            {t('common.inviteSent')}
          </Typography>
          <CButton
            title={t('common.inviteMore')}
            onPress={() => setIsInviteModalVisible(false)}
            variant="pill"
            containerStyle={{width: '100%', backgroundColor: theme.colors.activeColor}}
            textStyle={{color: theme.colors.black}}
          />
          <CButton
            title={t('common.close')}
            onPress={() => setIsInviteModalVisible(false)}
            variant="outline"
            containerStyle={{
              width: '100%',
              borderWidth: 0,
              borderColor: theme.colors.text,
            }}
          />
        </View>
      </CustomModal>
    </>
  );
};

export default FindPlayers;
