import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    groupCard: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: 12,
      padding: 10,
      flex: 1,
    },
    groupIconContainer: {
      width: 60,
      height: 60,
      borderRadius: 60,
      backgroundColor: theme.colors.white,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    groupIcon: {
      height: '100%',
      width: '100%',
      borderRadius: 60,
      resizeMode: 'cover',
    },
    groupTextContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
    groupName: {
      color: theme.colors.white,
      fontSize: 15,
      fontWeight: '600',
    },

    groupMembers: {
      color: theme.colors.gray,
      fontSize: 12,
      marginTop: 2,
    },
    groupMembersHighlighted: {
      color: theme.colors.primary,
      fontSize: 12,
      marginTop: 2,
    },
    inviteBanner: {
      backgroundColor: theme.colors.primary,
      paddingVertical: 12,
      alignItems: 'center',
      justifyContent: 'center',
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
      position: 'absolute',
      left: 0,
      right: 0,
      bottom: 0,
    },
    inviteBannerText: {
      color: theme.colors.white,
      fontWeight: 'bold',
      fontSize: 15,
    },
    groupNameContainer: {
      flex: 1,
    },
    lockContainer: {
      backgroundColor: theme.colors.inputLabel,
      padding: 6,
      borderRadius: 40,
    },
    locationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 2,
      marginTop: 5,
      marginLeft: -3,
    },
    buttonContainer: {
      gap: 5,
    },
    countView: {
      backgroundColor: theme.colors.activeColor,
      borderRadius: 100,
      height: 25,
      width: 25,
      justifyContent: 'center',
      alignItems: 'center',
    },
    countText: {
      color: '#000',
      fontSize: 12,
      fontWeight: 'bold',
    },
  });
