import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {
  updateUserProfile,
  getUserProfile,
  UpdateUserRequest,
  getUserDetails,
} from '@/services/userApi';
import {useAuthStore} from '@/store/authStore';
import api, {handleApiError} from '@/services/api';
import {toaster} from '@/utils/commonFunctions';

// Query keys
export const userKeys = {
  all: ['user'] as const,
  profile: () => [...userKeys.all, 'profile'] as const,
};

/**
 * Hook for getting the user profile
 */
export const useUserProfile = () => {
  const {user} = useAuthStore();

  return useQuery({
    queryKey: userKeys.profile(),
    queryFn: getUserProfile,
    enabled: !!user, // Only run if there's a user in the store
  });
};

/**
 * Hook for updating the user profile
 */
export const useUpdateUserProfile = () => {
  const queryClient = useQueryClient();
  const {login} = useAuthStore();

  return useMutation({
    mutationFn: (userData: UpdateUserRequest) => {
      return updateUserProfile(userData);
    },
    onSuccess: data => {
      console.log('Profile update success:', data);
      toaster('success', data.message, 'top');
      // Update the user in the auth store if user data is available
      if (data.data?.user) {
        login({
          name: data.data.user.name,
          email: data.data.user.email,
        });
      }

      // Invalidate the user profile query to refetch the latest data
      queryClient.invalidateQueries({queryKey: userKeys.profile()});
    },
    onError: error => {
      toaster('error', error.message, 'top');
    },
  });
};

export const useCheckDisplayName = (displayName: string) => {
  return useQuery({
    queryKey: ['checkDisplayName', displayName],
    queryFn: async () => {
      try {
        const response = await api.get(`/check-display-name?display_name=${displayName}`);
        return response.data;
      } catch (error) {
        throw new Error(handleApiError(error));
      }
    },
    enabled: displayName.length > 0, // Only run query if displayName is not empty
  });
};

// export const useGetRandomNames = () => {
//   return useQuery({
//     queryKey: ['randomNames'],
//     queryFn: async () => {
//       try {
//         const response = await api.get('/get-random-names');
//         return response.data;
//       } catch (error) {
//         throw new Error(handleApiError(error));
//       }
//     },
//     refetchOnMount: true,
//   });
// };

export const useUserDetails = () => {
  const {login} = useAuthStore();

  return useMutation({
    mutationFn: async () => {
      return getUserDetails();
    },
    onSuccess: data => {
      login(data?.data);
    },
    onError: (error: Error) => {
      toaster('error', error.message, 'top');
    },
  });
};
