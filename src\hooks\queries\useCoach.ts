import {useMutation} from '@tanstack/react-query';
import {updateCoachProfile} from '@/services/coachApi';
import {useAuthStore} from '@/store/authStore';
import {toaster} from '@/utils/commonFunctions';

export const useUpdateCoachProfile = () => {
  const {login} = useAuthStore();

  return useMutation({
    mutationFn: (userData: updateCoachProfile) => {
      return updateCoachProfile(userData);
    },
    onSuccess: data => {
      console.log('Profile update success:', data);
      toaster('success', data.message, 'top');
      //   Update the user in the auth store if user data is available
      if (data.data?.user) {
        login({
          name: data.data.user.name,
          email: data.data.user.email,
        });
      }
    },
    onError: error => {
      toaster('error', error.message, 'top');
    },
  });
};
