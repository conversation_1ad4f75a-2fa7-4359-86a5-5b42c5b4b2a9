import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    title: {
      color: theme.colors.white,
      fontSize: 18,
      fontWeight: '400',
      marginBottom: 6,
    },
    dropdownTrigger: {
      width: '100%',
      paddingHorizontal: 16,
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: theme.colors.placeholder,
      borderRadius: 8,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      height: 57,
    },
    dropdownText: {
      color: theme.colors.activeColor,
      fontSize: 16,
    },
    chevron: {
      transform: [{rotate: '0deg'}],
    },
    chevronRotated: {
      transform: [{rotate: '180deg'}],
    },
    chevronText: {
      color: '#9CA3AF',
      fontSize: 12,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modalContent: {
      backgroundColor: theme.colors.background,
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.white,
    },
    closeButton: {
      padding: 4,
    },
    closeIcon: {
      color: theme.colors.white,
      fontSize: 14,
      textAlignVertical: 'center',
      marginBottom: 2,
    },
    scrollView: {
      flex: 1,
      marginTop: 10,
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: 16,
      gap: 12,
      flex: 1,
    },
    tag: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: theme.colors.primary,
      borderRadius: 20,
    },
    tagText: {
      color: theme.colors.white,
      fontSize: 14,
      fontWeight: '500',
    },

    customInputContainer: {
      gap: 12,
      marginTop: 10,
    },
    inputWithIconContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.background,
      borderWidth: 1,
      borderColor: theme.colors.placeholder,
      borderRadius: 8,
      paddingRight: 8,
    },
    customInput: {
      flex: 1,
      paddingHorizontal: 12,
      color: theme.colors.white,
      fontSize: 16,
      height: 57,
    },
    addIconButton: {
      padding: 8,
    },

    selectedSection: {
      marginTop: 24,
    },
    selectedTitle: {
      fontSize: 18,
      fontWeight: '400',
      color: theme.colors.white,
      marginBottom: 12,
    },
    selectedTagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    selectedTag: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: theme.colors.orange,
      borderRadius: 20,
      gap: 8,
    },
    outerSelectedTag: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 5,
      backgroundColor: theme.colors.primary,
      borderRadius: 20,
      gap: 5,
    },
    selectedTagText: {
      color: theme.colors.white,
      fontSize: 14,
      fontWeight: '500',
    },
    removeButton: {
      padding: 2,
    },
  });
