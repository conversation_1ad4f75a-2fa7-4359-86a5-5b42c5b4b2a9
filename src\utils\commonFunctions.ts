import api, {clearTokenStorage} from '@/services/api';
import {Platform} from 'react-native';
import {QueryClient} from '@tanstack/react-query';
import Toast, {ToastPosition} from 'react-native-toast-message';
import {useAuthStore, useConfigStore} from '@/store';

export const IOS = Platform.OS === 'ios';

export function remainingDays(unixTimestamp: any): string {
  const now: any = new Date();
  const time: any = new Date(unixTimestamp * 1000);

  const diffMs = now - time;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHrs = Math.floor(diffMin / 60);
  const diffDays = Math.floor(diffHrs / 24);

  if (diffSec < 60) return 'just now';
  if (diffMin < 60) return `${diffMin}m`;
  if (diffHrs < 24) return `${diffHrs}h`;
  if (diffDays === 1) return 'Yesterday';

  const sameYear = now.getFullYear() === time.getFullYear();

  return time.toLocaleDateString('en-IN', {
    day: '2-digit',
    month: 'short',
    ...(sameYear ? {} : {year: 'numeric'}),
  });
}

export const getItemList = async (type: string) => {
  try {
    const response = await api.get(`/community-cruds/items?type=${type}`);
    if (response.status) {
      const {data} = response.data;
      return {
        data,
      };
    }
  } catch (error) {
    console.error('Error fetching data:', error);
    return {
      data: [],
    };
  }
};

export const getCommunityData = async ({pageParam = 1, type = '', item_type = ''}) => {
  const params = {
    type: type,
    item_type: item_type,
  };
  try {
    const response = await api.post(`/community-cruds/list?PageNo=${pageParam}`, params);
    if (response.status) {
      const {data, pagination} = response.data;
      return {
        data,
        hasMore: pagination.isMore,
        nextPage: pagination.currentPage + 1,
      };
    }
  } catch (error) {
    console.error('Error fetching data:', error);
    return {
      data: [],
      hasMore: false,
      nextPage: undefined,
    };
  }
};

export const updateCommunityLike = async (data: {
  id: string;
  is_liked: boolean;
  comment?: string;
}) => {
  try {
    const response = await api.post('/community-cruds/like-comment', {
      community_crud_id: data.id,
      is_liked: data.is_liked,
      comment: data.comment || '',
    });
    return response.data;
  } catch (error) {
    console.error('Error updating like:', error);
    throw error;
  }
};

interface LikeableItem {
  id: string;
  is_liked: boolean;
  likes_count: number;
}

export const setupCommunityMutation = <T extends LikeableItem>(
  queryClient: QueryClient,
  queryKey: string[],
  updateStateCallback?: (id: string, isLiked: boolean) => void,
) => {
  return {
    mutationFn: async (data: {id: string; is_liked: boolean; comment?: string}) => {
      return updateCommunityLike(data);
    },
    onMutate: async (newData: {id: string; is_liked: boolean}) => {
      await queryClient.cancelQueries({queryKey});
      const previousData = queryClient.getQueryData(queryKey);

      // Update the data in the query cache
      queryClient.setQueryData(queryKey, (old: any) => {
        if (!old || !old.data) return old;

        const updatedData = {
          ...old,
          data: old.data.map((item: T) =>
            item.id === newData.id
              ? {
                  ...item,
                  is_liked: newData.is_liked,
                  likes_count: newData.is_liked
                    ? (item.likes_count || 0) + 1
                    : (item.likes_count || 0) - 1,
                }
              : item,
          ),
        };
        return updatedData;
      });

      // Call the state update callback if provided
      if (updateStateCallback) {
        updateStateCallback(newData.id, newData.is_liked);
      }

      return {previousData};
    },
    onError: (err: Error, newData: {id: string; is_liked: boolean}, context: any) => {
      if (context?.previousData) {
        queryClient.setQueryData(queryKey, context.previousData);
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({queryKey});
    },
  };
};

export const fetchQRGenerator = async () => {
  try {
    const response = await api.get('/user/get-qr-token');
    return response.data.data;
  } catch (error) {
    toaster('error', 'Failed to generate QR code');
    console.error(error);
    return null;
  }
};

interface PasswordValidation {
  isValid: boolean;
  message: string;
  details: {
    length: boolean;
    mix: boolean;
    upperLower: boolean;
  };
}

export const validatePassword = (password: string): PasswordValidation => {
  const length = password.length >= 8;
  const mix = /[A-Za-z]/.test(password) && /\d/.test(password) && /[~!@#$%^*?_\-+=]/.test(password);
  const upperLower = /[A-Z]/.test(password) && /[a-z]/.test(password);
  const isValid = length && mix && upperLower;

  const message = isValid
    ? 'Password meets all requirements'
    : 'Password must have at least 8 characters, including uppercase, lowercase, a number, and a symbol';

  return {
    isValid,
    message,
    details: {
      length,
      mix,
      upperLower,
    },
  };
};

export const getMyGroups = async (type = '', pageNo: number = 1, search = '', filter = '') => {
  try {
    const params = {
      ...(search ? {search} : {}),
      ...(filter ? {tags: filter} : {}),
      page: pageNo,
    };
    const apiUrl =
      type === 'favorite'
        ? `/group-chat/my-groups?favorite_groups=1`
        : `/group-chat/my-groups?group_type=${type}`;
    const response = await api.get(apiUrl, {
      params,
    });
    if (response.status) {
      return response?.data;
    }
  } catch (error) {
    console.error('Error fetching data:', error);
    return null;
  }
};

export const getUsersList = async (
  pageNo: number = 1,
  search?: any,
  primary_sport?: any,
  rating?: any,
  lat?: number,
  lng?: number,
  limit: number = 100,
) => {
  try {
    const params = {
      ...(search ? {search} : {}),
      ...(primary_sport ? {primary_sport} : {}),
      ...(rating ? {rating} : {}),
      ...(lat ? {lat: lat} : {}),
      ...(lng ? {long: lng} : {}),
      ...(limit ? {limit: limit} : {}),
      page: pageNo,
    };
    const response = await api.get(`/users-list`, {
      params: params,
    });
    if (response.status) {
      return response?.data;
    }
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};

export const getPublicGroups = async (
  search?: string,
  pageNo: number = 1,
  filter?: string,
  lat?: number,
  long?: number,
) => {
  try {
    const params = {
      ...(search ? {search} : {}),
      ...(filter ? {tags: filter} : {}),
      page: pageNo,
    };
    const response = await api.get(`/group-chat/public-groups?lat=${lat}&long=${long}`, {params});
    if (response.status) {
      return response?.data;
    }
  } catch (error) {
    console.error('Error fetching data:', error);
    return null;
  }
};
export const getGroupDetails = async (id: string) => {
  try {
    const response = await api.get(`/group-chat/details/${id}`);
    if (response.status) {
      return response?.data?.data;
    }
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};

export const toaster = (type = '', text1 = '', position = 'top', text2 = '') => {
  Toast.show({
    type: type,
    text1: text1,
    position: position as ToastPosition,
    text2: text2,
  });
};

/**
 * Clears all user-related data from stores and logs out the user.
 */
export const clearAllData = () => {
  useConfigStore.getState().resetBooking();
  useConfigStore.getState().resetAll();
  useAuthStore.getState().logout();
  clearTokenStorage();
};
