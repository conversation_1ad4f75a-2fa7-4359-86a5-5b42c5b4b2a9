import React, {useState} from 'react';
import {View, Text, StyleSheet, Button, TextInput, ActivityIndicator} from 'react-native';
import {useLogin} from '@/hooks/queries/useAuth';
import {useAuthStore} from '@/store';
import {toaster} from '@/utils/commonFunctions';

const ReactQueryLoginTest = () => {
  const {isApiStatus} = useAuthStore();

  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('$miT2345');
  const [response, setResponse] = useState<any>(null);

  // Use the login mutation
  const loginMutation = useLogin();

  const handleLogin = () => {
    if (isApiStatus) {
      loginMutation.mutate(
        {
          email,
          password,
        },
        {
          onSuccess: data => {
            setResponse(data);
            toaster('success', 'Login successful', 'top');
          },
          onError: error => {
            setResponse({error: error.message});
            toaster('error', error.message, 'top');
          },
        },
      );
    } else {
      toaster('success', 'Login successful', 'top');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>React Query Login Test</Text>

      <TextInput
        style={styles.input}
        value={email}
        onChangeText={setEmail}
        placeholder="Email"
        keyboardType="email-address"
        autoCapitalize="none"
      />

      <TextInput
        style={styles.input}
        value={password}
        onChangeText={setPassword}
        placeholder="Password"
        secureTextEntry
      />

      <Button
        title={loginMutation.isPending ? 'Loading...' : 'Test Login'}
        onPress={handleLogin}
        disabled={loginMutation.isPending}
      />

      {loginMutation.isPending && (
        <ActivityIndicator style={styles.loader} size="large" color="#0000ff" />
      )}

      {response && (
        <View style={styles.responseContainer}>
          <Text style={styles.responseTitle}>API Response:</Text>
          <Text style={styles.responseText}>{JSON.stringify(response, null, 2)}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  loader: {
    marginTop: 20,
  },
  responseContainer: {
    marginTop: 20,
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  responseTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  responseText: {
    fontFamily: 'monospace',
  },
});

export default ReactQueryLoginTest;
