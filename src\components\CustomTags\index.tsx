import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Dimensions,
  SafeAreaView,
  Platform,
} from 'react-native';
import {CustomModal, Icon} from '..';
import {useThemeStore} from '@/store';
import {createStyles} from './styles';
// import {KeyboardAvoidingView} from 'react-native-keyboard-controller';
import {predefinedTags} from '@/config/staticData';

interface TagsComponentProps {
  onTagsChange?: (tags: string[]) => void;
  initialTags?: string[];
}

const TagsComponent: React.FC<TagsComponentProps> = ({onTagsChange, initialTags = []}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [selectedTags, setSelectedTags] = useState<string[]>(initialTags);
  const [customTag, setCustomTag] = useState<string>('');
  const customInputRef = useRef<TextInput>(null);

  const theme = useThemeStore();

  const styles = createStyles(theme);

  useEffect(() => {
    if (isOpen && customInputRef.current) {
      customInputRef.current.focus();
    }
  }, [isOpen]);

  const allTags: string[] = [
    ...predefinedTags,
    ...selectedTags.filter(tag => !predefinedTags.includes(tag)),
  ];

  const toggleTag = (tag: string): void => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag];

    setSelectedTags(newTags);
    onTagsChange?.(newTags);
  };

  const removeTag = (tagToRemove: string): void => {
    const newTags = selectedTags.filter(tag => tag !== tagToRemove);
    setSelectedTags(newTags);
    onTagsChange?.(newTags);
  };

  const addCustomTag = (): void => {
    const trimmedTag = customTag.trim();
    if (trimmedTag && !allTags.includes(trimmedTag)) {
      const newTags = [...selectedTags, trimmedTag];
      setSelectedTags(newTags);
      onTagsChange?.(newTags);
      setCustomTag('');
    }
  };

  const closeModal = (): void => {
    setIsOpen(false);
    setCustomTag('');
  };

  const CloseIcon = (style: any) => <Text style={[styles.closeIcon, style]}>✕</Text>;
  const {height} = Dimensions.get('window');

  return (
    <SafeAreaView>
      <Text style={styles.title}>Tags</Text>

      {/* Dropdown Trigger */}
      <TouchableOpacity
        style={styles.dropdownTrigger}
        onPress={() => setIsOpen(true)}
        activeOpacity={0.8}>
        <Text style={styles.dropdownText}>Tags</Text>
      </TouchableOpacity>

      {/* Modal for Dropdown */}
      <CustomModal
        variant="bottom"
        visible={isOpen}
        animationType="slide"
        onClose={() => closeModal()}
        title="Select Tags"
        showCloseButtonRight>
        {/* <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : undefined}> */}
        <View
          style={[
            styles.modalContent,
            {
              height: Platform.OS === 'android' ? 450 : height * 0.85,
            },
          ]}>
          <View style={styles.customInputContainer}>
            <View style={styles.inputWithIconContainer}>
              <TextInput
                ref={customInputRef}
                style={styles.customInput}
                value={customTag}
                onChangeText={setCustomTag}
                placeholder="Enter custom tag..."
                placeholderTextColor={theme.colors.placeholder}
                onSubmitEditing={addCustomTag}
                autoFocus={true}
              />
              <TouchableOpacity
                onPress={addCustomTag}
                style={styles.addIconButton}
                activeOpacity={0.8}>
                <Icon name={'create-group'} size={25} color={theme.colors.white} />
              </TouchableOpacity>
            </View>
          </View>
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            <TouchableOpacity activeOpacity={1}>
              <View style={styles.tagsContainer}>
                {allTags.map(tag => (
                  <TouchableOpacity
                    key={tag}
                    onPress={() => toggleTag(tag)}
                    style={[styles.tag, selectedTags.includes(tag) && styles.selectedTag]}
                    activeOpacity={0.8}>
                    <Text
                      style={[
                        styles.tagText,
                        selectedTags.includes(tag) && styles.selectedTagText,
                      ]}>
                      {tag}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </TouchableOpacity>
          </ScrollView>
        </View>
        {/* </KeyboardAvoidingView> */}
      </CustomModal>

      {/* Selected Tags Display */}
      {selectedTags.length > 0 && (
        <View style={styles.selectedSection}>
          <View style={styles.selectedTagsContainer}>
            {selectedTags.map(tag => (
              <View key={tag} style={styles.outerSelectedTag}>
                <Text style={styles.selectedTagText}>{tag}</Text>
                <TouchableOpacity
                  onPress={() => removeTag(tag)}
                  style={styles.removeButton}
                  activeOpacity={0.8}>
                  <CloseIcon />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

export default TagsComponent;
