// import PushNotification from ;
import {Platform} from 'react-native';
import PushNotification from 'react-native-push-notification';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import {navigationRef} from '@/navigation';
// import { getBatchCount } from '@app/utils/CommonFunction';

// const PushNotification = require('react-native-push-notification');

export const showNotification = (remoteMessage: any, isForeground = false) => {
  console.log('Show Notification Called', remoteMessage);
  // setTimeout(() => {
  //   getBatchCount()
  // }, 6000);
  // store.dispatch(NotificationAction.setNotificationType(remoteMessage));
  const {data, notification} = remoteMessage;
  // sendLocalNotification(data.title, data.message);
  if (!Object.prototype.hasOwnProperty.call(remoteMessage, 'notification')) {
    // For Handling Comet Chat Notifications
    console.log('No Notification key present');
    const {title, alert, message} = data;
    const notificationTitle = title;
    console.log('title>>>', title);
    console.log('alert>>>', alert);
    console.log('message>>>', message ? JSON.stringify(message) : '');
    if (message) {
      sendLocalNotification(notificationTitle, alert, message);
    }
  } else {
    const {source} = data;
    console.log('Notification key present');
    // if (isForeground) {
    //Receiving Notification from our server in foreground
    const {body, title} = notification;
    console.log('title, body, data remoteMessage ===>', remoteMessage);
    sendLocalNotification(title, body, data);

    // }
  }
};

const sendLocalNotification = (title: any, body: any, data: any) => {
  if (Platform.OS === 'android') {
    try {
      // Ensure the channel is created before sending notifications
      PushNotification.createChannel(
        {
          channelId: '1', // (required)
          channelName: 'My channel', // (required)
          channelDescription: 'A channel to categorize your notifications', // (optional)
          // sound: 'default', // (optional) default: 'default'
          // importance: PushNotification.Importance.HIGH, // (optional) default: Importance.HIGH
          soundName: 'default',
          vibrate: true, // (optional) default: true
          playSound: true,
        },
        created => console.log(`createChannel returned '${created}'`, title, body), // (optional) callback
      );

      PushNotification.localNotification({
        channelId: '1', // (required)
        title: title,
        message: body,
        userInfo: data,
      });
    } catch (e) {
      console.log('e ===>', e);
    }
  } else {
    const details = {
      alertTitle: title,
      alertBody: body,
      userInfo: data,
    };

    console.log('PushNotificationIOS ===', PushNotificationIOS);
    PushNotificationIOS.presentLocalNotification(details);
  }
};

/**
 * Handles navigation when a notification is clicked.
 * @param notification - The notification object containing data for navigation.
 */
export const handleNotificationNavigation = (notification: any) => {
  console.log('notification=====>>>>>', notification);
  if (!notification || !notification.data) return;
  const {action_type, channel_url, group_id} = notification.data;

  switch (action_type) {
    case 'chat_detail':
      navigationRef.current?.navigate('SendBird', {
        channelUrl: channel_url,
        group_id,
      });
      break;
    case 'group_join_request':
      navigationRef.current?.navigate('RequestList', {groupId: group_id});
      break;
    case 'accepted_group_join_request':
      navigationRef.current?.navigate('SendBird', {
        channelUrl: channel_url,
        group_id,
      });
      break;
    case 'rejected_group_join_request':
      navigationRef.current?.navigate('JoinGroupDetails', {id: group_id, from: 'my-groups'});
      break;
    case 'OPEN_NOTIFICATIONS':
      navigationRef.current?.navigate('NotificationsList');
      break;
    default:
      navigationRef.current?.navigate('NotificationsList');
      break;
  }
};
