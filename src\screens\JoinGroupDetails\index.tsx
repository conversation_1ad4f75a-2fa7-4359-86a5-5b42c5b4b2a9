import React, {useEffect} from 'react';
import {View, TouchableOpacity, FlatList, ScrollView, ActivityIndicator} from 'react-native';
import {createStyles} from './styles';
import {useThemeStore} from '@/store';
import Typography from '@/components/Typography';
import PlayerCard from '@/components/PlayerCard';
import {Images} from '@/config';
import {CImage, Header, Icon, SafeAreaView} from '@/components';
import {
  DrawerActions,
  useNavigation,
  useRoute,
  RouteProp,
  NavigationProp,
} from '@react-navigation/native';
import useTranslation from '@/hooks/useTranslation';
import {useQuery, useQueryClient} from '@tanstack/react-query';
import {getGroupDetails, toaster} from '@/utils/commonFunctions';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import CLoader from '@/components/CLoader';
import {useJoinOrLeaveGroup, useRequestToJoinGroup} from '@/hooks/queries/groups';

interface GroupMembers {
  id: string;
  name: string;
  profile_pic: string;
  email: string;
  display_name: string;
}

interface GroupsDetails {
  id: string;
  group_name: string;
  total_members: number;
  highlighted: boolean;
  type: string;
  locked: boolean;
  group_image: string;
  description: string;
  groupMembers: GroupMembers[];
  location: string;
  is_member?: boolean;
  group_type?: string; // Added group_type property
  loginUserExist?: boolean; // Added this property
  is_group_admin?: boolean; // Added this property
  has_user_requested?: boolean; // Added this property
}
type ApiResponse = GroupsDetails[];

const JoinGroupDetails = () => {
  const theme = useThemeStore();

  const navigation = useNavigation<NavigationProp<CommunityStackParamList>>();
  const styles = createStyles(theme);
  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const requestToJoinGroupMutation = useRequestToJoinGroup();
  const queryClient = useQueryClient();

  const {id, from} = useRoute<RouteProp<CommunityStackParamList, 'JoinGroupDetails'>>().params;
  const joinOrLeaveMutation = useJoinOrLeaveGroup();
  const [groupDetails, setGroupDetails] = React.useState<GroupsDetails | null>(null);

  const {t} = useTranslation();

  const {data, isLoading, refetch} = useQuery<ApiResponse>({
    queryKey: ['group-details', id],
    gcTime: 0,
    queryFn: async () => {
      const response = await getGroupDetails(id);
      return response as ApiResponse;
    },
  });
  useEffect(() => {
    setGroupDetails(data);
  }, [data]);
  const groupDetailsData: GroupsDetails = (groupDetails || {}) as GroupsDetails;

  const handleJoinOrLeave = async () => {
    try {
      const response = joinOrLeaveMutation.mutateAsync({groupId: id, refetch: true});

      if (response?.status === true) {
        queryClient.invalidateQueries({queryKey: ['my-groups']});
        setGroupDetails({
          ...groupDetailsData,
          loginUserExist: !groupDetailsData.loginUserExist, // Toggle membership status
        });
      }

      // refetch(); // Refresh group details after joining/leaving
    } catch (error) {
      setGroupDetails({
        ...groupDetailsData,
        loginUserExist: groupDetailsData.loginUserExist, // Revert membership status on error
      });
      toaster('error', (error as Error).message, 'top');
    }
  };

  const requestToJoinGroup = async () => {
    try {
      const response = await requestToJoinGroupMutation.mutateAsync({
        groupId: id,
        refetch: false,
      });
      if (response?.status === true) {
        toaster('success', response.message, 'top');
        queryClient.invalidateQueries({queryKey: ['group-details']});
        queryClient.invalidateQueries({queryKey: ['my-groups']});
        queryClient.invalidateQueries({queryKey: ['public-groups']});
      }
    } catch (error: any) {
      toaster('error', error.message, 'top');
    }
  };

  return (
    <SafeAreaView includeBottom={false} style={styles.container}>
      <Header
        leftIcon={{
          name: 'side-menu',
          size: 24,
          color: theme.colors.white,
          containerStyle: theme.colors.primary,
        }}
        leftIconButtonStyle={styles.menuButton}
        onLeftPress={openDrawer}
        rightIcons={[
          {name: 'notification', size: 24, badge: 0},
          {name: 'chat', size: 24, badge: 14},
        ]}
        pageTitle={
          from === 'my-groups'
            ? t('joinGroupDetailsScreen.groupDetails')
            : t('joinGroupDetailsScreen.title')
        }
        backgroundColor="transparent"
        // showBack={false}
      />
      {/* <View style={styles.header}>
        <TouchableOpacity activeOpacity={0.8} onPress={() => navigation.goBack()}>
          <Typography variant="parkTitle" style={styles.cancel}>
            {t('common.cancel')}
          </Typography>
        </TouchableOpacity>
        <TouchableOpacity activeOpacity={0.8} onPress={() => navigation.goBack()}>
          <Typography variant="parkTitle" style={styles.next}>
            {t('common.next')}
          </Typography>
        </TouchableOpacity>
      </View> */}
      {isLoading ? (
        <CLoader />
      ) : (
        <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
          <View style={styles.groupInfoCard}>
            <View style={styles.avatarContainer}>
              {groupDetailsData.group_image ? (
                <CImage
                  source={{uri: groupDetailsData.group_image}}
                  style={styles.avatar}
                  resizeMode="contain"
                />
              ) : (
                <View style={styles.groupIconContainer}>
                  <Icon name="groupicon-1" size={33} color={theme.colors.black} />
                </View>
              )}
              <View style={styles.groupInfoContent}>
                <Typography variant="parkTitle" style={styles.groupName}>
                  {groupDetailsData.group_name}
                </Typography>
                <Typography variant="caption" style={styles.members}>
                  {groupDetailsData.groupMembers?.length} members
                </Typography>
                <View style={styles.locationRow}>
                  <Icon
                    name="location-pin"
                    size={22}
                    color={theme?.colors?.activeColor}
                    style={{marginLeft: -4}}
                  />
                  <Typography variant="caption" style={styles.location}>
                    {groupDetailsData.location}
                  </Typography>
                </View>
              </View>
            </View>

            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => {
                if (groupDetailsData?.group_type === 'private') {
                  if (groupDetailsData?.has_user_requested) {
                    console.log('Already requested to join');
                  } else if (groupDetailsData?.loginUserExist) {
                    handleJoinOrLeave();
                  } else {
                    requestToJoinGroup();
                  }
                } else {
                  if (groupDetailsData?.loginUserExist) {
                    handleJoinOrLeave();
                  } else {
                    handleJoinOrLeave();
                  }
                }
                // groupDetailsData?.group_type === 'private' && !groupDetailsData?.has_user_requested
                //   ? requestToJoinGroup()
                //   : handleJoinOrLeave();
              }}
              disabled={joinOrLeaveMutation.isPending || requestToJoinGroupMutation?.isPending}>
              {joinOrLeaveMutation.isPending || requestToJoinGroupMutation?.isPending ? (
                <ActivityIndicator size="small" color={theme.colors.primary} />
              ) : (
                <Typography variant="playerTitle" style={styles.joinText}>
                  {groupDetailsData?.group_type === 'private'
                    ? groupDetailsData?.has_user_requested
                      ? 'Requested '
                      : groupDetailsData?.loginUserExist
                        ? t('joinGroupDetailsScreen.leaveGroup')
                        : t('joinGroupDetailsScreen.requestToJoin')
                    : groupDetailsData.loginUserExist
                      ? t('joinGroupDetailsScreen.leaveGroup')
                      : t('joinGroupDetailsScreen.joinGroup')}
                  {/* {groupDetailsData?.group_type !== 'private'
                    ? groupDetailsData.loginUserExist
                      ? t('joinGroupDetailsScreen.leaveGroup')
                      : t('joinGroupDetailsScreen.joinGroup')
                    : groupDetailsData?.group_type === 'private' &&
                      !groupDetailsData.loginUserExist &&
                      t('joinGroupDetailsScreen.requestToJoin')} */}
                </Typography>
              )}
            </TouchableOpacity>
          </View>
          <Typography variant="description" style={styles.description}>
            {groupDetailsData.description}
          </Typography>

          {groupDetailsData?.is_group_admin && (
            <TouchableOpacity
              style={styles.requestsButton}
              onPress={() => navigation.navigate('RequestList', {groupId: id})}>
              <Typography variant="subtitle" color={theme.colors.activeColor}>
                {t('joinGroupDetailsScreen.requestsList')}
              </Typography>
            </TouchableOpacity>
          )}

          <Typography
            variant="playerTitle"
            style={[styles.joinText, {marginTop: 20, marginBottom: -8}]}>
            {t('joinGroupDetailsScreen.members')}
          </Typography>

          <FlatList
            data={groupDetailsData.groupMembers}
            renderItem={({item}) => <PlayerCard playerData={item} showAdd={false} />}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.playerList}
          />
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

export default JoinGroupDetails;
