import React, {useState, useCallback, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Linking,
  Platform,
} from 'react-native';
import {
  Camera,
  useCameraDevice,
  useCodeScanner,
  useCameraPermission,
} from 'react-native-vision-camera';
import {check, PERMISSIONS, RESULTS, openSettings} from 'react-native-permissions';
import {useThemeStore} from '@/store/themeStore';

interface QRScannerProps {
  onScan: (data: string) => void;
  onClose?: () => void;
  isActive?: boolean;
}

const {width} = Dimensions.get('window');
const frameSize = width * 0.7;

const QRScanner: React.FC<QRScannerProps> = ({onScan, onClose, isActive = true}) => {
  const theme = useThemeStore();
  const [scannerActive, setScannerActive] = useState(true);
  const {hasPermission, requestPermission} = useCameraPermission();
  const [permissionBlocked, setPermissionBlocked] = useState(false);

  const cameraPermission = Platform.select({
    ios: PERMISSIONS.IOS.CAMERA,
    android: PERMISSIONS.ANDROID.CAMERA,
  });

  // Check permission status
  useEffect(() => {
    if (isActive && cameraPermission) {
      check(cameraPermission).then(result => {
        console.log('🚀 ~ check ~ result:', result, RESULTS);
        if (result === RESULTS.BLOCKED || result === RESULTS.DENIED) {
          setPermissionBlocked(true);
        } else {
          setPermissionBlocked(false);
        }
      });
    }
  }, [isActive, cameraPermission]);

  // Request permission if not blocked
  useEffect(() => {
    if (isActive && !hasPermission && !permissionBlocked) {
      requestPermission();
    }
  }, [isActive, hasPermission, permissionBlocked, requestPermission]);

  const device = useCameraDevice('back');

  const resetScanner = useCallback(() => {
    setScannerActive(true);
  }, []);

  const codeScanner = useCodeScanner({
    codeTypes: ['qr'],
    onCodeScanned: codes => {
      if (codes.length > 0 && scannerActive) {
        setScannerActive(false);
        if (codes[0].value) {
          onScan(codes[0].value);
          setTimeout(() => {
            resetScanner();
          }, 2000);
        }
      }
    },
  });

  const styles = StyleSheet.create({
    container: {flex: 1, backgroundColor: 'transparent'},
    camera: {flex: 1},
    markerContainer: {
      ...StyleSheet.absoluteFillObject,
      justifyContent: 'center',
      alignItems: 'center',
    },
    scanFrame: {
      width: frameSize,
      height: frameSize,
      position: 'relative',
    },
    corner: {
      position: 'absolute',
      width: 40,
      height: 40,
      borderColor: theme.colors.text,
      borderWidth: 3,
    },
    cornerTL: {
      top: 0,
      left: 0,
      borderBottomWidth: 0,
      borderRightWidth: 0,
      borderTopLeftRadius: 20,
    },
    cornerTR: {
      top: 0,
      right: 0,
      borderBottomWidth: 0,
      borderLeftWidth: 0,
      borderTopRightRadius: 20,
    },
    cornerBL: {
      bottom: 0,
      left: 0,
      borderTopWidth: 0,
      borderRightWidth: 0,
      borderBottomLeftRadius: 20,
    },
    cornerBR: {
      bottom: 0,
      right: 0,
      borderTopWidth: 0,
      borderLeftWidth: 0,
      borderBottomRightRadius: 20,
    },
    scanText: {
      color: theme.colors.white,
      marginTop: 20,
      fontSize: 16,
      textAlign: 'center',
    },
    closeButton: {
      position: 'absolute',
      bottom: 40,
      alignSelf: 'center',
      padding: 15,
      zIndex: 10,
    },
    closeText: {
      color: theme.colors.white,
      fontSize: 16,
    },
    permissionContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.background,
    },
    permissionText: {
      color: theme.colors.text,
      fontSize: 16,
      textAlign: 'center',
      marginBottom: 20,
      paddingHorizontal: 30,
    },
    permissionButton: {
      backgroundColor: theme.colors.primary,
      paddingVertical: 10,
      paddingHorizontal: 20,
      borderRadius: 8,
    },
    permissionButtonText: {
      color: theme.colors.white,
      fontSize: 16,
    },
  });

  // Inactive state
  if (!isActive) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>Switch to Direct Connect tab to scan QR codes</Text>
      </View>
    );
  }

  // No permission screen
  if (!hasPermission) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>Camera permission is required to scan QR codes</Text>
        <TouchableOpacity
          style={styles.permissionButton}
          onPress={() => {
            if (permissionBlocked) {
              openSettings().catch(() => {
                Linking.openURL('app-settings:');
              });
            } else {
              requestPermission();
            }
          }}>
          <Text style={styles.permissionButtonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // No camera
  if (device == null) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>No camera device available</Text>
      </View>
    );
  }

  // Main scanner view
  return (
    <View style={styles.container}>
      <Camera
        style={styles.camera}
        device={device}
        isActive={isActive && hasPermission}
        codeScanner={codeScanner}
      />
      <View style={styles.markerContainer}>
        <View style={styles.scanFrame}>
          <View style={[styles.cornerTL, styles.corner]} />
          <View style={[styles.cornerTR, styles.corner]} />
          <View style={[styles.cornerBL, styles.corner]} />
          <View style={[styles.cornerBR, styles.corner]} />
        </View>
        <Text style={styles.scanText}>Scan QR Code to connect</Text>
      </View>
      {onClose && (
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeText}>Cancel</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default QRScanner;
