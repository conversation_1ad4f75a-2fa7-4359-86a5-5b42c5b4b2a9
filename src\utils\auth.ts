import {GoogleSignin, statusCodes} from '@react-native-google-signin/google-signin';
import {
  LoginManager,
  AccessToken,
  GraphRequest,
  GraphRequestManager,
  Settings,
  Profile,
  AuthenticationToken,
} from 'react-native-fbsdk-next';
import {Platform} from 'react-native';

// Initialize Google Sign In
// Call this function in your App.tsx or early in your app initialization
export const configureGoogleSignIn = () => {
  GoogleSignin.configure({
    // Replace with your actual web client ID from Google Cloud Console
    webClientId: '954151296869-c4744r6dca0ur9nrhlqec0kb7399efu7.apps.googleusercontent.com',
    // offlineAccess: true,
  });
};

// Initialize Facebook SDK
// Call this function in your App.tsx or early in your app initialization
export const configureFacebookSDK = () => {
  // No specific configuration needed for Facebook SDK in JavaScript
  // The configuration is already done in the AndroidManifest.xml and Info.plist
  // This function is provided for API consistency
  Settings.initializeSDK();
};

/**
 * Helper function to handle Google authentication
 */
export const signInWithGoogle = async (): Promise<{
  success: boolean;
  data?: {
    name: string;
    email: string;
    idToken: string;
    accessToken: string;
    refreshToken: string;
    user?: any;
    User?: any;
  };
  error?: string;
}> => {
  try {
    // Check if Google Play Services are available
    await GoogleSignin.hasPlayServices({showPlayServicesUpdateDialog: true});

    // Sign in
    const res = await GoogleSignin.signIn();

    // Get user info from getCurrentUser which returns the cached user
    const currentUser = await GoogleSignin.getCurrentUser();
    console.log('🚀 ~ signInWithGoogle ~ currentUser:', currentUser);

    await GoogleSignin.revokeAccess();
    if (res.data?.idToken) {
      return {
        success: true,
        data: res?.data,
      };
    } else {
      return {
        success: false,
        error: 'Failed to get Google user info',
      };
    }
  } catch (error: any) {
    let errorMessage = 'Failed to sign in with Google';

    if (error.code) {
      switch (error.code) {
        case statusCodes.SIGN_IN_CANCELLED:
          errorMessage = 'Google sign in was cancelled';
          break;
        case statusCodes.IN_PROGRESS:
          errorMessage = 'Google sign in is already in progress';
          break;
        case statusCodes.PLAY_SERVICES_NOT_AVAILABLE:
          errorMessage = 'Google Play Services is not available';
          break;
        default:
          errorMessage = error.message || errorMessage;
          break;
      }
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
};

/**
 * Helper function to handle Facebook authentication
 */
export const signInWithFacebook = async (): Promise<{
  success: boolean;
  user?: {name: string; email: string};
  data?: {
    accessToken?: string;
    applicationID?: string;
    userID?: string;
    authenticationToken?: string;
  };
  error?: string;
}> => {
  try {
    // Check if we're on iOS to use Limited Login or standard login for Android
    if (Platform.OS === 'ios') {
      // Use Limited Login for iOS
      const result = await LoginManager.logInWithPermissions(
        ['public_profile', 'email'],
        'limited',
      );
      console.log('🚀 ~ signInWithFacebook ~ result:', result);

      if (result.isCancelled) {
        return {
          success: false,
          error: 'Facebook login was cancelled',
        };
      }

      // Get authentication token (used by Limited Login)
      const data = await AuthenticationToken.getAuthenticationTokenIOS();
      console.log('🚀 ~ signInWithFacebook ~ data:', data);

      if (!data) {
        return {
          success: false,
          error: 'Failed to get Facebook authentication token',
        };
      }

      // Get user profile data
      const profile = await Profile.getCurrentProfile();
      console.log('🚀 ~ signInWithFacebook ~ profile:', profile);

      if (!profile) {
        return {
          success: false,
          error: 'Failed to get Facebook profile data',
        };
      }

      return {
        success: true,
        user: {
          name: profile.name || 'Facebook User',
          // Limited Login doesn't provide email, so we generate a placeholder
          email: `${profile.userID}@facebook.com`,
        },
        data: {...data},
      };
    } else {
      // Standard login flow for Android
      // Attempt login with permissions
      const response = await LoginManager.logInWithPermissions(['public_profile', 'email']);
      console.log('🚀 ~ signInWithFacebook ~ response:', response);

      if (response.isCancelled) {
        return {
          success: false,
          error: 'Facebook login was cancelled',
        };
      }

      // Get access token
      const data = await AccessToken.getCurrentAccessToken();
      console.log('🚀 ~ getCurrentAccessToken ~ response:', data);

      if (!data) {
        return {
          success: false,
          error: 'Failed to get Facebook access token',
        };
      }

      // Return a Promise that resolves with user data from Graph API
      return new Promise(resolve => {
        // Create a graph request to get user data
        const responseCallback = (error: any, result: any) => {
          if (error) {
            resolve({
              success: false,
              error: 'Error fetching Facebook user data',
            });
            return;
          }

          resolve({
            success: true,
            user: {
              name: result.name || 'Facebook User',
              email: result.email || `${result.id}@facebook.com`,
            },
            data: {...data},
          });
        };

        const infoRequest = new GraphRequest(
          '/me',
          {
            accessToken: data.accessToken,
            parameters: {
              fields: {
                string: 'email,name',
              },
            },
          },
          responseCallback,
        );

        // Start the graph request
        new GraphRequestManager().addRequest(infoRequest).start();
      });
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to sign in with Facebook';
    return {
      success: false,
      error: errorMessage,
    };
  }
};

/**
 * Helper function to handle email registration
 */
export const registerWithEmail = async (
  email: string,
  password: string,
  fullName: string,
): Promise<{success: boolean; user?: {name: string; email: string}; error?: string}> => {
  try {
    // Simulating email registration
    // In a real implementation, you would use a backend API or Firebase

    // Mock success response
    const userInfo = {
      name: fullName,
      email: email,
    };

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      success: true,
      user: userInfo,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to register with email';
    return {
      success: false,
      error: errorMessage,
    };
  }
};

/**
 * Helper function to handle email login
 */
export const loginWithEmail = async (
  email: string,
  password: string,
): Promise<{success: boolean; user?: {name: string; email: string}; error?: string}> => {
  try {
    // Simulating email login
    // In a real implementation, you would use a backend API or Firebase

    // Mock success response
    const userInfo = {
      name: 'Email User',
      email: email,
    };

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      success: true,
      user: userInfo,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to login with email';
    return {
      success: false,
      error: errorMessage,
    };
  }
};
