import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flexGrow: 1,
      backgroundColor: 'transparent',
      paddingHorizontal: 16,
    },
    containerMain: {
      flex: 1,
      backgroundColor: 'transparent',
      paddingHorizontal: 16,
    },
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.secondary,
      marginVertical: 10,
      width: '100%',
      alignSelf: 'center',
    },

    headerRow: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 5,
    },
    headerTitle: {
      color: theme.colors.white,
      fontSize: 22,
      fontWeight: '700',
    },
    photoContainer: {
      alignItems: 'center',
      marginVertical: 12,
    },
    photoCircle: {
      width: 142,
      height: 142,
      borderRadius: 100,
      backgroundColor: theme.colors.activeColor,
      alignItems: 'center',
      justifyContent: 'center',
    },
    addPhotoText: {
      color: theme.colors.black,
      fontWeight: '600',
      fontSize: 16,
    },
    label: {
      color: theme.colors.white,
      fontWeight: '600',
      fontSize: 15,
      marginBottom: 6,
      marginTop: 10,
    },
    subLabel: {
      color: theme.colors.white,
      fontWeight: '400',
      fontSize: 13,
      marginTop: 10,
      marginBottom: 4,
    },
    textArea: {
      backgroundColor: theme.colors.white,
      borderRadius: 8,
      color: theme.colors.black,
      height: 140,
      textAlignVertical: 'top',
      padding: 12,
      marginBottom: -6,
    },
    row: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      backgroundColor: theme.colors.white,
      borderRadius: 8,
      gap: 5,
      paddingVertical: 16,
      paddingHorizontal: 12,
    },
    rowBetween: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginVertical: 8,
    },
    pill: {
      flex: 1,
      borderTopLeftRadius: 12,
      borderTopRightRadius: 12,
      borderBottomLeftRadius: 12,
      height: 60,
      paddingTop: 10,
      paddingLeft: 15,
    },
    pillText: {
      fontSize: 16,
      fontWeight: '400',
      lineHeight: 18,
    },
    addPill: {
      backgroundColor: theme.colors.background,
      borderRadius: 16,
      width: 32,
      height: 32,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    input: {
      height: 70,
      backgroundColor: theme.colors.white,
      borderRadius: 8,
      color: theme.colors.black,
      marginBottom: -6,
    },

    togglePill: {
      backgroundColor: theme.colors.background,
      borderRadius: 12,
      paddingHorizontal: 24,
      paddingVertical: 10,
      marginRight: 12,
      alignItems: 'center',
      justifyContent: 'center',
      minWidth: 70,
    },
    rateRow: {
      marginBottom: 8,
    },
    rateInput: {
      backgroundColor: theme.colors.white,
      borderRadius: 12,
      color: theme.colors.black,
      height: 65,
      fontSize: 32,
      fontWeight: '700',
      paddingLeft: 20,
    },
    rateSuffix: {
      color: theme.colors.white,
      fontSize: 18,
      fontWeight: '400',
    },
    rowWrap: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 10,
      marginBottom: 8,
    },
    submitButton: {
      backgroundColor: theme.colors.activeColor,
      borderRadius: 12,
      paddingVertical: 14,
      alignItems: 'center',
      marginTop: 24,
      marginBottom: 32,
    },
    submitButtonText: {
      color: theme.colors.black,
      fontWeight: 'bold',
      fontSize: 18,
    },
    certificationRow: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#fff',
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 16,
      marginBottom: 12,
      gap: 5,
      flex: 1,
    },
    certificates: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      width: '88%',
    },
    certChip: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.white,
      borderRadius: 8,
      borderWidth: 2,
      borderColor: theme.colors.white,
      paddingHorizontal: 12,
      paddingVertical: 6,
      minWidth: 80,
      justifyContent: 'center',
    },
    certChipSelected: {
      borderColor: theme.colors.primary, // blue border when selected
    },
    certChipText: {
      color: theme.colors.black,
      fontWeight: 'bold',
      fontSize: 16,
      marginRight: 4,
    },
    certChipIcon: {
      marginLeft: 2,
    },
    plusIcon: {
      marginLeft: 'auto',
    },
    courtChip: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.white,
      borderRadius: 8,
      borderWidth: 2,
      borderColor: theme.colors.border,
      paddingHorizontal: 12,
      paddingVertical: 6,
      minWidth: 80,
      justifyContent: 'center',
    },
    courtChipText: {
      color: theme.colors.black,
      fontWeight: 'bold',
      fontSize: 16,
      marginRight: 4,
    },

    options: {
      flexDirection: 'row',
      gap: 10,
      flexWrap: 'wrap',
    },
    pill1: {
      width: '48%',
      height: 60,
      // backgroundColor: theme.colors.dimGray,
      borderTopLeftRadius: 12,
      borderTopRightRadius: 12,
      borderBottomLeftRadius: 12,
      paddingTop: 10,
      paddingLeft: 15,
      paddingRight: 15,
      paddingBottom: 12,
    },
    profileImage: {
      width: 142,
      height: 142,
      borderRadius: 100,
      backgroundColor: theme.colors.activeColor,
      justifyContent: 'center',
      alignItems: 'center',
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    gradientContainer: {
      width: '100%',
      height: '100%',
    },
    gradientContent: {
      width: '100%',
      borderRadius: 12,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      padding: 16,
      flex: 1,
      justifyContent: 'center',
    },
    borderContainer: {
      // flex: 1,
      height: Platform.OS === 'ios' ? '92%' : '100%',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.white,
      // padding: 10,
    },
  });
