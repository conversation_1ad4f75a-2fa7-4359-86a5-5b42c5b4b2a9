

<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> components/common/CustomMapView.tsx</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="styles/style.css">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
             
                <a href="index.html">
                    <h1 class="navbar-item">GoRaqt Documentation</h1>
                </a>
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    Documentation
                </a>
                
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
                <div class="search-wrapper">
                    <input id="search" type="text" placeholder="Search docs..." class="input">
                </div>
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Global</h3><ul><li><a href="global.html#HIDE_TAB_SCREENS">HIDE_TAB_SCREENS</a></li><li><a href="global.html#SENSITIVE_FIELDS">SENSITIVE_FIELDS</a></li><li><a href="global.html#addBreadcrumb">addBreadcrumb</a></li><li><a href="global.html#apiCall">apiCall</a></li><li><a href="global.html#authenticateWithBiometrics">authenticateWithBiometrics</a></li><li><a href="global.html#calculateAge">calculateAge</a></li><li><a href="global.html#checkAuthStatusApi">checkAuthStatusApi</a></li><li><a href="global.html#checkLocationPermission">checkLocationPermission</a></li><li><a href="global.html#cleanMemoryIfNeeded">cleanMemoryIfNeeded</a></li><li><a href="global.html#clearAllData">clearAllData</a></li><li><a href="global.html#configureStatusBar">configureStatusBar</a></li><li><a href="global.html#createHealthService">createHealthService</a></li><li><a href="global.html#deleteFcmTokenFromDB">deleteFcmTokenFromDB</a></li><li><a href="global.html#deleteNotification">deleteNotification</a></li><li><a href="global.html#disableBiometrics">disableBiometrics</a></li><li><a href="global.html#enableBiometrics">enableBiometrics</a></li><li><a href="global.html#forgotPasswordApi">forgotPasswordApi</a></li><li><a href="global.html#formatBirthdate">formatBirthdate</a></li><li><a href="global.html#getAddressFromCoordinates">getAddressFromCoordinates</a></li><li><a href="global.html#getCurrentLocationWithAddress">getCurrentLocationWithAddress</a></li><li><a href="global.html#getCurrentPosition">getCurrentPosition</a></li><li><a href="global.html#getCurrentRegion">getCurrentRegion</a></li><li><a href="global.html#getCurrentUserApi">getCurrentUserApi</a></li><li><a href="global.html#getNotificationsList">getNotificationsList</a></li><li><a href="global.html#getTabBarVisibility">getTabBarVisibility</a></li><li><a href="global.html#getUnreadNotificationsCount">getUnreadNotificationsCount</a></li><li><a href="global.html#getUserDetails">getUserDetails</a></li><li><a href="global.html#getUserProfile">getUserProfile</a></li><li><a href="global.html#handleNotificationNavigation">handleNotificationNavigation</a></li><li><a href="global.html#handleTextureError">handleTextureError</a></li><li><a href="global.html#initSentry">initSentry</a></li><li><a href="global.html#isBiometricsAvailable">isBiometricsAvailable</a></li><li><a href="global.html#isBiometricsEnabled">isBiometricsEnabled</a></li><li><a href="global.html#lastCacheClearTime">lastCacheClearTime</a></li><li><a href="global.html#logError">logError</a></li><li><a href="global.html#logMessage">logMessage</a></li><li><a href="global.html#loginWithEmail">loginWithEmail</a></li><li><a href="global.html#loginWithEmailApi">loginWithEmailApi</a></li><li><a href="global.html#logoutApi">logoutApi</a></li><li><a href="global.html#markAllNotificationsAsRead">markAllNotificationsAsRead</a></li><li><a href="global.html#markNotificationAsRead">markNotificationAsRead</a></li><li><a href="global.html#navigateBackFromExample">navigateBackFromExample</a></li><li><a href="global.html#navigateToOptimizedFlatListExample">navigateToOptimizedFlatListExample</a></li><li><a href="global.html#otpResendApi">otpResendApi</a></li><li><a href="global.html#otpVerifyApi">otpVerifyApi</a></li><li><a href="global.html#prefetchInitialQueries">prefetchInitialQueries</a></li><li><a href="global.html#prefetchPost">prefetchPost</a></li><li><a href="global.html#prefetchUser">prefetchUser</a></li><li><a href="global.html#refreshTokenApi">refreshTokenApi</a></li><li><a href="global.html#registerWithEmail">registerWithEmail</a></li><li><a href="global.html#registerWithEmailApi">registerWithEmailApi</a></li><li><a href="global.html#requestLocationPermission">requestLocationPermission</a></li><li><a href="global.html#resetPasswordApi">resetPasswordApi</a></li><li><a href="global.html#saveFcmTokenInDB">saveFcmTokenInDB</a></li><li><a href="global.html#schema">schema</a></li><li><a href="global.html#scrubObject">scrubObject</a></li><li><a href="global.html#scrubSensitiveData">scrubSensitiveData</a></li><li><a href="global.html#setCustomStatusBar">setCustomStatusBar</a></li><li><a href="global.html#setDarkStatusBar">setDarkStatusBar</a></li><li><a href="global.html#setLightStatusBar">setLightStatusBar</a></li><li><a href="global.html#setNavigationContext">setNavigationContext</a></li><li><a href="global.html#setStatusBarHidden">setStatusBarHidden</a></li><li><a href="global.html#setUserContext">setUserContext</a></li><li><a href="global.html#setupMemoryCleaning">setupMemoryCleaning</a></li><li><a href="global.html#setupTextureErrorHandlers">setupTextureErrorHandlers</a></li><li><a href="global.html#signInWithFacebook">signInWithFacebook</a></li><li><a href="global.html#signInWithGoogle">signInWithGoogle</a></li><li><a href="global.html#startTransaction">startTransaction</a></li><li><a href="global.html#styles">styles</a></li><li><a href="global.html#updateUserProfile">updateUserProfile</a></li><li><a href="global.html#useAuthStatus">useAuthStatus</a></li><li><a href="global.html#useCurrentUser">useCurrentUser</a></li><li><a href="global.html#useLogin">useLogin</a></li><li><a href="global.html#useRegister">useRegister</a></li><li><a href="global.html#useSafeArea">useSafeArea</a></li><li><a href="global.html#useSocialLogin">useSocialLogin</a></li><li><a href="global.html#useUpdateUserProfile">useUpdateUserProfile</a></li><li><a href="global.html#useUserProfile">useUserProfile</a></li></ul></div><div class="category"><h2>Components</h2><h3>Components</h3><ul><li><a href="Advertisement.html">Advertisement</a></li><li><a href="BiometricGate.html">BiometricGate</a></li><li><a href="CButton.html">CButton</a></li><li><a href="CreateGoRaqtProfile.html">CreateGoRaqtProfile</a></li><li><a href="CustomMapView.html">CustomMapView</a></li><li><a href="CustomMarker.html">CustomMarker</a></li><li><a href="NotificationCard.html">NotificationCard</a></li><li><a href="OfferBanner.html">OfferBanner</a></li><li><a href="Subscription.html">Subscription</a></li></ul></div><div class="category"><h2>Interfaces</h2><h3>Interfaces</h3><ul><li><a href="AdvertisementProps.html">AdvertisementProps</a></li><li><a href="ApiResponse.html">ApiResponse</a></li><li><a href="BiometricGateProps.html">BiometricGateProps</a></li><li><a href="CButtonProps.html">CButtonProps</a></li><li><a href="CreateProfileProps.html">CreateProfileProps</a></li><li><a href="CustomMarkerProps.html">CustomMarkerProps</a></li><li><a href="DrawerItemType.html">DrawerItemType</a></li><li><a href="ForgotPasswordScreenProps.html">ForgotPasswordScreenProps</a></li><li><a href="Notification.html">Notification</a></li><li><a href="NotificationCardProps.html">NotificationCardProps</a></li><li><a href="PaginationState.html">PaginationState</a></li><li><a href="PermissionsScreenProps.html">PermissionsScreenProps</a></li><li><a href="RouteParams.html">RouteParams</a></li><li><a href="SentryNavigationContainerProps.html">SentryNavigationContainerProps</a></li><li><a href="SocialRegisterRequest.html">SocialRegisterRequest</a></li><li><a href="SplashScreenProps.html">SplashScreenProps</a></li><li><a href="TermsOfServiceScreenProps.html">TermsOfServiceScreenProps</a></li><li><a href="UseLogoutReturn.html">UseLogoutReturn</a></li></ul><h3><a href="global.html">Global</a></h3></div><div class="category"><h2>Navigation</h2><h3>Components</h3><ul><li><a href="DrawerNavigator.html">DrawerNavigator</a></li><li><a href="SentryNavigationContainer.html">SentryNavigationContainer</a></li></ul></div><div class="category"><h2>Screens</h2><h3>Components</h3><ul><li><a href="CommunityDetails.html">CommunityDetails</a></li><li><a href="DirectFetchTest.html">DirectFetchTest</a></li><li><a href="EmailSignupScreen.html">EmailSignupScreen</a></li><li><a href="ForgotPasswordScreen.html">ForgotPasswordScreen</a></li><li><a href="LoginScreen.html">LoginScreen</a></li><li><a href="MyMatchesScreen.html">MyMatchesScreen</a></li><li><a href="NotificationsListScreen.html">NotificationsListScreen</a></li><li><a href="PermissionsScreen.html">PermissionsScreen</a></li><li><a href="SignupScreen.html">SignupScreen</a></li><li><a href="SplashScreen.html">SplashScreen</a></li><li><a href="TermsOfServiceScreen.html">TermsOfServiceScreen</a></li><li><a href="Verification.html">Verification</a></li></ul></div><div class="category"><h2>Utils</h2><h3>Global</h3><ul><li><a href="global.html#api">api</a></li><li><a href="global.html#socialLoginApi">socialLoginApi</a></li><li><a href="global.html#useLogout">useLogout</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Source</p>
                    <h1>components/common/CustomMapView.tsx</h1>
                </header>
                



    
    <section>
        <article>
            <pre class="prettyprint source linenums"><code>import React, {useState, useEffect, useRef, useMemo, useCallback} from 'react';
import {View, StyleSheet, Platform, Dimensions, ActivityIndicator} from 'react-native';
import MapView, {Marker, PROVIDER_GOOGLE, Region} from 'react-native-maps';
import {darkMapStyle} from '@/utils/mapStyles';
import {getCurrentPosition} from '@/utils/locationService';
import CustomMarker from './CustomMarker';
import {useThemeStore} from '@/store';

const {height} = Dimensions.get('window');

const isAndroid = Platform.OS === 'android';

// Maximum number of markers to render at once to prevent memory issues
const MAX_VISIBLE_MARKERS = 50;

interface pins {
  id: string;
  coordinate: {
    latitude: number;
    longitude: number;
  };
  display_name: string;
  email: string;
  name: string;
  profile_pic: string;
  group_image: string;
  userData: {
    location: string;
  };
}

interface CustomMapViewProps {
  pins: pins[];
  initialRegion?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  showUserLocation?: boolean;
  style?: React.CSSProperties | React.CSSProperties[] | object;
  markerType?: 'player' | 'coach' | 'shop' | 'default';
  onMarkerPress?: (pin: pins) => void;
}

/**
 * @component
 * @category Components
 * @description
 * A custom MapView component that handles user location and shows visible pins.
 *
 * Props:
 *   pins: An array of MapPin objects to display on the map.
 *   initialRegion: An optional object with latitude, longitude, latitudeDelta, and longitudeDelta
 *     properties to set the initial region of the map. Defaults to a region for NYC.
 *   showUserLocation: An optional boolean to indicate whether to show the user's location on the map.
 *     Defaults to true.
 *   style: An optional object with React Native styles to customize the component's container.
 *   markerType: An optional string to specify the type of markers to display. Can be 'player', 'coach',
 *     'shop', or 'default'. Defaults to 'default'.
 *   onMarkerPress: An optional function to call when a marker is pressed. The function receives the
 *     corresponding MapPin object as an argument.
 *
 * Notes:
 *   The component will only show up to MAX_VISIBLE_MARKERS pins at a time to prevent memory issues.
 *   The pins are filtered by visibility based on the map's current region, and then sorted by distance
 *     to the center of the map. If there are too many pins, the component will use a more aggressive
 *     filtering strategy to reduce the number of visible pins.
 *
 * @prop {CustomMapViewProps} props - The properties for the CustomMapView component
 * @returns {JSX.Element} - Returns a MapView component with markers for each pin
 */
const CustomMapView: React.FC&lt;CustomMapViewProps> = ({
  pins,
  initialRegion = {
    latitude: 40.7128,
    longitude: -74.006,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
  showUserLocation = true,
  style,
  markerType = 'default',
  onMarkerPress,
}) => {
  const theme = useThemeStore();
  const mapRef = useRef&lt;MapView>(null);
  const [userLocation, setUserLocation] = useState&lt;{latitude: number; longitude: number} | null>(
    null,
  );
  // Using underscore prefix to indicate unused state variable
  const [_isAtUserLocation, setIsAtUserLocation] = useState(true);
  const [isLoaded, setIsLoaded] = useState(true);
  useEffect(() => {
    setTimeout(() => {
      setIsLoaded(false);
    }, 400);
  }, []);

  const [showLocationButton, setShowLocationButton] = useState(false);
  // Initialize visibleRegion with initialRegion to show pins immediately
  const [visibleRegion, setVisibleRegion] = useState&lt;Region | null>(initialRegion);
  const [loadingLocation, setLoadingLocation] = useState(true);
  const [pinsLoaded, setPinsLoaded] = useState(false);

  const [pinId, setPinId] = useState('');

  // Track if component is mounted to prevent memory leaks
  const isMounted = useRef(true);

  // Get user's current location
  useEffect(() => {
    const fetchUserLocation = async () => {
      try {
        if (isAndroid) {
          await new Promise(resolve => setTimeout(resolve, 500)); // Android delay
        }

        const position = await getCurrentPosition();
        if (position &amp;&amp; isMounted.current) {
          setUserLocation(position);
          setIsAtUserLocation(true);
          setShowLocationButton(false);
        }
      } catch (error) {
        console.error('Error getting user location:', error);
        if (isMounted.current) {
          setShowLocationButton(true);
        }
      } finally {
        if (isMounted.current) {
          setLoadingLocation(false);
        }
      }
    };

    if (showUserLocation) {
      fetchUserLocation();
    }

    // Cleanup function
    return () => {
      isMounted.current = false;
    };
  }, [showUserLocation]);

  // Cleanup function when component unmounts
  useEffect(() => {
    return () => {
      isMounted.current = false;
      setVisibleRegion(null);
      setUserLocation(null);
    };
  }, []);

  // Effect to handle pins loading
  useEffect(() => {
    if (pins &amp;&amp; pins.length > 0 &amp;&amp; isMounted.current) {
      // Small delay to ensure pins are processed properly
      const timer = setTimeout(() => {
        if (isMounted.current) {
          setPinsLoaded(true);
        }
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [pins]);

  const isMapAtUserLocation = useCallback(
    (region: Region): boolean => {
      if (!userLocation) return false;
      const latDiff = Math.abs(region.latitude - userLocation.latitude);
      const lngDiff = Math.abs(region.longitude - userLocation.longitude);
      return latDiff &lt; 0.001 &amp;&amp; lngDiff &lt; 0.001;
    },
    [userLocation],
  );

  const handleRegionChange = useCallback(
    (region: Region) => {
      if (!isMounted.current) return;

      setVisibleRegion(region);

      if (userLocation) {
        const atUserLocation = isMapAtUserLocation(region);
        setIsAtUserLocation(atUserLocation);
        setShowLocationButton(!atUserLocation);
      }
    },
    [userLocation, isMapAtUserLocation],
  );

  const centerOnUserLocation = useCallback(async () => {
    if (userLocation &amp;&amp; mapRef.current) {
      mapRef.current.animateToRegion(
        {
          latitude: userLocation.latitude,
          longitude: userLocation.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        },
        1000,
      );
      setIsAtUserLocation(true);
      setShowLocationButton(false);
    } else {
      try {
        const position = await getCurrentPosition();
        if (position &amp;&amp; mapRef.current &amp;&amp; isMounted.current) {
          setUserLocation(position);
          mapRef.current.animateToRegion(
            {
              latitude: position.latitude,
              longitude: position.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            },
            1000,
          );
          setIsAtUserLocation(true);
          setShowLocationButton(false);
        }
      } catch (error) {
        console.error('Error getting user location:', error);
      }
    }
  }, [userLocation]);

  // Optimizing visible pins based on the map's current region
  const isMarkerVisible = useCallback(
    (coordinate: {latitude: number; longitude: number}): boolean => {
      // Use visibleRegion if available, otherwise fall back to initialRegion
      const region = visibleRegion || initialRegion;

      const {latitude, longitude} = coordinate;
      const {latitude: centerLat, longitude: centerLng, latitudeDelta, longitudeDelta} = region;

      const latMin = centerLat - latitudeDelta / 2;
      const latMax = centerLat + latitudeDelta / 2;
      const lngMin = centerLng - longitudeDelta / 2;
      const lngMax = centerLng + longitudeDelta / 2;

      // Reduced padding to show fewer markers
      const latPadding = latitudeDelta * 0.1;
      const lngPadding = longitudeDelta * 0.1;

      return (
        latitude >= latMin - latPadding &amp;&amp;
        latitude &lt;= latMax + latPadding &amp;&amp;
        longitude >= lngMin - lngPadding &amp;&amp;
        longitude &lt;= lngMax + lngPadding
      );
    },
    [visibleRegion, initialRegion],
  );

  // Memoize the visible pins so they are only recalculated when necessary
  // Limit the number of visible pins to prevent memory issues
  const visiblePins = useMemo(() => {
    if (!pins) return [];

    // Use visibleRegion if available, otherwise fall back to initialRegion
    const region = visibleRegion || initialRegion;

    // If there are too many pins, use a more aggressive filtering strategy
    if (pins.length > MAX_VISIBLE_MARKERS * 2) {
      // First filter by visibility, then take only the closest ones to the center
      const centerLat = region.latitude;
      const centerLng = region.longitude;

      return (
        pins
          .filter(pin => isMarkerVisible(pin.coordinate))
          // Sort by distance to center (approximate calculation for better performance)
          .sort((a, b) => {
            const distA =
              Math.pow(a.coordinate.latitude - centerLat, 2) +
              Math.pow(a.coordinate.longitude - centerLng, 2);
            const distB =
              Math.pow(b.coordinate.latitude - centerLat, 2) +
              Math.pow(b.coordinate.longitude - centerLng, 2);
            return distA - distB;
          })
          .slice(0, MAX_VISIBLE_MARKERS)
      );
    }

    // For fewer pins, just filter by visibility
    const filtered = pins
      .filter(pin => isMarkerVisible(pin.coordinate))
      .slice(0, MAX_VISIBLE_MARKERS);

    return filtered;
  }, [pins, visibleRegion, initialRegion, isMarkerVisible]);

  return (
    &lt;View style={[styles(theme).container, style]}>
      &lt;MapView
        ref={mapRef}
        provider={PROVIDER_GOOGLE}
        style={styles(theme).mapStyle}
        initialRegion={initialRegion}
        customMapStyle={darkMapStyle}
        rotateEnabled={false}
        zoomControlEnabled={false}
        scrollEnabled={pinsLoaded} // Only enable scrolling after pins are loaded
        zoomEnabled={pinsLoaded} // Only enable zooming after pins are loaded
        toolbarEnabled={isAndroid ? false : undefined}
        moveOnMarkerPress={false}
        showsCompass={false}
        showsScale={false}
        pitchEnabled={false}
        followsUserLocation={false}
        showsMyLocationButton={isLoaded}
        showsUserLocation={showUserLocation &amp;&amp; !loadingLocation} // Show location once fetched
        onRegionChangeComplete={handleRegionChange}
        onMapReady={() => {
          // Ensure map is ready for interaction
          if (isMounted.current &amp;&amp; !pinsLoaded &amp;&amp; pins.length > 0) {
            setPinsLoaded(true);
          }
        }}>
        {visiblePins.map(pin => {
          return (
            &lt;Marker
              key={pin.id}
              coordinate={pin.coordinate}
              tracksViewChanges={false}
              onPress={e => {
                // Prevent default behavior that might show a callout
                e.stopPropagation();
                onMarkerPress?.(pin);
              }}
              // Important for performance
            >
              &lt;CustomMarker
                key={pinId}
                type={
                  markerType === 'player'
                    ? 'player'
                    : (pin.type as 'available_kiosk' | 'planned_location')
                }
                pin={pin}
                onChange={id => {
                  setPinId(id);
                }}
              />
              {/* {markerType === 'player' &amp;&amp; (
                &lt;Callout tooltip={true}>
                  &lt;View style={{width: Dimensions.get('window').width * 0.9}}>
                    &lt;PlayerSchedulePlayCard
                      playerName={pin.title}
                      location={pin.description}
                      image={pin.image}
                      onPress={() => {
                        console.log('player pressed');
                      }}
                    />
                  &lt;/View>
                &lt;/Callout>
              )} */}
            &lt;/Marker>
          );
        })}
      &lt;/MapView>

      {/* Loading indicator while pins are loading */}
      {!pinsLoaded &amp;&amp; pins.length > 0 &amp;&amp; (
        &lt;View style={styles(theme).loadingContainer}>
          &lt;ActivityIndicator size="large" color={theme.colors.activeColor} />
        &lt;/View>
      )}

      {/* Show location button if needed */}
      {/* {showUserLocation &amp;&amp; showLocationButton &amp;&amp; !loadingLocation &amp;&amp; (
        &lt;TouchableOpacity
          style={styles(theme).centerButton}
          onPress={centerOnUserLocation}
          activeOpacity={0.8}>
          &lt;Icon name="location" size={20} color="#666666" />
        &lt;/TouchableOpacity>
      )} */}
    &lt;/View>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    loadingContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
      zIndex: 10,
    },
    container: {
      flex: 1,
      position: 'relative',
      height: '100%',
      width: '100%',
    },
    mapStyle: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    centerButton: {
      position: 'absolute',
      top: height / 15,
      right: 12,
      backgroundColor: 'white',
      borderRadius: 8,
      width: 40,
      height: 40,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
      // zIndex: 999,
    },
  });

export default CustomMapView;
</code></pre>
        </article>
    </section>




            </div>
            
            <!-- Footer removed via patch -->
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

<script src="scripts/search.js"> </script>


</body>
</html>
