import React, {useState, useEffect, useRef, useMemo, useCallback} from 'react';
import {View, StyleSheet, Platform, Dimensions, ActivityIndicator} from 'react-native';
import MapView, {Marker, PROVIDER_GOOGLE, Region} from 'react-native-maps';
import {darkMapStyle} from '@/utils/mapStyles';
import {getCurrentPosition} from '@/utils/locationService';
import CustomMarker from './CustomMarker';
import {useThemeStore} from '@/store';

const {height} = Dimensions.get('window');

const isAndroid = Platform.OS === 'android';

// Maximum number of markers to render at once to prevent memory issues
const MAX_VISIBLE_MARKERS = 50;

interface pins {
  id: string;
  coordinate: {
    latitude: number;
    longitude: number;
  };
  display_name: string;
  email: string;
  name: string;
  profile_pic: string;
  group_image: string;
  userData: {
    location: string;
  };
}

interface CustomMapViewProps {
  pins: pins[];
  initialRegion?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  showUserLocation?: boolean;
  style?: React.CSSProperties | React.CSSProperties[] | object;
  markerType?: 'player' | 'coach' | 'shop' | 'default';
  onMarkerPress?: (pin: pins) => void;
}

/**
 * @component
 * @category Components
 * @description
 * A custom MapView component that handles user location and shows visible pins.
 *
 * Props:
 *   pins: An array of MapPin objects to display on the map.
 *   initialRegion: An optional object with latitude, longitude, latitudeDelta, and longitudeDelta
 *     properties to set the initial region of the map. Defaults to a region for NYC.
 *   showUserLocation: An optional boolean to indicate whether to show the user's location on the map.
 *     Defaults to true.
 *   style: An optional object with React Native styles to customize the component's container.
 *   markerType: An optional string to specify the type of markers to display. Can be 'player', 'coach',
 *     'shop', or 'default'. Defaults to 'default'.
 *   onMarkerPress: An optional function to call when a marker is pressed. The function receives the
 *     corresponding MapPin object as an argument.
 *
 * Notes:
 *   The component will only show up to MAX_VISIBLE_MARKERS pins at a time to prevent memory issues.
 *   The pins are filtered by visibility based on the map's current region, and then sorted by distance
 *     to the center of the map. If there are too many pins, the component will use a more aggressive
 *     filtering strategy to reduce the number of visible pins.
 *
 * @prop {CustomMapViewProps} props - The properties for the CustomMapView component
 * @returns {JSX.Element} - Returns a MapView component with markers for each pin
 */
const CustomMapView: React.FC<CustomMapViewProps> = ({
  pins,
  initialRegion = {
    latitude: 40.7128,
    longitude: -74.006,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
  showUserLocation = true,
  style,
  markerType = 'default',
  onMarkerPress,
}) => {
  const theme = useThemeStore();
  const mapRef = useRef<MapView>(null);
  const [userLocation, setUserLocation] = useState<{latitude: number; longitude: number} | null>(
    null,
  );
  // Using underscore prefix to indicate unused state variable
  const [_isAtUserLocation, setIsAtUserLocation] = useState(true);
  const [isLoaded, setIsLoaded] = useState(true);
  useEffect(() => {
    setTimeout(() => {
      setIsLoaded(false);
    }, 400);
  }, []);

  const [showLocationButton, setShowLocationButton] = useState(false);
  // Initialize visibleRegion with initialRegion to show pins immediately
  const [visibleRegion, setVisibleRegion] = useState<Region | null>(initialRegion);
  const [loadingLocation, setLoadingLocation] = useState(true);
  const [pinsLoaded, setPinsLoaded] = useState(false);

  const [pinId, setPinId] = useState('');

  // Track if component is mounted to prevent memory leaks
  const isMounted = useRef(true);

  // Get user's current location
  useEffect(() => {
    const fetchUserLocation = async () => {
      try {
        if (isAndroid) {
          await new Promise(resolve => setTimeout(resolve, 500)); // Android delay
        }

        const position = await getCurrentPosition();
        if (position && isMounted.current) {
          setUserLocation(position);
          setIsAtUserLocation(true);
          setShowLocationButton(false);
        }
      } catch (error) {
        console.error('Error getting user location:', error);
        if (isMounted.current) {
          setShowLocationButton(true);
        }
      } finally {
        if (isMounted.current) {
          setLoadingLocation(false);
        }
      }
    };

    if (showUserLocation) {
      fetchUserLocation();
    }

    // Cleanup function
    return () => {
      isMounted.current = false;
    };
  }, [showUserLocation]);

  // Cleanup function when component unmounts
  useEffect(() => {
    return () => {
      isMounted.current = false;
      setVisibleRegion(null);
      setUserLocation(null);
    };
  }, []);

  // Effect to handle pins loading
  useEffect(() => {
    if (pins && pins.length > 0 && isMounted.current) {
      // Small delay to ensure pins are processed properly
      const timer = setTimeout(() => {
        if (isMounted.current) {
          setPinsLoaded(true);
        }
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [pins]);

  const isMapAtUserLocation = useCallback(
    (region: Region): boolean => {
      if (!userLocation) return false;
      const latDiff = Math.abs(region.latitude - userLocation.latitude);
      const lngDiff = Math.abs(region.longitude - userLocation.longitude);
      return latDiff < 0.001 && lngDiff < 0.001;
    },
    [userLocation],
  );

  const handleRegionChange = useCallback(
    (region: Region) => {
      if (!isMounted.current) return;

      setVisibleRegion(region);

      if (userLocation) {
        const atUserLocation = isMapAtUserLocation(region);
        setIsAtUserLocation(atUserLocation);
        setShowLocationButton(!atUserLocation);
      }
    },
    [userLocation, isMapAtUserLocation],
  );

  const centerOnUserLocation = useCallback(async () => {
    if (userLocation && mapRef.current) {
      mapRef.current.animateToRegion(
        {
          latitude: userLocation.latitude,
          longitude: userLocation.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        },
        1000,
      );
      setIsAtUserLocation(true);
      setShowLocationButton(false);
    } else {
      try {
        const position = await getCurrentPosition();
        if (position && mapRef.current && isMounted.current) {
          setUserLocation(position);
          mapRef.current.animateToRegion(
            {
              latitude: position.latitude,
              longitude: position.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            },
            1000,
          );
          setIsAtUserLocation(true);
          setShowLocationButton(false);
        }
      } catch (error) {
        console.error('Error getting user location:', error);
      }
    }
  }, [userLocation]);

  // Optimizing visible pins based on the map's current region
  const isMarkerVisible = useCallback(
    (coordinate: {latitude: number; longitude: number}): boolean => {
      // Use visibleRegion if available, otherwise fall back to initialRegion
      const region = visibleRegion || initialRegion;

      const {latitude, longitude} = coordinate;
      const {latitude: centerLat, longitude: centerLng, latitudeDelta, longitudeDelta} = region;

      const latMin = centerLat - latitudeDelta / 2;
      const latMax = centerLat + latitudeDelta / 2;
      const lngMin = centerLng - longitudeDelta / 2;
      const lngMax = centerLng + longitudeDelta / 2;

      // Reduced padding to show fewer markers
      const latPadding = latitudeDelta * 0.1;
      const lngPadding = longitudeDelta * 0.1;

      return (
        latitude >= latMin - latPadding &&
        latitude <= latMax + latPadding &&
        longitude >= lngMin - lngPadding &&
        longitude <= lngMax + lngPadding
      );
    },
    [visibleRegion, initialRegion],
  );

  // Memoize the visible pins so they are only recalculated when necessary
  // Limit the number of visible pins to prevent memory issues
  const visiblePins = useMemo(() => {
    if (!pins) return [];

    // Use visibleRegion if available, otherwise fall back to initialRegion
    const region = visibleRegion || initialRegion;

    // If there are too many pins, use a more aggressive filtering strategy
    if (pins.length > MAX_VISIBLE_MARKERS * 2) {
      // First filter by visibility, then take only the closest ones to the center
      const centerLat = region.latitude;
      const centerLng = region.longitude;

      return (
        pins
          .filter(pin => isMarkerVisible(pin.coordinate))
          // Sort by distance to center (approximate calculation for better performance)
          .sort((a, b) => {
            const distA =
              Math.pow(a.coordinate.latitude - centerLat, 2) +
              Math.pow(a.coordinate.longitude - centerLng, 2);
            const distB =
              Math.pow(b.coordinate.latitude - centerLat, 2) +
              Math.pow(b.coordinate.longitude - centerLng, 2);
            return distA - distB;
          })
          .slice(0, MAX_VISIBLE_MARKERS)
      );
    }

    // For fewer pins, just filter by visibility
    const filtered = pins
      .filter(pin => isMarkerVisible(pin.coordinate))
      .slice(0, MAX_VISIBLE_MARKERS);

    return filtered;
  }, [pins, visibleRegion, initialRegion, isMarkerVisible]);

  return (
    <View style={[styles(theme).container, style]}>
      <MapView
        ref={mapRef}
        provider={PROVIDER_GOOGLE}
        style={styles(theme).mapStyle}
        initialRegion={initialRegion}
        customMapStyle={darkMapStyle}
        rotateEnabled={false}
        zoomControlEnabled={false}
        scrollEnabled={pinsLoaded} // Only enable scrolling after pins are loaded
        zoomEnabled={pinsLoaded} // Only enable zooming after pins are loaded
        toolbarEnabled={isAndroid ? false : undefined}
        moveOnMarkerPress={false}
        showsCompass={false}
        showsScale={false}
        pitchEnabled={false}
        followsUserLocation={false}
        showsMyLocationButton={isLoaded}
        showsUserLocation={showUserLocation && !loadingLocation} // Show location once fetched
        onRegionChangeComplete={handleRegionChange}
        onMapReady={() => {
          // Ensure map is ready for interaction
          if (isMounted.current && !pinsLoaded && pins.length > 0) {
            setPinsLoaded(true);
          }
        }}>
        {visiblePins.map(pin => {
          return (
            <Marker
              key={pin.id}
              coordinate={pin.coordinate}
              tracksViewChanges={false}
              onPress={e => {
                // Prevent default behavior that might show a callout
                e.stopPropagation();
                onMarkerPress?.(pin);
              }}
              // Important for performance
            >
              <CustomMarker
                key={pinId}
                type={
                  markerType === 'player'
                    ? 'player'
                    : (pin.type as 'available_kiosk' | 'planned_location')
                }
                pin={pin}
                onChange={id => {
                  setPinId(id);
                }}
              />
              {/* {markerType === 'player' && (
                <Callout tooltip={true}>
                  <View style={{width: Dimensions.get('window').width * 0.9}}>
                    <PlayerSchedulePlayCard
                      playerName={pin.title}
                      location={pin.description}
                      image={pin.image}
                      onPress={() => {
                        console.log('player pressed');
                      }}
                    />
                  </View>
                </Callout>
              )} */}
            </Marker>
          );
        })}
      </MapView>

      {/* Loading indicator while pins are loading */}
      {!pinsLoaded && pins.length > 0 && (
        <View style={styles(theme).loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.activeColor} />
        </View>
      )}

      {/* Show location button if needed */}
      {/* {showUserLocation && showLocationButton && !loadingLocation && (
        <TouchableOpacity
          style={styles(theme).centerButton}
          onPress={centerOnUserLocation}
          activeOpacity={0.8}>
          <Icon name="location" size={20} color="#666666" />
        </TouchableOpacity>
      )} */}
    </View>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    loadingContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
      zIndex: 10,
    },
    container: {
      flex: 1,
      position: 'relative',
      height: '100%',
      width: '100%',
    },
    mapStyle: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    centerButton: {
      position: 'absolute',
      top: height / 15,
      right: 12,
      backgroundColor: 'white',
      borderRadius: 8,
      width: 40,
      height: 40,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
      // zIndex: 999,
    },
  });

export default CustomMapView;
