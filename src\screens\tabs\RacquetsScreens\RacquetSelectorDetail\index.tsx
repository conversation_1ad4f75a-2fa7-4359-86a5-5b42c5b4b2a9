import React, {useRef} from 'react';
import {View, Dimensions, TouchableOpacity} from 'react-native';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {useThemeStore} from '@/store/themeStore';
import {CImage, Header, Icon, RatingBar, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import Carousel from 'react-native-reanimated-carousel';
import type {ICarouselInstance} from 'react-native-reanimated-carousel';
import {Images} from '@/config';
import BottomSheet, {BottomSheetFlatList, BottomSheetScrollView} from '@gorhom/bottom-sheet';
import GripSizeSelector from '@/components/GripSizeSelector';
import {CollapsibleViewWithIcon} from '@/components/CollapsibleView';
import {createStyles} from './styles';
import useTranslation from '@/hooks/useTranslation';
import {useConfigStore} from '@/store';
import {StackNavigationProp} from '@react-navigation/stack';

// Temporary fallback images until new assets are added
const tempRedRacquet1 = Images.racquet1;
const tempRedRacquet2 = Images.racquet2;
const tempRedRacquet3 = Images.racquet3;
const tempRedRacquet4 = Images.racquet4;
const tempRedRacquet5 = Images.racquet5;

const steps = [
  {
    id: 8,
    question: 'Results',
    racquet: {
      name: 'Babolat Pure Drive',
      image: Images.babolatPureDrive,
    },
  },
  {
    id: 1,
    question: 'Results',
    racquet: {
      name: 'Dunlop-racket',
      image: Images.babolatPureDrive,
    },
  },
];

const RacquetSelectorDetail = () => {
  const navigation = useNavigation<StackNavigationProp<any>>();
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const carouselRef = useRef<ICarouselInstance>(null);
  const bottomSheetRef = useRef<BottomSheet>(null);
  const racquetCarouselRef = useRef<ICarouselInstance>(null);

  const {getCartData, setCartData} = useConfigStore();
  const cartData = getCartData();
  const [currentIndex, setCurrentIndex] = React.useState(0);
  const {t} = useTranslation();
  const openFilterBottomSheet = () => {
    bottomSheetRef.current?.expand(); // Open to full height
  };

  const scrollRacquetCarousel = (direction: 'next' | 'prev') => {
    if (direction === 'next') {
      racquetCarouselRef.current?.next();
    } else {
      racquetCarouselRef.current?.prev();
    }
  };

  const renderItem = ({item}: {item: (typeof steps)[0]}) => {
    return (
      <View style={styles.racquetImageWrapper}>
        <CImage source={item.racquet.image} style={styles.racquetImage} resizeMode="contain" />
        <TouchableOpacity style={styles.plusCircle} onPress={openFilterBottomSheet}>
          <Icon name="add" size={60} color={theme.colors.activeColor} />
        </TouchableOpacity>
        <Typography variant="equipmentTitle" style={styles.racquetName}>
          {item?.racquet?.name}
        </Typography>
        <TouchableOpacity onPress={() => handleAddToCart(item)}>
          <Typography variant="tryNow" style={styles.tryNow}>
            {t('RacquetSelectorDetail.tryItNow')}
          </Typography>
        </TouchableOpacity>
      </View>
    );
  };

  const handleAddToCart = (cartItem: any) => {
    const existingCartData = getCartData() || [];
    const itemExists = existingCartData.some(item => item.id === cartItem.id);

    if (!itemExists) {
      // Add to cart (append don't replace)
      setCartData([...existingCartData, cartItem]);
    }
    navigation.navigate('CartScreen');
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        leftIcon={{
          name: 'side-menu',
          size: 24,
          color: theme.colors.white,
          containerStyle: {backgroundColor: theme.colors.primary},
        }}
        onLeftPress={() => navigation.dispatch(DrawerActions.openDrawer())}
        rightIcons={[
          {name: 'cart', size: 32, badge: cartData?.length || 0, color: theme.colors.activeColor},
        ]}
        pageTitle={t('RacquetSelectorDetail.RacquetSelectorDetail')}
        backgroundColor="transparent"
        isBackPress={true}
      />

      <Typography variant="question" style={styles.question}>
        {t('RacquetSelectorDetail.results')}
      </Typography>

      <Carousel
        ref={carouselRef}
        width={Dimensions.get('window').width}
        height={Dimensions.get('window').height - 240}
        data={steps}
        style={styles.carousel}
        onSnapToItem={setCurrentIndex}
        renderItem={renderItem}
        defaultIndex={0}
        snapEnabled={false}
        loop={false}
      />

      <View style={styles.pagination}>
        {steps?.map((_, idx) => (
          <View key={idx} style={idx === currentIndex ? styles.dotActive : styles.dot} />
        ))}
      </View>
      <BottomSheet
        ref={bottomSheetRef}
        snapPoints={['100%']}
        enableOverDrag={false}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        enablePanDownToClose={true}
        topInset={100}
        index={-1}
        backgroundStyle={{backgroundColor: theme.colors.background}}
        handleIndicatorStyle={{backgroundColor: theme.colors.bottomSheetBackground}}>
        <BottomSheetScrollView
          contentContainerStyle={styles.modalContainer}
          showsVerticalScrollIndicator={false}>
          <TouchableOpacity
            style={styles.tryNowContainer}
            onPress={() => handleAddToCart(steps[currentIndex])}>
            <Typography variant="tryNow" style={styles.tryNow}>
              {t('RacquetSelectorDetail.tryItNow')}
            </Typography>
            <Icon name="add" size={36} color={theme.colors.activeColor} />
          </TouchableOpacity>

          <View style={styles.carouselContainer}>
            <TouchableOpacity onPress={() => scrollRacquetCarousel('prev')}>
              <Icon name="Left-chevron" size={28} color={theme.colors.dimGray} />
            </TouchableOpacity>

            <Carousel
              ref={racquetCarouselRef}
              width={Dimensions.get('window').width - 100}
              height={320}
              data={[
                {image: tempRedRacquet1},
                {image: tempRedRacquet2},
                {image: tempRedRacquet3},
                {image: tempRedRacquet4},
                {image: tempRedRacquet5},
              ]}
              onConfigurePanGesture={panGesture => {
                'worklet';
                panGesture.activeOffsetX([-10, 10]); // Only trigger on deliberate horizontal swipe
                panGesture.failOffsetY([-5, 5]); // Pass vertical gestures up
              }}
              style={styles.racquetCarousel}
              loop={true}
              autoPlay={false}
              renderItem={({item}) => (
                <View style={styles.racquetSlide}>
                  <CImage source={item.image} style={styles.racketImage} resizeMode="contain" />
                </View>
              )}
            />

            <TouchableOpacity onPress={() => scrollRacquetCarousel('next')}>
              <Icon name="Right-chevron" size={28} color={theme.colors.dimGray} />
            </TouchableOpacity>
          </View>
          <View>
            <BottomSheetFlatList
              data={[
                {image: tempRedRacquet1},
                {image: tempRedRacquet2},
                {image: tempRedRacquet3},
                {image: tempRedRacquet4},
                {image: tempRedRacquet5},
              ]}
              horizontal
              contentContainerStyle={styles.indicatorContainer}
              renderItem={({item}) => (
                <TouchableOpacity
                  style={styles.indicatorItem}
                  onPress={() => {
                    // Find index of this item in carousel data and scroll to it
                    const index = [
                      tempRedRacquet1,
                      tempRedRacquet2,
                      tempRedRacquet3,
                      tempRedRacquet4,
                      tempRedRacquet5,
                    ].indexOf(item.image);
                    if (index !== -1) {
                      racquetCarouselRef.current?.scrollTo({index});
                    }
                  }}>
                  <CImage source={item.image} style={styles.indicatorIcon} resizeMode="contain" />
                </TouchableOpacity>
              )}
              keyExtractor={(_, index) => index.toString()}
            />
          </View>

          <View style={styles.contentContainer}>
            <View style={styles.titleContainer}>
              <Typography variant="tryNow" style={styles.centerText}>
                Dunlop
              </Typography>
              <Typography variant="sectionTitle" style={styles.centerText}>
                Dunlop CX 200 Tennis Racquet
              </Typography>
            </View>
            <GripSizeSelector />
            <View style={styles.divider} />
            <GripSizeSelector
              gripSizes={[
                {id: '1', size: 'Strung'},
                {id: '2', size: 'Unstrung'},
                {id: '3', size: 'Hybrid'},
              ]}
            />
            <View style={styles.divider} />
            <GripSizeSelector
              gripSizes={[
                {id: '1', size: '1'},
                {id: '2', size: '2'},
                {id: '3', size: '3'},
              ]}
            />
            <View style={styles.divider} />
            <CollapsibleViewWithIcon title="Specifications">
              <View>
                <Typography style={styles.listItem}>• Cross-platform development</Typography>
                <Typography style={styles.listItem}>• Faster development time</Typography>
                <Typography style={styles.listItem}>• Live reloading</Typography>
                <Typography style={styles.listItem}>• Community support and libraries</Typography>
                <Typography style={styles.listItem}>• Performance close to native apps</Typography>
              </View>
            </CollapsibleViewWithIcon>
            <View style={styles.divider} />

            <CollapsibleViewWithIcon title="Average Ratings">
              <View style={styles.ratingContainer}>
                <RatingBar value={4.5} onChange={() => {}} />
              </View>
            </CollapsibleViewWithIcon>
          </View>
        </BottomSheetScrollView>
      </BottomSheet>
    </SafeAreaView>
  );
};

export default RacquetSelectorDetail;
