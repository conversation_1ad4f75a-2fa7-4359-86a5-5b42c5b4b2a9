import React, {useRef, useState, useEffect} from 'react';
import {
  View,
  StyleSheet,
  ImageBackground,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Keyboard,
  ActivityIndicator,
} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {CButton, Header, Icon, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import {Images} from '@/config/images';
import CInput from '@/components/CInput';
import {useForm, Controller, SubmitHandler} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {useAuthStore} from '@/store';
import {useCheckDisplayName, useUpdateUserProfile, useUserDetails} from '@/hooks/queries/useUser';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {debounce, isBoolean, isNull} from 'lodash-es';
import {toaster} from '@/utils/commonFunctions';
import {useProfileUpload, useRandomName} from '@/hooks/queries/useUsers';
import {useQueryClient} from '@tanstack/react-query';
import ModalYearPicker from '@/components/ModalYearPicker';
import useTranslation from '@/hooks/useTranslation';
import LocationSearch from '@/components/GooglePlaceAutocomplete';
import UploadComponent from '@/components/Cupload/Index';
import CustomDropdown from '@/components/CustomDropdown/CustomDropdown';
import {brandOptions, courtArr, createProfileRadioData, pTennisData} from '@/config/staticData';
import {isArray, isString} from 'lodash';

// Form data type explicitly defined first
interface FormData {
  profileImage: string;
  fullName: string;
  email: string;
  birthYear: string;
  profession?: string;
  primarySport: Array<any>;
  location: string;
  myCourt: Array<any>;
  yearsPlaying: string;
  skillLevel: string;
  rating: string;
  favoriteBrand?: Array<any>;
  memberships?: string;
  affiliations?: string;
  randomName: string;
  locationCoordinates: {
    lat?: number;
    lng?: number;
  } | null;
}

// Stats data for profile
interface StatItem {
  value: string;
  label: string;
}

type RootStackParamList = {
  Notifications: undefined;
  ProfileQrScreen: undefined;
  // Add other screens as needed
};

type NavigationProp = StackNavigationProp<RootStackParamList>;

const EditProfileScreen = () => {
  const theme = useThemeStore();
  const navigation = useNavigation<NavigationProp>();

  const fullNameInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const birthYearInputRef = useRef<TextInput>(null);
  const professionInputRef = useRef<TextInput>(null);
  const primarySportInputRef = useRef<TextInput>(null);
  const locationInputRef = useRef<TextInput>(null);
  const myCourtInputRef = useRef<TextInput>(null);
  const yearsPlayingInputRef = useRef<TextInput>(null);
  const skillLevelInputRef = useRef<TextInput>(null);
  const favoriteBrandInputRef = useRef<TextInput>(null);
  const membershipsInputRef = useRef<TextInput>(null);
  const affiliationsInputRef = useRef<TextInput>(null);
  const [showYearModal, setShowYearModal] = useState(false);

  const randomNameApi = useRandomName();
  const queryClient = useQueryClient();
  const profileImageUpload = useProfileUpload();
  const cachedData = queryClient.getQueryData(['randomName']);

  const {t} = useTranslation();

  // Profile stats data
  const statsData: StatItem[] = [
    {value: '13', label: t('editProfileScreen.matches')},
    {value: '20', label: t('editProfileScreen.following')},
    {value: '12', label: t('editProfileScreen.followers')},
  ];

  const updateProfileMutation = useUpdateUserProfile();
  const userDetails = useUserDetails();
  const {user, login, isApiStatus} = useAuthStore();
  console.log('user=====>>>>>', user);

  const [displayName, setDisplayName] = useState('');
  const [ageStatus, setAgeStatus] = useState<string>('');
  const {data: nameCheck, isLoading: isCheckingName} = useCheckDisplayName(displayName);

  const fitnessOptions = createProfileRadioData?.describeFitnessOptions.map(item => ({
    label: t(item.label),
    value: item.value,
  }));

  // Form validation schema
  const schema = yup.object({
    fullName: yup.string().required(t('editProfileScreen.fullNameRequired')),
    email: yup
      .string()
      .email(t('editProfileScreen.invalidEmail'))
      .required(t('editProfileScreen.emailRequired')),
    birthYear: yup
      .string()
      .required(t('editProfileScreen.birthYearRequired'))
      .matches(/^[0-9]{4}$/, t('editProfileScreen.invalidBirthYear'))
      .test('is-valid-year', t('editProfileScreen.invalidBirthYear'), value => {
        const numValue = parseInt(value, 10);
        const currentYear = new Date().getFullYear();
        if (numValue > currentYear) {
          return false;
        }
        return !isNaN(numValue) && numValue >= currentYear - 60 && numValue <= currentYear;
      }),
    profession: yup
      .string()
      .nullable()
      .optional()
      .matches(/^[A-Za-z\s]*$/, t('editProfileScreen.invalidProfession')),
    primarySport: yup
      .array()
      .min(1, t('editProfileScreen.primarySportRequired'))
      .required(t('editProfileScreen.primarySportRequired')),
    location: yup.string().required(t('editProfileScreen.locationRequired')),
    myCourt: yup
      .array()
      .min(1, t('editProfileScreen.myCourtRequired'))
      .required(t('editProfileScreen.myCourtRequired')),
    yearsPlaying: yup
      .string()
      .required(t('editProfileScreen.yearsPlayingRequired'))
      .matches(/^[0-9]+$/, t('editProfileScreen.invalidYearsPlaying'))
      .test('is-positive', t('editProfileScreen.negativeYearsPlaying'), value => {
        const numValue = parseInt(value, 10);
        return !isNaN(numValue) && numValue > 0;
      }),
    skillLevel: yup.string().required(t('editProfileScreen.skillLevelRequired')),
    rating: yup
      .string()
      .required(t('editProfileScreen.ratingRequired'))
      .matches(/^\d+(\.\d+)?$/, t('editProfileScreen.ratingMustBeNumber'))
      .test('rating-range', t('editProfileScreen.ratingRange'), value => {
        const num = parseFloat(value);
        return num >= 1 && num <= 16.5;
      }),
    favoriteBrand: yup.array().optional(),
    memberships: yup.string().optional(),
    affiliations: yup.string().optional(),
    randomName: yup.string().optional(),
    locationCoordinates: yup
      .object()
      .shape({
        lat: yup.number().optional(),
        lng: yup.number().optional(),
      })
      .nullable()
      .default(null),
  });

  const {
    control,
    handleSubmit,
    formState: {errors},
    watch,
    setValue,
  } = useForm<FormData>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      profileImage: user?.profile_pic || '',
      fullName: user?.display_name || '',
      email: user?.email || '',
      birthYear: isNull(user?.birthyear) ? '' : String(user?.birthyear) || '',
      profession: user?.profession || '',
      primarySport: (isArray(user?.primary_sport_reference) && user?.primary_sport_reference) || [],
      location: user?.location || '',
      myCourt: (isArray(user?.court_name) && user?.court_name) || [],
      yearsPlaying: isNull(user?.years_playing) ? '' : String(user?.years_playing) || '',
      skillLevel: user?.fitness_level || '',
      rating: user?.rating || '',
      favoriteBrand: (isArray(user?.favorite_brand) && user?.favorite_brand) || [],
      memberships: user?.memberships || '',
      affiliations: user?.affiliations || '',
      randomName: '',
      locationCoordinates: null,
    },
  });

  // Get the current birth year value
  const birthYearValue = watch('birthYear');

  // Determine if user is adult or minor based on birth year
  useEffect(() => {
    if (birthYearValue && /^\d{4}$/.test(birthYearValue)) {
      const birthYear = parseInt(birthYearValue, 10);
      const currentYear = new Date().getFullYear();
      if (birthYear > currentYear) {
        setAgeStatus('');
      } else if (birthYear >= currentYear - 60 && birthYear <= currentYear) {
        const age = currentYear - birthYear;
        setAgeStatus(age >= 18 ? 'ADULT' : 'JUNIOR');
      } else {
        setAgeStatus('');
      }
    } else {
      setAgeStatus('');
    }
  }, [birthYearValue]);

  useEffect(() => {
    handleRandomName();
  }, []);

  const onSubmit: SubmitHandler<FormData> = data => {
    console.log('data=====>>>>>', data);
    const updateData = {
      display_name: data.fullName,
      birthyear: data.birthYear,
      profession: data.profession,
      primary_sport_reference: data.primarySport,
      location: data.location,
      court_name: data.myCourt,
      years_playing: data.yearsPlaying,
      fitness_level: data.skillLevel,
      rating: data?.rating,
      favorite_brand: data?.favoriteBrand,
      memberships: data?.memberships,
      affiliations: data.affiliations,
      location_lat: data?.locationCoordinates?.lat || user?.location_lat,
      location_long: data?.locationCoordinates?.lng || user?.location_long,
    };
    updateProfileMutation.mutate(updateData, {
      onSuccess: data => {
        // login(data.data);
        userDetails.mutate();
        navigation.goBack();
      },
      onError: error => {
        toaster('error', error.message || 'Failed to update profile', 'top');
      },
    });
  };

  const handleNavigate = () => {
    navigation.navigate('ProfileQrScreen');
  };

  const handleRandomName = () => {
    const {userType, fitnessLevel, birthYear} = control._formValues;
    const data =
      userType || fitnessLevel || birthYear
        ? {
            ...(userType && {user_type: userType}),
            ...(fitnessLevel && {fitness_level: fitnessLevel}),
            ...(birthYear && {birthyear: birthYear}),
          }
        : {};
    if (isApiStatus) {
      randomNameApi.mutate(data as any, {
        onSuccess: response => {
          if (!response?.status) {
            toaster('error', response.message, 'top');
          }
        },
        onError: error => {
          toaster('error', error.message, 'top');
        },
      });
    } else {
      toaster('success', 'Random name generation API called', 'top');
    }
  };

  const handleUpload = (imageObj: Array<any>) => {
    const files = imageObj.map(img => ({
      uri: img?.path,
      name: img?.filename,
      type: img?.mime,
      size: img?.size,
    }));

    if (isApiStatus) {
      profileImageUpload.mutate(
        {files},
        {
          onSuccess: response => {
            if (response?.status) {
              setValue('profileImage', response?.data?.profile_pic);
              userDetails.mutate();
              toaster('success', response.message, 'top');
            } else {
              toaster('error', response.message, 'top');
            }
          },
          onError: error => {
            toaster('error', error.message, 'top');
          },
        },
      );
    } else {
      toaster('success', 'Image upload api called', 'top');
    }
  };

  return (
    <ImageBackground source={Images.gradientBg} style={styles(theme).backgroundImage}>
      <SafeAreaView style={styles(theme).container}>
        <Header
          pageTitle={t('editProfileScreen.title')}
          showBack={false}
          leftComponent={
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Icon name="Left-chevron" size={24} color={theme.colors.white} />
            </TouchableOpacity>
          }
          rightComponent={
            <View style={styles(theme).rightComponentContainer}>
              <TouchableOpacity onPress={() => navigation.navigate('Notifications')}>
                <Icon name="notification" size={26} color={theme.colors.activeColor} />
              </TouchableOpacity>
              <TouchableOpacity>
                <Icon name="chat" size={26} color={theme.colors.activeColor} />
              </TouchableOpacity>
            </View>
          }
          backgroundColor="transparent"
        />
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles(theme).keyboardAvoidingView}>
          <ScrollView
            contentContainerStyle={styles(theme).scrollContainer}
            showsVerticalScrollIndicator={false}>
            {/* Profile Section */}
            <View style={styles(theme).profileSection}>
              {/* Profile Image */}
              <View style={styles(theme).profileImageContainer}>
                <Controller
                  control={control}
                  name="profileImage" // <- field name in form
                  render={({field: {value}, fieldState: {error}}) => {
                    return (
                      <UploadComponent
                        containerStyle={styles(theme).profileImage}
                        imageContentStyle={{width: '100%', height: '100%'}}
                        value={value}
                        onSelected={e => handleUpload(Array.isArray(e) ? e : [e])}
                        renderItem={
                          <Typography variant="tagTitle" color={theme.colors.black}>
                            {t('editProfileScreen.addPhoto')}
                          </Typography>
                        }
                      />
                    );
                  }}
                />
                <TouchableOpacity style={styles(theme).qrCodeContainer} onPress={handleNavigate}>
                  <Icon name="QR" size={30} color={theme.colors.black} />
                </TouchableOpacity>
              </View>
              <View style={styles(theme).nameContainer}>
                <View style={styles(theme).profileStatsContainer}>
                  <Typography variant="title" color={theme.colors.primary}>
                    {user?.name || 'Alex'}
                  </Typography>
                  <Typography
                    variant="bodyMedium"
                    color={theme.colors.primary}
                    style={styles(theme).emailText}>
                    {user?.email || '<EMAIL>'}
                  </Typography>
                </View>
                <View style={styles(theme).ballContainer}>
                  <Icon name="badge" size={26} color={theme.colors.activeColor} />
                </View>
              </View>

              {/* Profile Stats */}
              <View style={styles(theme).statsContainer}>
                {statsData.map((stat, index) => (
                  <React.Fragment key={stat.label}>
                    <View style={styles(theme).statItem}>
                      <Typography variant="subtitle2" color={theme.colors.white}>
                        {stat.value}
                      </Typography>
                      <Typography
                        variant="parkTitle"
                        color={theme.colors.primary}
                        style={styles(theme).statLabel}>
                        {stat.label}
                      </Typography>
                    </View>
                    {index < statsData.length - 1 && <View style={styles(theme).statDivider} />}
                  </React.Fragment>
                ))}
              </View>
            </View>

            {/* Form */}
            <View style={styles(theme).formContainer}>
              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="fullName"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.displayName')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.displayName')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={text => {
                        onChange(text);
                        const debouncedCheck = debounce((name: string) => {
                          setDisplayName(name);
                        }, 1000);
                        debouncedCheck(text);
                      }}
                      onBlur={onBlur}
                      hasError={
                        !!errors.fullName ||
                        (value.length > 0 && isBoolean(nameCheck?.status) && !nameCheck?.status)
                      }
                      error={
                        errors.fullName?.message ||
                        (value.length > 0 && isBoolean(nameCheck?.status) && !nameCheck?.status
                          ? nameCheck?.message
                          : '')
                      }
                      inputStyle={styles(theme).input}
                      ref={fullNameInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => emailInputRef.current?.focus()}
                      blurOnSubmit={false}
                      editable={!isCheckingName}
                      isLoading={isCheckingName}
                    />
                  )}
                />
              </View>

              {/* <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="email"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label="Email"
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder="Enter your email address"
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.email}
                      error={errors.email?.message}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      inputStyle={styles(theme).input}
                      ref={emailInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => birthYearInputRef.current?.focus()}
                      blurOnSubmit={false}
                      editable={false}
                    />
                  )}
                />
              </View> */}

              <View style={[styles(theme).inputContainer, {marginTop: -5, marginBottom: 25}]}>
                <View style={styles(theme).randomName}>
                  <Typography variant="bodyMedium" style={styles(theme).radioTitle}>
                    {t('editProfileScreen.randomName')}
                    {randomNameApi?.isPending && (
                      <ActivityIndicator
                        size={18}
                        style={{paddingLeft: 10}}
                        color={theme.colors.white}
                      />
                    )}
                  </Typography>
                  <TouchableOpacity
                    activeOpacity={0.7}
                    onPress={() => !randomNameApi?.isPending && handleRandomName()}>
                    <Ionicons name="reload" color={theme.colors.white} size={18} />
                  </TouchableOpacity>
                </View>
                <Controller
                  control={control}
                  name="randomName"
                  render={({field: {onChange, value}}) => (
                    <View style={[styles(theme).radioContainer]}>
                      {cachedData?.data?.map((option, index) => (
                        <CButton
                          containerStyle={styles(theme).fitnessBtn}
                          textStyle={styles(theme).fitnessText}
                          key={index}
                          title={option}
                          variant="primary"
                          onPress={() => {
                            Keyboard.dismiss();
                            setDisplayName(option);
                            setValue('fullName', option, {
                              shouldValidate: true,
                            });
                            onChange(option); // Call onChange to update the form value
                          }}
                        />
                      ))}
                    </View>
                  )}
                />
                {/* {errors.randomName && (
                  <Typography variant="body" style={styles(theme).errorText}>
                    {errors.randomName.message}
                  </Typography>
                )} */}
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="birthYear"
                  render={({field: {onChange, onBlur, value}}) => (
                    <View>
                      <TouchableOpacity activeOpacity={1} onPress={() => setShowYearModal(true)}>
                        <View pointerEvents="none">
                          <CInput
                            label={t('editProfileScreen.birthYear')}
                            showLabel={true}
                            labelStyle={styles(theme).label}
                            placeholder="YYYY"
                            placeholderTextColor={theme.colors.placeholder}
                            value={value}
                            onChangeText={text => {
                              onChange(text);
                              if (text.length === 4) {
                                const birthYear = parseInt(text, 10);
                                const currentYear = new Date().getFullYear();
                                if (birthYear > currentYear) {
                                  setAgeStatus('');
                                  setValue('birthYear', text, {
                                    shouldValidate: true,
                                  });
                                } else if (
                                  birthYear >= currentYear - 60 &&
                                  birthYear <= currentYear
                                ) {
                                  const age = currentYear - birthYear;
                                  setAgeStatus(age >= 18 ? 'ADULT' : 'JUNIOR');
                                  setValue('birthYear', text, {
                                    shouldValidate: true,
                                  });
                                } else {
                                  setAgeStatus('');
                                  setValue('birthYear', text, {
                                    shouldValidate: true,
                                  });
                                }
                              } else {
                                setAgeStatus('');
                                setValue('birthYear', text, {
                                  shouldValidate: true,
                                });
                              }
                            }}
                            onBlur={onBlur}
                            hasError={!!errors.birthYear}
                            error={errors.birthYear?.message}
                            keyboardType="numeric"
                            maxLength={4}
                            inputStyle={styles(theme).input}
                            containerStyle={{flex: 1}}
                            ref={birthYearInputRef}
                            returnKeyType="next"
                            onSubmitEditing={() => professionInputRef.current?.focus()}
                            blurOnSubmit={false}
                          />
                          {ageStatus && (
                            <View style={styles(theme).ageTag}>
                              <Typography variant="bodyMedium" style={styles(theme).ageTagText}>
                                {ageStatus}
                              </Typography>
                            </View>
                          )}
                        </View>
                      </TouchableOpacity>
                    </View>
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="profession"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.profession')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.professionPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value || ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      inputStyle={styles(theme).input}
                      ref={professionInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => primarySportInputRef.current?.focus()}
                      blurOnSubmit={false}
                      hasError={!!errors.profession}
                      error={errors.profession?.message}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="primarySport" // <- form field name
                  render={({field: {onChange, value}}) => (
                    <CustomDropdown
                      multiselect={true}
                      placeholder={t('editProfileScreen.sportPreference')}
                      label={t('editProfileScreen.primarySport')}
                      labelStyle={styles(theme).label}
                      mainStyles={[styles(theme).input, {marginBottom: 12}]}
                      sportsData={pTennisData}
                      value={value}
                      onChangeValue={onChange}
                      error={errors.primarySport?.message}
                      // containerStyle={styles.dropdownContainer}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="location"
                  render={({field: {onChange, value}, fieldState: {error}}) => (
                    <>
                      <LocationSearch
                        // handleLocationSelect={onChange}
                        handleLocationSelect={(location, coordinates) => {
                          onChange(location);
                          if (coordinates) {
                            setValue('locationCoordinates', coordinates);
                          }
                        }}
                        selectedLocation={value}
                        label={t('editProfileScreen.location')}
                        placeholder={t('editProfileScreen.locationPlaceholder')}
                      />
                      {error?.message && (
                        <Typography style={styles(theme).errorText}>{error.message}</Typography>
                      )}
                    </>
                    // <CInput
                    //   label={t('editProfileScreen.location')}
                    //   showLabel={true}
                    //   labelStyle={styles(theme).label}
                    //   placeholder={t('editProfileScreen.locationPlaceholder')}
                    //   placeholderTextColor={theme.colors.placeholder}
                    //   value={value}
                    //   onChangeText={onChange}
                    //   onBlur={onBlur}
                    //   hasError={!!errors.location}
                    //   error={errors.location?.message}
                    //   inputStyle={styles(theme).input}
                    //   ref={locationInputRef}
                    //   returnKeyType="next"
                    //   onSubmitEditing={() => myCourtInputRef.current?.focus()}
                    //   blurOnSubmit={false}
                    // />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="myCourt"
                  render={({field: {onChange, value}}) => (
                    <CustomDropdown
                      multiselect={true}
                      label={t('editProfileScreen.myCourt')}
                      placeholder={t('editProfileScreen.myCourtPlaceholder')}
                      labelStyle={styles(theme).label}
                      mainStyles={[styles(theme).input, {marginBottom: 12}]}
                      sportsData={courtArr}
                      value={value}
                      error={errors.myCourt?.message}
                      onChangeValue={onChange}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="yearsPlaying"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.yearsPlaying')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.yearsPlayingPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      maxLength={2}
                      hasError={!!errors.yearsPlaying}
                      error={errors.yearsPlaying?.message}
                      keyboardType="numeric"
                      inputStyle={styles(theme).input}
                      ref={yearsPlayingInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => skillLevelInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="skillLevel"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CustomDropdown
                      placeholder={t('editProfileScreen.skillLevelPlaceholder')}
                      label={t('editProfileScreen.skillLevel')}
                      labelStyle={styles(theme).label}
                      mainStyles={[styles(theme).input, {marginBottom: 12}]}
                      sportsData={fitnessOptions}
                      value={value}
                      error={errors.skillLevel?.message}
                      onChangeValue={onChange}
                      // containerStyle={styles.dropdownContainer}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="rating"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.rating')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.ratingPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      maxLength={4}
                      hasError={!!errors.rating}
                      error={errors.rating?.message}
                      keyboardType="numeric"
                      inputStyle={styles(theme).input}
                      ref={yearsPlayingInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => skillLevelInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="favoriteBrand"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CustomDropdown
                      multiselect={true}
                      label={t('editProfileScreen.favoriteBrand')}
                      placeholder={t('editProfileScreen.favoriteBrandPlaceholder')}
                      labelStyle={styles(theme).label}
                      mainStyles={[styles(theme).input, {marginBottom: 12}]}
                      sportsData={brandOptions}
                      value={value}
                      error={errors.favoriteBrand?.message}
                      onChangeValue={onChange}
                      // containerStyle={styles.dropdownContainer}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="memberships"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.memberships')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.membershipsPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value || ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      inputStyle={styles(theme).input}
                      ref={membershipsInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => affiliationsInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="affiliations"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.affiliations')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.affiliationsPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value || ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      inputStyle={styles(theme).input}
                      ref={affiliationsInputRef}
                      returnKeyType="done"
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <CButton
                title={t('editProfileScreen.completeProfile')}
                variant="primary"
                onPress={handleSubmit(onSubmit)}
                loading={updateProfileMutation.isPending}
                containerStyle={styles(theme).buttonContainer}
              />
            </View>
          </ScrollView>
          <ModalYearPicker
            visible={showYearModal}
            onClose={() => setShowYearModal(false)}
            title={t('editProfileScreen.selectYear')}
            initialYear={
              watch('birthYear') ? parseInt(watch('birthYear'), 10) : new Date().getFullYear()
            }
            onYearSelected={year => {
              setValue('birthYear', String(year), {
                shouldValidate: true,
              });
              // Update age status when year is selected
              const currentYear = new Date().getFullYear();
              if (year >= 1920 && year <= currentYear) {
                const age = currentYear - year;
                setAgeStatus(
                  age >= 18 ? t('editProfileScreen.adult') : t('editProfileScreen.junior'),
                );
              } else {
                setAgeStatus('');
              }
              setShowYearModal(false);
            }}
          />
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    container: {
      flex: 1,
      paddingHorizontal: 16,
    },
    nameContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 10,
      marginBottom: 30,
    },
    profileStatsContainer: {
      alignItems: 'center',
    },
    formContainer: {
      flex: 1,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingBottom: 40,
      paddingHorizontal: 16,
    },
    label: {
      color: theme.colors.white,
      fontSize: 18,
      fontWeight: '400',
    },
    profileSection: {
      alignItems: 'center',
      paddingTop: 5,
      paddingBottom: 20,
    },
    profileImageContainer: {
      position: 'relative',
      marginBottom: 16,
    },
    profileImage: {
      width: 142,
      height: 142,
      borderRadius: 100,
      backgroundColor: theme.colors.activeColor,
      justifyContent: 'center',
      alignItems: 'center',
    },

    fitnessBtn: {
      width: '48%',
      paddingHorizontal: 10,
      paddingVertical: 10,
    },

    fitnessText: {
      fontSize: theme.fontSize.medium,
      fontWeight: '400',
    },

    qrCodeContainer: {
      position: 'absolute',
      bottom: -10,
      right: -10,
      backgroundColor: theme.colors.white,
      borderRadius: 100,
      padding: 6,
      width: 56,
      height: 56,
      borderWidth: 3,
      borderColor: theme.colors.black,
      justifyContent: 'center',
      alignItems: 'center',
    },
    emailText: {
      marginTop: -10,
    },
    statsContainer: {
      flexDirection: 'row',
      backgroundColor: theme.colors.TranslucentWhite || 'rgba(255, 255, 255, 0.15)',
      borderRadius: 20,
      paddingVertical: 20,
    },
    statItem: {
      alignItems: 'center',
      flex: 1,
    },
    statDivider: {
      width: 1,
      height: '100%',
      backgroundColor: theme.colors.white,
    },
    inputContainer: {
      marginBottom: 10,
    },
    input: {
      backgroundColor: theme.colors.TranslucentWhite || 'rgba(255, 255, 255, 0.15)',
      color: theme.colors.white,
      borderWidth: 0,
      borderRadius: 18,
      height: 66,
      fontWeight: '400',
      fontSize: 18,
    },
    birthYearWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      position: 'relative',
    },
    ageTag: {
      position: 'absolute',
      right: 20,
      top: 51,
    },
    ageTagText: {
      color: theme.colors.white,
      fontWeight: '700',
      fontSize: 14,
    },
    ballContainer: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      right: -45,
      borderRadius: 100,
      padding: 6,
      justifyContent: 'center',
      alignItems: 'center',
    },
    rightComponentContainer: {
      flexDirection: 'row',
      gap: 20,
    },
    statLabel: {
      fontSize: 16,
    },
    buttonContainer: {
      marginTop: 20,
    },
    randomName: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    radioTitle: {
      color: theme.colors.text,
    },
    fitnessRadio: {
      width: 150,
    },
    radioContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginVertical: 10,
      gap: 10,
    },
    errorText: {
      color: theme.colors.coralRed,
      marginTop: 8,
      fontSize: theme.fontSize.small,
    },
  });

export default EditProfileScreen;
