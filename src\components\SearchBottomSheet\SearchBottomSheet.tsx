import React, {useState, useRef, useEffect, useMemo} from 'react';
import {StyleSheet, View, TouchableOpacity, Keyboard} from 'react-native';
import {ShoppingCartSection} from './ShoppingCartSection';
import {useThemeStore} from '@/store/themeStore';
import CreateGoRaqtProfile from '../createProfile/CreateGoRaqtProfile';
import Advertisement from '../Advertisement';
import Login from '../createProfile/login';
import Subscription from '../Subscription';
import ParkCard from '../ParkCard';
import Typography from '../Typography';
import ParksSearchScreen from '../ParksSearch';
import EquipmentReservation from '../EquipmentReservation';
import {Icon} from '@/components';
import InvitePlayers from '../InvitePlayers';
import BottomSheet, {BottomSheetScrollView, BottomSheetView} from '@gorhom/bottom-sheet';
import CustomDateTimePicker from '../CustomDateTimePicker/CustomDateTimePicker';
import {useConfigStore} from '@/store';
import useTranslation from '@/hooks/useTranslation';

const SearchBottomSheet = ({closePlayerScheduleCard}: {closePlayerScheduleCard: () => void}) => {
  const [showContent, setShowContent] = useState<string>('search_parks');
  const bottomSheetRef = useRef<BottomSheet>(null);
  const {t} = useTranslation();

  const [showInvitePlayers, setShowInvitePlayers] = useState<boolean>(false);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const snapPoints = useMemo(() => [130, 500, '100%'], []);

  const theme = useThemeStore();
  const {
    bookingStep,
    bookingData,
    updateBookingData,
    previousBookingStep,
    profileSetup,
    setProfileSetup,
    profileStep,
    setProfileStep,
  } = useConfigStore();
  console.log('🚀 ~ SearchBottomSheet ~ profileStep:', profileStep);
  const [isBottomSheetReady, setBottomSheetReady] = useState(false);

  const dynamicStyles = useMemo(() => createDynamicStyles(theme), [theme]);

  const isFullyExpanded = useMemo(() => {
    const lastSnapPoint = snapPoints[snapPoints.length - 1];
    const isLastIndex = currentIndex === snapPoints.length - 1;

    if (typeof lastSnapPoint === 'string') {
      const percentage = parseInt(lastSnapPoint, 10);
      return isLastIndex && percentage >= 50; // Only show close icon for heights >= 50%
    }

    return isLastIndex && snapPoints.length > 1; // For numeric values or single snap points
  }, [currentIndex, snapPoints]);

  // Effect to update the bottom sheet index when initialIndex changes
  // useEffect(() => {x
  // Sync local state with Redux state
  useEffect(() => {
    if (bookingStep) {
      setShowContent(bookingStep);
      // Reset invite players view when booking step changes
      setShowInvitePlayers(false);
    }
  }, [bookingStep]);

  useEffect(() => {
    if (profileStep === 'offer_compo' && bottomSheetRef.current && !profileSetup) {
      setTimeout(() => {
        bottomSheetRef.current?.snapToIndex(2);
      }, 800);
    }
  }, [profileStep]);
  console.log('profileStep=====>>>>>', profileStep);

  const handleBack = () => {
    previousBookingStep();
  };

  // Handle closing the bottom sheet
  const handleCloseBottomSheet = () => {
    bottomSheetRef.current?.snapToIndex(0);
    Keyboard.dismiss();
  };

  // Handle park selection with the Redux navigation
  const handleParkSelect = (parkData: object) => {
    updateBookingData({selectedPark: parkData}, 'next');
  };

  // Handle date updates in real-time
  const handleDateChange = (dates: {startDate: Date | null; endDate: Date | null}) => {
    // Only update dateTime in booking data, don't navigate
    if (dates.startDate) {
      updateBookingData({
        dateTime: {
          ...bookingData?.dateTime,
          startDate: dates.startDate,
          endDate: dates.endDate,
        },
      });
    }
  };

  // Handle time updates in real-time
  const handleTimeChange = (times: {startTime: string | null; endTime: string | null}) => {
    // Only update dateTime in booking data, don't navigate
    updateBookingData({
      dateTime: {
        ...bookingData?.dateTime,
        startTime: times.startTime,
        endTime: times.endTime,
      },
    });
  };

  // Handle final date time selection with navigation
  const handleDateTimeSelect = (dateTime: any) => {
    if (dateTime && dateTime.startDate instanceof Date) {
      // Update booking data and navigate to next step
      updateBookingData(
        {
          dateTime: {
            startDate: dateTime.startDate,
            endDate: dateTime.endDate,
            startTime: dateTime.startTime,
            endTime: dateTime.endTime,
          },
        },
        'next',
      );
    } else {
      console.error('Invalid date selection received:', dateTime);
    }
  };

  // Handle equipment selection
  const handleEquipmentSelect = (equipment: string[]) => {
    updateBookingData({equipment}, 'next');
  };

  // Handle player selection
  const handlePlayersSelect = (players: any[]) => {
    updateBookingData({players});
    setShowInvitePlayers(false);
  };

  // Handle Add Players button click
  const handleAddPlayersClick = () => {
    setShowInvitePlayers(true);
  };

  // Handle closing the invite players view
  const handleCloseInvitePlayers = () => {
    setShowInvitePlayers(false);
  };

  const CloseButton = () => (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={handleCloseBottomSheet}
      style={dynamicStyles.xButton}>
      <Icon name="close1-1" size={30} color={theme.colors.red} />
    </TouchableOpacity>
  );

  const renderDatePicker = () => {
    return (
      <BottomSheetScrollView
        style={dynamicStyles.datePickerView}
        contentContainerStyle={{paddingBottom: 40}}>
        <ParkCard
          parkData={bookingData?.selectedPark}
          onSelect={d => {
            console.log('Selected park data:', d);
          }}
          style={{marginTop: 20}}
        />
        <Typography variant="subtitle" color={theme.colors.white} style={dynamicStyles.titleStyle}>
          {t('datePicker.selectDateAndTime')}
        </Typography>
        <CustomDateTimePicker
          onConfirm={handleDateTimeSelect}
          onClose={handleBack}
          allowRangeSelection
          minDate={new Date()}
          title=""
          onAddPlayers={handleAddPlayersClick}
          selectedPlayers={bookingData?.players || []}
          initialStartDate={bookingData?.dateTime?.startDate || null}
          initialEndDate={bookingData?.dateTime?.endDate || null}
          initialStartTime={bookingData?.dateTime?.startTime || null}
          initialEndTime={bookingData?.dateTime?.endTime || null}
          onDateChange={handleDateChange}
          onTimeChange={handleTimeChange}
        />
      </BottomSheetScrollView>
    );
  };

  // Component rendering for the invite players screen
  const renderInvitePlayersScreen = () => {
    return (
      <BottomSheetScrollView style={dynamicStyles.datePickerView}>
        <View style={dynamicStyles.closeButtonContainer}>
          <TouchableOpacity
            style={[dynamicStyles.closeButton, dynamicStyles.backBtn]}
            onPress={handleCloseInvitePlayers}>
            <Icon name="back" size={20} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
        <InvitePlayers
          onConfirm={handlePlayersSelect}
          initialSelectedPlayers={bookingData?.players || []}
        />
      </BottomSheetScrollView>
    );
  };

  const renderContent = () => {
    if (!profileSetup) {
      if (profileStep === 'create_profile') {
        return (
          <BottomSheet
            backgroundStyle={{backgroundColor: theme.colors.background}}
            handleIndicatorStyle={{backgroundColor: theme.colors.bottomSheetBackground}}
            enableOverDrag={false}
            keyboardBlurBehavior="restore"
            ref={bottomSheetRef}
            keyboardBehavior="interactive"
            enablePanDownToClose={false}
            onChange={index => {
              if (index === -1) {
                bottomSheetRef.current?.snapToPosition(500);
              }
            }}>
            <CreateGoRaqtProfile
              completeProfile={() => {
                setProfileStep('claim_offer');
              }}
              setupLater={() => {
                setProfileStep('claim_offer');
              }}
            />
          </BottomSheet>
        );
      } else if (profileStep === 'claim_offer') {
        return (
          <BottomSheet
            backgroundStyle={{backgroundColor: theme.colors.background}}
            handleIndicatorStyle={{backgroundColor: theme.colors.bottomSheetBackground}}
            enableOverDrag={false}
            keyboardBlurBehavior="restore"
            ref={bottomSheetRef}
            snapPoints={['48%']}
            index={0}
            onChange={() => {}}>
            <BottomSheetView style={createDynamicStyles(theme).contentBottomContainer}>
              <Advertisement
                claimOffer={() => {
                  setProfileStep('offer_compo');
                }}
                skip={() => {
                  setProfileSetup(true);
                }}
              />
            </BottomSheetView>
          </BottomSheet>
        );
      } else if (profileStep === 'offer_compo') {
        return (
          <BottomSheet
            ref={bottomSheetRef}
            snapPoints={[137, 500, '100%']}
            enableOverDrag={false}
            // topInset={40}
            keyboardBehavior="interactive"
            keyboardBlurBehavior="restore"
            enablePanDownToClose={false} // Prevents unintended closing
            animateOnMount // Animate when first rendered
            backgroundStyle={{backgroundColor: theme.colors.background}}
            handleIndicatorStyle={{backgroundColor: theme.colors.bottomSheetBackground}}
            style={dynamicStyles.paddingHorizontal16}
            onChange={index => {
              setCurrentIndex(index);
              if (index === -1) {
                bottomSheetRef.current?.snapToIndex(0);
              }
            }}>
            {isFullyExpanded && <CloseButton />}
            {/* {isBottomSheetReady && ( */}
            <Subscription
              onAccept={() => {
                setProfileSetup(true);
              }}
            />
            {/* )} */}
          </BottomSheet>
        );
      } else if (profileStep === 'signup') {
        return (
          <BottomSheet
            backgroundStyle={{backgroundColor: theme.colors.background}}
            handleIndicatorStyle={{backgroundColor: theme.colors.bottomSheetBackground}}
            enableOverDrag={false}
            keyboardBlurBehavior="restore"
            ref={bottomSheetRef}
            onChange={() => {
              setBottomSheetReady(true);
            }}>
            <BottomSheetView style={createDynamicStyles(theme).contentBottomContainer}>
              {isBottomSheetReady && (
                <Login
                  completeProfile={() => {
                    setProfileStep('offer_compo');
                  }}
                  setupLater={() => {
                    setProfileStep('offer_compo');
                  }}
                />
              )}
            </BottomSheetView>
          </BottomSheet>
        );
      }
    } else {
      if (showContent === 'select_date_time' && showInvitePlayers) {
        return (
          <BottomSheet
            backgroundStyle={{backgroundColor: theme.colors.background}}
            handleIndicatorStyle={{backgroundColor: theme.colors.bottomSheetBackground}}
            enableOverDrag={false}
            keyboardBlurBehavior="restore"
            ref={bottomSheetRef}
            onChange={() => {
              setBottomSheetReady(true);
            }}>
            {/* <BottomSheetView style={createDynamicStyles(theme).contentBottomContainer}> */}
            {renderInvitePlayersScreen()}
            {/* </BottomSheetView> */}
          </BottomSheet>
        );
      } else if (showContent === 'cart_view') {
        return <ShoppingCartSection onBack={handleBack} />;
      } else if (showContent === 'search_parks') {
        return (
          <BottomSheet
            ref={bottomSheetRef}
            snapPoints={snapPoints} // Use the memoized snapPoints
            enableOverDrag={false}
            index={0}
            // topInset={40}
            keyboardBehavior="interactive"
            keyboardBlurBehavior="restore"
            enablePanDownToClose={false} // Prevents unintended closing
            animateOnMount // Animate when first rendered
            backgroundStyle={{backgroundColor: theme.colors.background}}
            handleIndicatorStyle={{backgroundColor: theme.colors.bottomSheetBackground}}
            style={dynamicStyles.paddingHorizontal16}
            onChange={index => {
              setCurrentIndex(index);
              if (index === -1) {
                bottomSheetRef.current?.snapToIndex(0);
              }
              closePlayerScheduleCard();
            }}>
            {isFullyExpanded && <CloseButton />}
            <ParksSearchScreen
              onParkSelect={handleParkSelect}
              displayHeader={!isFullyExpanded}
              onBackPress={handleBack}
            />
          </BottomSheet>
        );
      } else if (showContent === 'select_date_time') {
        return renderDatePicker();
      } else if (showContent === 'purchase_equipments') {
        return <EquipmentReservation onSubmit={handleEquipmentSelect} />;
      }
    }
  };

  return renderContent();
};

const createDynamicStyles = (theme: any) => {
  return StyleSheet.create({
    titleStyle: {
      marginVertical: 10,
      fontSize: theme.fontSize.xlarge,
      fontWeight: 'bold',
    },
    closeButton: {
      width: 40,
      height: 40,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      position: 'absolute',
      left: 20,
      top: 0,
      backgroundColor: theme.colors.white2,
    },
    closeButtonText: {
      color: theme.colors.Gainsboro,
      fontSize: 18,
      fontWeight: 'bold',
    },
    xButton: {
      marginBottom: 16,
      height: 30,
      width: 40,
    },
    datePickerView: {
      gap: 5,
      borderColor: theme.colors.red,
      flex: 1,
    },
    contentContainer: {
      flex: 1,
    },
    stepIndicator: {
      alignItems: 'center',
      padding: 10,
    },
    closeButtonContainer: {
      flexDirection: 'row',
      paddingBottom: 20,
    },
    backBtn: {left: 0, top: -20},
    contentBottomContainer: {
      flex: 1,
      padding: 20,
      alignItems: 'center',
    },
    paddingHorizontal16: {
      paddingHorizontal: 16,
    },
  });
};

export default SearchBottomSheet;
