import React, {useState} from 'react';
import {useThemeStore} from '@/store/themeStore';
import Typography from '@/components/Typography';
import {ScrollView, View, TouchableOpacity, Text, StyleSheet, ImageBackground} from 'react-native';
import {SubmitHand<PERSON>, useForm, Controller} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {useNavigation} from '@react-navigation/native';
import {DrawerNavigationProp} from '@react-navigation/drawer';
import {createStyles} from './styles';
import {CButton, CInput, CustomModal, Icon} from '@/components';
import PillLabel from '@/components/PillLabel';
import CustomToggleSwitch from '@/components/CustomToggleSwitch';
import GetCertified from '../GetCertified';
import useTranslation from '@/hooks/useTranslation';
import {useUserDetails} from '@/hooks/queries/useUser';
import {toaster} from '@/utils/commonFunctions';
import CToast from '@/components/CToast';
import UploadComponent from '@/components/Cupload/Index';
import {useProfileUpload} from '@/hooks/queries/useUsers';
import CustomDropdown from '@/components/CustomDropdown/CustomDropdown';
import {courtArr} from '@/config/staticData';
import {Images} from '@/config';
import CoachWrapper from '@/components/CoachWrapper';
import {useAuthStore} from '@/store';
import {useUpdateCoachProfile} from '@/hooks/queries/useCoach';

type RootDrawerParamList = {
  welcome: undefined;
  EditCoachProfile: undefined;
  CoachOptions: undefined;
  getCertified: {from: string};
};

type NavigationProp = DrawerNavigationProp<RootDrawerParamList>;

interface FormData {
  playerName: string;
  phoneNumber: string;
  email: string;
  description: string;
  isPrivate: boolean;
  sportsClub: string;
  rate: number;
  certifications: CertificateItem[];
  publicCourses: string[];
  videoCoaching: boolean;
  otherServices: string[];
  skillSets: string[];
  playerTypes: string[];
  profileImage: string;
}

interface ChipProps {
  label: string;
  bold?: boolean;
  onRightPress?: () => void;
  rightIconName?: string;
  style?: any;
}

interface CertificateItem {
  name: string;
  id: string;
}

interface SelectorProps {
  title: string;
  data: CertificateItem[];
  onAddPress: () => void;
  onMinusPress: (item: CertificateItem) => void;
}

const styles = StyleSheet.create({
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#666',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 8,
    alignSelf: 'flex-start',
  },
  label: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  rightIcon: {
    marginLeft: 6,
  },
  certificationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 16,
    marginBottom: 12,
    gap: 5,
    flex: 1,
  },
  certificates: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    width: '88%',
  },
  plusIcon: {
    marginVertical: 'auto',
    justifyContent: 'center',
    alignSelf: 'auto',
    alignContent: 'center',
    alignItems: 'center',
  },
});
const Chip: React.FC<ChipProps> = ({
  label,
  bold = false,
  onRightPress,
  rightIconName = '',
  style,
}) => (
  <View style={[styles.chip, style]}>
    <Text style={[styles.label]}>{label}</Text>
    {onRightPress && (
      <TouchableOpacity onPress={onRightPress} style={styles.rightIcon}>
        <Icon name={rightIconName} size={18} color="#fff" />
      </TouchableOpacity>
    )}
  </View>
);

const Selector: React.FC<SelectorProps> = ({title, data, onAddPress, onMinusPress}) => {
  const theme = useThemeStore();
  return (
    <View>
      <Typography variant="pushNotificationTitle" color={theme.colors.white}>
        {title}
      </Typography>
      <View style={styles.certificationRow}>
        <View style={styles.certificates}>
          {data?.map((item, index) => (
            <Chip
              key={item?.id}
              label={item?.name}
              onRightPress={() => onMinusPress(item)}
              rightIconName="line"
            />
          ))}
        </View>
        <TouchableOpacity style={styles.plusIcon} onPress={onAddPress}>
          <Icon name="plus" size={30} color={theme.colors.secondary} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const EditCoachProfile = (props: any) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const userDetails = useUserDetails();
  const {user} = useAuthStore();
  const navigation = useNavigation<NavigationProp>();
  const {t} = useTranslation();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedCertifications, setSelectedCertifications] = useState<CertificateItem[]>(
    user?.coachData?.certifications || [],
  );

  const otherServicesArray = [
    t('editCoachProfile.stringing'),
    t('editCoachProfile.gripping'),
    t('editCoachProfile.customization'),
  ];
  const skillSetArray = [
    t('editCoachProfile.doubles'),
    t('editCoachProfile.footwork'),
    t('editCoachProfile.funGames'),
    t('editCoachProfile.groupLessons'),
    t('editCoachProfile.mentalSkills'),
    t('editCoachProfile.privateLessons'),
    t('editCoachProfile.redOrange'),
    t('editCoachProfile.singles'),
    t('editCoachProfile.technicalFundamentals'),
  ];
  const playerTypesArray = [
    t('editCoachProfile.adult'),
    t('editCoachProfile.learning'),
    t('editCoachProfile.middleSchool'),
    t('editCoachProfile.preSchool'),
    t('editCoachProfile.redOrange'),
    t('editCoachProfile.socialPlayer'),
    t('editCoachProfile.youth'),
  ];

  const schema = yup.object({
    description: yup.string().required('Description is required'),
    certifications: yup.array().of(
      yup.object().shape({
        id: yup.string().required(),
        name: yup.string().required('Certifications is required'),
      }),
    ),
    isPrivate: yup.boolean(),
    sportsClub: yup.string().required('Sports club is required'),
    rate: yup.string().required('Rate is required'),
    publicCourses: yup.array().of(yup.string()).required('Public courts are required'),
    videoCoaching: yup.boolean().required('Video coaching is required'),
    otherServices: yup.array().of(yup.string()).required('Other services are required'),
    skillSets: yup.array().of(yup.string()).required('Skill sets are required'),
    playerTypes: yup.array().of(yup.string()).required('Player types are required'),
  });

  const {
    control,
    handleSubmit,
    formState: {errors},
    setValue,
  } = useForm<FormData>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      description: user?.coachData?.profile_description || '',
      certifications: selectedCertifications || [],
      isPrivate: user?.coachData?.is_private || false,
      publicCourses: user?.coachData?.public_courts?.split(',') || [],
      sportsClub: user?.coachData?.sports_clubs || '',
      videoCoaching: user?.coachData?.video_coaching || false,
      rate: user?.coachData?.rate || undefined,
      otherServices: user?.coachData?.other_services?.split(',') || [],
      skillSets: user?.coachData?.skills?.split(',') || [],
      playerTypes: user?.coachData?.player_types?.split(',') || [],
      profileImage: user?.coach_profile_pic || '',
    },
  });

  const onSubmit: SubmitHandler<FormData> = data => {
    const labelArray = data?.certifications?.map(item => item.id);

    const updateData = {
      profile_description: data?.description,
      certifications: labelArray,
      is_private: data?.isPrivate,
      public_courts: data?.publicCourses,
      sports_clubs: data?.sportsClub,
      video_coaching: data?.videoCoaching,
      rate: data?.rate,
      other_services: data?.otherServices,
      skills: data?.skillSets,
      player_types: data?.playerTypes,
    };

    // navigation.navigate('CoachOptions');

    updateCoachProfile.mutate(updateData, {
      onSuccess: data => {
        // // login(data.data);
        userDetails.mutate();
        // navigation.goBack();
        toaster('success', data.message, 'top');
      },
      onError: error => {
        toaster('error', error.message || 'Failed to update profile', 'top');
      },
    });
  };

  const handleCertificationsSelect = (data: any) => {
    setSelectedCertifications(data);
    setValue('certifications', data);
  };

  const updateCoachProfile = useUpdateCoachProfile();
  const profileImageUpload = useProfileUpload();

  const handleUpload = (imageObj: Array<any>, type?: string) => {
    const files = imageObj.map(img => ({
      uri: img?.path,
      name: img?.filename,
      type: img?.mime,
      size: img?.size,
    }));

    profileImageUpload.mutate(
      {files, type: 'coach'},
      {
        onSuccess: response => {
          if (response?.status) {
            setValue('profileImage', response?.data?.coach_profile_pic);
            userDetails.mutate();
            toaster('success', response.message, 'top');
          } else {
            toaster('error', response.message, 'top');
          }
        },
        onError: error => {
          toaster('error', error.message, 'top');
        },
      },
    );
  };

  return (
    <ImageBackground source={Images.gradientBg} style={styles.backgroundImage}>
      <CoachWrapper>
        <ScrollView contentContainerStyle={styles.container}>
          <TouchableOpacity activeOpacity={1}>
            {/* Header */}
            <View style={styles.headerRow}>
              <TouchableOpacity
                onPress={() => {
                  navigation.goBack();
                }}>
                <Icon name="Left-chevron" size={24} color={theme.colors.white} />
              </TouchableOpacity>
              <Typography variant="invitePlayersTitle" color={theme.colors.white}>
                {t('editCoachProfile.title')}
              </Typography>
            </View>
            {/* Profile Photo */}
            <View style={{alignItems: 'center', marginVertical: 20}}>
              <Controller
                control={control}
                name="profileImage"
                render={({field: {value}, fieldState: {error}}) => {
                  return (
                    <UploadComponent
                      containerStyle={styles.profileImage}
                      imageContentStyle={{width: '100%', height: '100%'}}
                      value={value}
                      onSelected={e => handleUpload(Array.isArray(e) ? e : [e])}
                      renderItem={
                        <Typography variant="tagTitle" color={theme.colors.black}>
                          {t('editProfileScreen.addPhoto')}
                        </Typography>
                      }
                    />
                  );
                }}
              />
            </View>
            {/* Profile Description */}
            <Typography variant="pushNotificationTitle" color={theme.colors.white}>
              {t('editCoachProfile.description')}
            </Typography>
            <Controller
              control={control}
              name="description"
              render={({field: {onChange, value, onBlur}}) => (
                <CInput
                  variant="light"
                  placeholder={t('editCoachProfile.descriptionPlaceholder')}
                  placeholderTextColorStyle={theme.colors.placeholder}
                  multiline={true}
                  numberOfLines={6}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  inputStyle={styles.textArea}
                  hasError={!!errors.description}
                  error={errors.description?.message}
                />
              )}
            />
            {/* Divider */}
            <View style={styles.divider} />
            {/* Certifications */}
            <Controller
              control={control}
              name="certifications"
              render={({field: {onChange, value}}) => (
                <Selector
                  title={t('editCoachProfile.certifications')}
                  data={value || []}
                  onAddPress={() => setIsModalVisible(true)}
                  onMinusPress={item => {
                    const newValue = value?.filter(cert => cert.id !== item.id);
                    onChange(newValue);
                    setSelectedCertifications(newValue);
                  }}
                />
              )}
            />
            {/* Divider */}
            <View style={styles.divider} />s{/* Private Switch */}
            <View style={styles.rowBetween}>
              <Typography variant="pushNotificationTitle" color={theme.colors.white}>
                {t('editCoachProfile.private')}
              </Typography>
              <Controller
                control={control}
                name="isPrivate"
                render={({field: {value, onChange}}) => (
                  <CustomToggleSwitch
                    isEnabled={value}
                    onToggle={onChange}
                    activeText="YES"
                    inactiveText="NO"
                    trackColor={{active: theme.colors.white, inactive: theme.colors.activeColor}}
                    thumbColor={{
                      active: theme.colors.activeColor,
                      inactive: theme.colors.activeColor,
                    }}
                    backgroundColorOn={theme.colors.white}
                    backgroundColorOff={theme.colors.dimGray}
                  />
                )}
              />
            </View>
            <View style={styles.divider} />
            {/* Locations */}
            <Typography variant="pushNotificationTitle" color={theme.colors.white}>
              {t('editCoachProfile.locationsAvailable')}
            </Typography>
            <Typography style={styles.subLabel}>{t('editCoachProfile.publicCourts')}</Typography>
            <Controller
              control={control}
              name="publicCourses"
              render={({field: {onChange, value}}) => (
                <CustomDropdown
                  multiselect={true}
                  placeholder={'Select courts'}
                  labelStyle={styles.label}
                  placeholderStyle={{color: theme.colors.placeholder}}
                  mainStyles={[styles.input, {marginBottom: 12}]}
                  sportsData={courtArr}
                  value={value}
                  error={errors.publicCourses?.message}
                  onChangeValue={onChange}
                />
              )}
            />
            <Typography style={styles.subLabel}>Sports Clubs</Typography>
            <Controller
              control={control}
              name="sportsClub"
              render={({field: {onChange, value, onBlur}}) => (
                <CInput
                  variant="light"
                  placeholder="Enter name"
                  placeholderTextColor={theme.colors.placeholder}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  inputStyle={styles.input}
                  hasError={!!errors.sportsClub}
                  error={errors.sportsClub?.message}
                />
              )}
            />
            <View style={styles.divider} />
            <Typography variant="pushNotificationTitle" color={theme.colors.white}>
              {t('editCoachProfile.videoCoaching')}
            </Typography>
            <Controller
              control={control}
              name="videoCoaching"
              render={({field: {onChange, value}}) => (
                <View style={styles.options}>
                  <PillLabel
                    label="Yes"
                    containerStyle={styles.pill1}
                    backgroundColor={value ? theme.colors.activeColor : theme.colors.dimGray}
                    textStyle={{
                      ...styles.pillText,
                      color: value ? theme.colors.black : theme.colors.white,
                    }}
                    onPress={() => onChange(true)}
                  />
                  <PillLabel
                    label="No"
                    containerStyle={styles.pill1}
                    backgroundColor={!value ? theme.colors.activeColor : theme.colors.dimGray}
                    textStyle={{
                      ...styles.pillText,
                      color: !value ? theme.colors.black : theme.colors.white,
                    }}
                    onPress={() => onChange(false)}
                  />
                </View>
              )}
            />
            <View style={styles.divider} />
            {/* Rate */}
            <Typography style={styles.label}>{t('editCoachProfile.rate')}</Typography>
            <View style={styles.rateRow}>
              <Controller
                control={control}
                name="rate"
                render={({field: {onChange, value, onBlur}}) => (
                  <CInput
                    placeholder="$75/hr"
                    leftText="$"
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    inputStyle={styles.rateInput}
                    hasError={!!errors.rate}
                    error={errors.rate?.message}
                  />
                )}
              />
            </View>
            <View style={styles.divider} />
            {/* Other Services */}
            <Typography style={styles.label}>{t('editCoachProfile.otherServices')}</Typography>
            <Controller
              control={control}
              name="otherServices"
              render={({field: {onChange, value}}) => (
                <View style={styles.options}>
                  {otherServicesArray?.map((label, index) => (
                    <PillLabel
                      key={index}
                      label={label}
                      containerStyle={styles.pill1}
                      textStyle={{
                        ...styles.pillText,
                        color: value?.includes(label) ? theme.colors.black : theme.colors.white,
                      }}
                      backgroundColor={
                        value?.includes(label) ? theme.colors.activeColor : theme.colors.dimGray
                      }
                      onPress={() => {
                        const newValue = value?.includes(label)
                          ? value.filter(item => item !== label)
                          : [...(value || []), label];
                        onChange(newValue);
                      }}
                    />
                  ))}
                </View>
              )}
            />
            <View style={styles.divider} />
            {/* Coaching Skill Sets */}
            <Typography style={styles.label}>{t('editCoachProfile.CoachingSkillsSets')}</Typography>
            <Controller
              control={control}
              name="skillSets"
              render={({field: {onChange, value}}) => (
                <View style={styles.options}>
                  {skillSetArray?.map((label, index) => (
                    <PillLabel
                      key={index}
                      label={label}
                      containerStyle={styles.pill1}
                      textStyle={{
                        ...styles.pillText,
                        color: value?.includes(label) ? theme.colors.black : theme.colors.white,
                      }}
                      backgroundColor={
                        value?.includes(label) ? theme.colors.activeColor : theme.colors.dimGray
                      }
                      onPress={() => {
                        const newValue = value?.includes(label)
                          ? value.filter(item => item !== label)
                          : [...(value || []), label];
                        onChange(newValue);
                      }}
                    />
                  ))}
                </View>
              )}
            />
            <View style={styles.divider} />
            {/* Types of Players */}
            <Typography style={styles.label}>{t('editCoachProfile.typesOfPlayers')}</Typography>
            <Controller
              control={control}
              name="playerTypes"
              render={({field: {onChange, value}}) => (
                <View style={styles.options}>
                  {playerTypesArray?.map((label, index) => (
                    <PillLabel
                      key={index}
                      label={label}
                      containerStyle={styles.pill1}
                      textStyle={{
                        ...styles.pillText,
                        color: value?.includes(label) ? theme.colors.black : theme.colors.white,
                      }}
                      backgroundColor={
                        value?.includes(label) ? theme.colors.activeColor : theme.colors.dimGray
                      }
                      onPress={() => {
                        const newValue = value?.includes(label)
                          ? value.filter(item => item !== label)
                          : [...(value || []), label];
                        onChange(newValue);
                      }}
                    />
                  ))}
                </View>
              )}
            />
            {/* Submit Button */}
            <CButton
              title={t('editCoachProfile.save')}
              onPress={handleSubmit(onSubmit)}
              containerStyle={styles.submitButton}
              textStyle={styles.submitButtonText}
              loading={updateCoachProfile.isPending}
            />
            <CustomModal
              animationType="slide"
              variant="bottom"
              visible={isModalVisible}
              onClose={() => {
                setIsModalVisible(false);
              }}
              modalContainerStyle={{height: '100%'}}
              showCloseButtonRight={true}
              imageBg={true}>
              <GetCertified
                onClose={data => {
                  setIsModalVisible(false);
                  handleCertificationsSelect(data);
                }}
                getCertifications={data => {
                  handleCertificationsSelect(data);
                }}
                selectedCertifications={selectedCertifications}
              />
              <CToast />
            </CustomModal>
          </TouchableOpacity>
        </ScrollView>
      </CoachWrapper>
    </ImageBackground>
  );
};

export default EditCoachProfile;
