Stack trace:
Frame         Function      Args
0007FFFF7F40  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF6E40) msys-2.0.dll+0x1FEBA
0007FFFF7F40  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF8218) msys-2.0.dll+0x67F9
0007FFFF7F40  000210046832 (000210285FF9, 0007FFFF7DF8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF7F40  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF7F40  0002100690B4 (0007FFFF7F50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF8220  00021006A49D (0007FFFF7F50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBD8B40000 ntdll.dll
7FFBD6EE0000 KERNEL32.DLL
7FFBD5D60000 KERNELBASE.dll
7FFBD6D10000 USER32.dll
7FFBD5D30000 win32u.dll
000210040000 msys-2.0.dll
7FFBD8860000 GDI32.dll
7FFBD6570000 gdi32full.dll
7FFBD66B0000 msvcp_win.dll
7FFBD6420000 ucrtbase.dll
7FFBD8090000 advapi32.dll
7FFBD8700000 msvcrt.dll
7FFBD6BB0000 sechost.dll
7FFBD8380000 RPCRT4.dll
7FFBD53B0000 CRYPTBASE.DLL
7FFBD5C90000 bcryptPrimitives.dll
7FFBD86B0000 IMM32.DLL
