import React, {useCallback, useMemo, memo} from 'react';
import {
  View,
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {useInfiniteQuery} from '@tanstack/react-query';
import Typography from '@/components/Typography';

// The height of each item - used for getItemLayout optimization
const ITEM_HEIGHT = 100;

// Define types for our data
interface ListItem {
  id: string;
  title: string;
  description: string;
  image: string;
  timestamp: number;
}

interface PageData {
  items: ListItem[];
  nextCursor: number | null;
  prevCursor: number | null;
}

// Mock API function to fetch paginated data
const fetchPage = async (pageParam: number): Promise<PageData> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  const PAGE_SIZE = 20;
  const start = (pageParam - 1) * PAGE_SIZE;
  const end = start + PAGE_SIZE;

  // Generate mock data
  const items = Array.from({length: PAGE_SIZE}, (_, index) => ({
    id: `item-${start + index}`,
    title: `Item ${start + index}`,
    description: `Description for item ${start + index}. This is a longer text to demonstrate multiline support.`,
    image: `https://picsum.photos/id/${((start + index) % 100) + 1}/200/200`,
    timestamp: Date.now() - (start + index) * 60000,
  }));

  return {
    items,
    nextCursor: end < 100 ? pageParam + 1 : null, // Limit to 100 items total
    prevCursor: pageParam > 1 ? pageParam - 1 : null,
  };
};

// Memoized list item component to prevent unnecessary re-renders
const ListItemComponent = memo(
  ({item, onPress}: {item: ListItem; onPress: (id: string) => void}) => {
    const theme = useThemeStore();

    // Handle item press with useCallback to prevent function recreation
    const handlePress = useCallback(() => {
      onPress(item.id);
    }, [item.id, onPress]);

    return (
      <TouchableOpacity
        style={[styles.itemContainer, {borderBottomColor: theme.colors.divider}]}
        onPress={handlePress}
        activeOpacity={0.7}>
        <Image source={{uri: item.image}} style={styles.itemImage} />
        <View style={styles.itemContent}>
          <Typography variant="subtitle" numberOfLines={1}>
            {item.title}
          </Typography>
          <Typography variant="body" numberOfLines={2} style={{color: theme.colors.textSecondary}}>
            {item.description}
          </Typography>
          <Typography variant="caption" style={{color: theme.colors.textTertiary}}>
            {new Date(item.timestamp).toLocaleDateString()}
          </Typography>
        </View>
      </TouchableOpacity>
    );
  },
  // Custom comparison function for memo
  (prevProps, nextProps) => {
    // Only re-render if the item id changes
    return prevProps.item.id === nextProps.item.id;
  },
);

// Optimized FlatList example component with useInfiniteQuery
const OptimizedInfiniteQueryFlatListExample = () => {
  const theme = useThemeStore();

  // Set up infinite query
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    refetch,
    isRefetching,
  } = useInfiniteQuery({
    queryKey: ['infiniteList'],
    queryFn: ({pageParam}) => fetchPage(pageParam),
    initialPageParam: 1,
    getNextPageParam: lastPage => lastPage.nextCursor,
    getPreviousPageParam: firstPage => firstPage.prevCursor,
  });

  // Flatten all pages into a single array of items
  const flattenedData = useMemo(() => {
    return data?.pages.flatMap(page => page.items) ?? [];
  }, [data]);

  // Memoized key extractor function
  const keyExtractor = useCallback((item: ListItem) => item.id, []);

  // Memoized getItemLayout function for fixed height items
  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: ITEM_HEIGHT,
      offset: ITEM_HEIGHT * index,
      index,
    }),
    [],
  );

  // Handle item press
  const handleItemPress = useCallback((id: string) => {
    console.log(`Item pressed: ${id}`);
    // Navigate or perform action here
  }, []);

  // Memoized renderItem function
  const renderItem = useCallback(
    ({item}: {item: ListItem}) => <ListItemComponent item={item} onPress={handleItemPress} />,
    [handleItemPress],
  );

  // Handle end reached - load more data
  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

  // Memoized footer component
  const ListFooterComponent = useMemo(() => {
    if (!isFetchingNextPage) return null;

    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
        <Typography variant="caption" style={{marginLeft: 8}}>
          Loading more items...
        </Typography>
      </View>
    );
  }, [isFetchingNextPage, theme.colors.primary]);

  // Memoized empty component
  const ListEmptyComponent = useMemo(() => {
    if (isLoading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      );
    }

    if (isError) {
      return (
        <View style={styles.emptyContainer}>
          <Typography variant="subtitle" align="center">
            Error loading items
          </Typography>
          <TouchableOpacity onPress={() => refetch()} style={styles.retryButton}>
            <Typography variant="button" style={{color: theme.colors.primary}}>
              Retry
            </Typography>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Typography variant="subtitle" align="center">
          No items found
        </Typography>
        <Typography variant="body" align="center" style={{marginTop: 8}}>
          Pull down to refresh
        </Typography>
      </View>
    );
  }, [isLoading, isError, refetch, theme.colors.primary]);

  return (
    <View style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <FlatList
        data={flattenedData}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        getItemLayout={getItemLayout}
        onEndReached={handleEndReached}
        onEndReachedThreshold={0.5}
        ListFooterComponent={ListFooterComponent}
        ListEmptyComponent={ListEmptyComponent}
        refreshing={isRefetching}
        onRefresh={refetch}
        // Performance optimization props
        removeClippedSubviews={Platform.OS === 'android'} // Use on Android, can cause issues on iOS
        maxToRenderPerBatch={10} // Default is 10
        updateCellsBatchingPeriod={50} // Default is 50ms
        initialNumToRender={10} // Render enough to fill the screen
        windowSize={21} // Default is 21 (10 viewports above, 10 below, 1 visible)
        // Style props
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  itemContainer: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    padding: 12,
    borderBottomWidth: 1,
    height: ITEM_HEIGHT,
  },
  itemImage: {
    width: 70,
    height: 70,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  itemContent: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'space-between',
  },
  footer: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  retryButton: {
    marginTop: 16,
    padding: 8,
  },
});

export default OptimizedInfiniteQueryFlatListExample;
