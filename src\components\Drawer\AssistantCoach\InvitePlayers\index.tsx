import React, {useRef} from 'react';
import {styles as createStyles} from './styles';
import {useThemeStore} from '@/store/themeStore';
import Typography from '@/components/Typography';
import {CButton, CInput} from '@/components';
import {KeyboardAvoidingView, Platform, ScrollView, TextInput, View} from 'react-native';
import {Controller, SubmitHandler, useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {DrawerNavigationProp} from '@react-navigation/drawer';
import useTranslation from '@/hooks/useTranslation';

type RootDrawerParamList = {
  welcome: undefined;
  InvitePlayers: undefined;
  CoachOptions: undefined;
  // ... other screens
};

interface FormData {
  playerName: string;
  phoneNumber: string;
  email: string;
}

const InvitePlayers = (props: any) => {
  const {onClose} = props;
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const {t} = useTranslation();

  const playerNameInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const phoneNumberInputRef = useRef<TextInput>(null);

  const schema = yup.object({
    playerName: yup.string().required('Full name is required'),
    phoneNumber: yup.string().required('Phone number is required'),
    email: yup.string().email('Email format is invalid').required('Email is required'),
  });

  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm<FormData>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      playerName: '',
      phoneNumber: '',
      email: '',
    },
  });

  const onSubmit: SubmitHandler<FormData> = data => {
    console.log('Form data:', data);
    onClose();
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardAvoidingView}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}>
        <Typography
          variant="invitePlayersTitle"
          color={theme.colors.white}
          align="center"
          style={{marginBottom: 20}}>
          {t('InvitePlayersAssistantCoach.title')}
        </Typography>
        <View style={styles.inputContainer}>
          <Controller
            control={control}
            name="playerName"
            render={({field: {onChange, onBlur, value}}) => (
              <CInput
                label={t('InvitePlayersAssistantCoach.playerName')}
                showLabel={true}
                variant="light"
                labelStyle={styles.label}
                // placeholderTextColor={theme.colors.placeholder}
                placeholder={t('InvitePlayersAssistantCoach.name')}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                hasError={!!errors.playerName}
                error={errors.playerName?.message}
                inputStyle={styles.input}
                ref={playerNameInputRef}
                returnKeyType="next"
                onSubmitEditing={() => phoneNumberInputRef.current?.focus()}
                blurOnSubmit={false}
                cursorColor={theme.colors.black}
              />
            )}
          />
        </View>

        <View style={styles.inputContainer}>
          <Controller
            control={control}
            name="phoneNumber"
            render={({field: {onChange, onBlur, value}}) => (
              <View style={styles.birthYearWrapper}>
                <CInput
                  label={t('InvitePlayersAssistantCoach.playerPhone')}
                  showLabel={true}
                  variant="light"
                  labelStyle={styles.label}
                  placeholder={t('InvitePlayersAssistantCoach.phone')}
                  // placeholderTextColor={theme.colors.placeholder}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  hasError={!!errors.phoneNumber}
                  error={errors.phoneNumber?.message}
                  keyboardType="numeric"
                  maxLength={10}
                  inputStyle={styles.input}
                  containerStyle={{flex: 1}}
                  ref={phoneNumberInputRef}
                  returnKeyType="next"
                  onSubmitEditing={() => emailInputRef.current?.focus()}
                  blurOnSubmit={false}
                  cursorColor={theme.colors.black}
                />
              </View>
            )}
          />
        </View>

        <View style={styles.inputContainer}>
          <Controller
            control={control}
            name="email"
            render={({field: {onChange, onBlur, value}}) => (
              <CInput
                label={t('InvitePlayersAssistantCoach.playerEmail')}
                showLabel={true}
                variant="light"
                labelStyle={styles.label}
                placeholder={t('InvitePlayersAssistantCoach.email')}
                // placeholderTextColor={theme.colors.placeholder}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                hasError={!!errors.email}
                error={errors.email?.message}
                keyboardType="email-address"
                autoCapitalize="none"
                inputStyle={styles.input}
                ref={emailInputRef}
                returnKeyType="done"
                blurOnSubmit={false}
                cursorColor={theme.colors.black}
                onSubmitEditing={handleSubmit(onSubmit)}
              />
            )}
          />
        </View>

        <View style={styles.btnMain}>
          <CButton
            title={t('common.next')}
            variant="primary"
            onPress={handleSubmit(onSubmit)}
            textStyle={styles.buttonText}
            containerStyle={styles.buttonContainer}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default InvitePlayers;
