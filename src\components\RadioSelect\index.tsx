// src/components/RadioSelect.tsx
import React from 'react';
import {View, TouchableOpacity, StyleSheet, ViewStyle, StyleProp, TextStyle} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import Typography from '../Typography';

interface RadioSelectProps {
  label: string;
  selected: boolean;
  onPress: () => void;
  containerStyle?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  multiSelect?: boolean; // New prop to enable multiple selection mode
}

/***
// Example usage of RadioSelect
const ExampleUsage = () => {
  const options = [
    { label: 'Dunlop ATP Official Tennis Balls', value: 'dunlop_atp' },
    { label: 'Dunlop AO Tennis Balls', value: 'dunlop_ao' },
    { label: 'Dunlop Stage 1 Green Tennis Balls', value: 'dunlop_stage1' },
  ];

  const [selectedValue, setSelectedValue] = React.useState<string | null>(null);

  return (
    <View>
      {options.map((option) => (
        <RadioSelect
          key={option.value}
          label={option.label}
          selected={selectedValue === option.value}
          onPress={() => setSelectedValue(option.value)}
        />
      ))}
    </View>
  );
};
*/

const RadioSelect: React.FC<RadioSelectProps> = ({
  label,
  selected,
  onPress,
  containerStyle,
  textStyle,
  multiSelect: _multiSelect = false, // Keep for API compatibility but don't use
}) => {
  const theme = useThemeStore();

  return (
    <TouchableOpacity
      style={[styles(theme).container, containerStyle]}
      onPress={onPress}
      activeOpacity={0.7}>
      <View
        style={[
          styles(theme).radioCircle, // Always use circular style
          {
            borderColor: theme.colors.text,
            backgroundColor: selected ? theme.colors.inputLabel : theme.colors.primary,
          },
        ]}
      />
      <Typography
        variant="body"
        style={[styles(theme).label, {color: theme.colors.text}, textStyle]}>
        {label}
      </Typography>
    </TouchableOpacity>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 1,
    },
    radioCircle: {
      height: 20,
      width: 20,
      borderRadius: 12,
      borderWidth: 2,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 10,
    },
    selectedInnerCircle: {
      height: 24,
      width: 24,
      borderRadius: 24,
      borderWidth: 1,
    },
    label: {
      fontSize: 16,
    },
  });

export default RadioSelect;
