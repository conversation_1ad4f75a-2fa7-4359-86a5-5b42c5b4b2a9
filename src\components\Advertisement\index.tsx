import React from 'react';
import {useThemeStore} from '@/store/themeStore';
import {View} from 'react-native';
import {styles as createStyles} from './styles';
import CButton from '../CButton';
import Typography from '../Typography';
import {Icon} from '@/components';
import useTranslation from '@/hooks/useTranslation';

/**
 * @category Interfaces
 * @typedef {Object} AdvertisementProps
 * @property {Function} skip - Function to be called when the user presses the "Skip" button.
 * @property {Function} claimOffer - Function to be called when the user presses the "Claim Offer" button
 */
interface AdvertisementProps {
  skip: () => void;
  claimOffer: () => void;
}

/**
 * @component
 * @category Components
 * @description
 * This component displays an advertisement of offers with two buttons:
 * "Skip" and "Claim Offer".
 *
 * @param {AdvertisementProps} props - The properties for the Advertisement component.
 * @returns {JSX.Element} The rendered Advertisement component.
 */
const Advertisement = (props: AdvertisementProps): JSX.Element => {
  const {skip = () => {}, claimOffer = () => {}} = props;
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const {t} = useTranslation();

  return (
    <View style={styles.root}>
      <View style={styles.imageContainer}>
        <Icon name="icon-W" size={100} color={theme.colors.crimsonRed} />
      </View>
      <Typography variant="subtitle2" align="center" style={styles.title}>
        {t('advertisement.title')}
      </Typography>
      <Typography variant="bodyMedium1" align="center" style={styles.subTitle}>
        {t('advertisement.subTitle')}
      </Typography>
      <View style={styles.buttonContainer}>
        <CButton
          title={t('advertisement.skipBtn')}
          variant="primary"
          onPress={() => {
            skip();
          }}
          containerStyle={[styles.flex1]}
          textStyle={styles.buttonTextPrimary}
        />

        <CButton
          title={t('advertisement.claimOfferBtn')}
          variant="primary"
          onPress={() => {
            claimOffer();
          }}
          containerStyle={styles.flex1}
          textStyle={styles.buttonTextPrimary}
        />
      </View>
    </View>
  );
};

export default Advertisement;
