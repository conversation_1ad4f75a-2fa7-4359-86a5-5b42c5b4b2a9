import React, {useState} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {View} from 'react-native';
import {CButton, CInput, Icon} from '@/components';
import {createStyles} from './styles';
import {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import Typography from '../Typography';
import useTranslation from '@/hooks/useTranslation';

/**
 * @category Interfaces
 * @typedef {Object} SubscriptionProps
 * @property {Function} onAccept - Callback which is invoked when user accept the offer.
 */

/**
 * @component
 * @category Components
 * @description Subscription component for displaying subscription offers with promo code functionality
 *
 * @return {JSX.Element} The rendered subscription component
 */

const Subscription = ({onAccept}: {onAccept: () => void}) => {
  const [promoCode, setPromoCode] = useState('');
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const {t} = useTranslation();

  return (
    <BottomSheetScrollView
      contentContainerStyle={styles.scrollViewContainer}
      showsVerticalScrollIndicator={false}>
      {/* Header with close button and pricing */}
      <View style={styles.headerContent}>
        <Typography variant="subtitle2" color={theme.colors.white} style={styles.trialText}>
          {t('subscription.trial')}
        </Typography>
        <Typography variant="subtitle2" color={theme.colors.white} style={styles.priceText}>
          {t('subscription.price')}
        </Typography>
      </View>

      {/* Promo message */}
      <View style={styles.promoMessageContainer}>
        <Typography variant="description" color={theme.colors.white}>
          {t('subscription.promoMessage')}
        </Typography>
      </View>
      <View style={styles.divider} />

      {/* Bullet points */}
      <View style={styles.bulletPointsContainer}>
        {[1, 2, 3].map(item => (
          <View key={item} style={styles.bulletPoint}>
            <Icon name="right" size={24} color={theme.colors.white} />
            <View style={styles.bulletTextContainer}>
              <Typography variant="subtitle" color={theme.colors.white}>
                {t('subscription.bulletTitle')}
              </Typography>
              <Typography variant="description" color={theme.colors.white}>
                {t('subscription.bulletSubtitle')}
              </Typography>
            </View>
          </View>
        ))}
      </View>

      {/* Promo Code Input */}
      <CInput
        placeholder={t('subscription.promoCodePlaceholder')}
        value={promoCode}
        onChangeText={setPromoCode}
        placeholderTextColorStyle={theme.colors.activeColor}
        useBottomSheetInput={true}
      />

      {/* Terms and Conditions */}
      <Typography variant="badgeText" color={theme.colors.white} style={styles.termsText}>
        {t('subscription.termsText')}
      </Typography>
      <Typography variant="body" color={theme.colors.activeColor} style={styles.termsLink}>
        {t('subscription.termsLink')}
      </Typography>

      {/* Accept Button */}
      <CButton title={t('subscription.acceptBtn')} onPress={onAccept} />
    </BottomSheetScrollView>
  );
};

export default Subscription;
