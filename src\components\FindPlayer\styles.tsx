import {FONT_SIZE} from '@/utils/fonts';
import {Platform, StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme?.colors?.background,
      gap: 4,
      padding: 20,
    },
    listContainer: {
      flex: 1,
      marginBottom: 16,
    },
    scrollView: {
      paddingBottom: 20,
      gap: 20,
    },
    titleContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 10,
      marginBottom: 10,
    },
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Platform.OS === 'ios' ? 12 : 2,
      backgroundColor: theme.colors.white,
      borderRadius: 8,
      borderColor: theme.colors.primary,
      borderWidth: 2,
      // height: 70,
    },
    searchInput: {
      flex: 1,
      color: theme.colors.primary,
      fontSize: 18,
      // minHeight: 70,
    },
    filterInput: {
      marginVertical: 12,
    },
    inviteBtn: {
      width: '100%',
      alignSelf: 'center',
      borderRadius: 28,
      backgroundColor: theme.colors.activeColor,
    },
    inviteBtnText: {
      color: theme.colors.background,
      fontSize: FONT_SIZE.xl,
      fontWeight: 'bold',
    },
    keyboardAvoidingView: {
      flex: 1,
      paddingTop: 10,
    },
    scrollContainer: {
      flexGrow: 1,
    },
    form: {
      paddingVertical: 10,
    },
    inputContainer: {
      marginBottom: 20,
    },
    input: {
      height: 47,
      paddingHorizontal: 16,
      borderRadius: 100,
      borderWidth: 1,
    },
    searchBar: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.white,
      borderRadius: 8,
      paddingHorizontal: 15,
      gap: 10,

      // height: 70,
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: 10,
      justifyContent: 'space-between',
      marginTop: 20,
    },
  });
