

<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> screens/auth/EmailSignupScreen/index.tsx</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="styles/style.css">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
             
                <a href="index.html">
                    <h1 class="navbar-item">GoRaqt Documentation</h1>
                </a>
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    Documentation
                </a>
                
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
                <div class="search-wrapper">
                    <input id="search" type="text" placeholder="Search docs..." class="input">
                </div>
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Global</h3><ul><li><a href="global.html#HIDE_TAB_SCREENS">HIDE_TAB_SCREENS</a></li><li><a href="global.html#SENSITIVE_FIELDS">SENSITIVE_FIELDS</a></li><li><a href="global.html#addBreadcrumb">addBreadcrumb</a></li><li><a href="global.html#apiCall">apiCall</a></li><li><a href="global.html#authenticateWithBiometrics">authenticateWithBiometrics</a></li><li><a href="global.html#calculateAge">calculateAge</a></li><li><a href="global.html#checkAuthStatusApi">checkAuthStatusApi</a></li><li><a href="global.html#checkLocationPermission">checkLocationPermission</a></li><li><a href="global.html#cleanMemoryIfNeeded">cleanMemoryIfNeeded</a></li><li><a href="global.html#clearAllData">clearAllData</a></li><li><a href="global.html#configureStatusBar">configureStatusBar</a></li><li><a href="global.html#createHealthService">createHealthService</a></li><li><a href="global.html#deleteFcmTokenFromDB">deleteFcmTokenFromDB</a></li><li><a href="global.html#deleteNotification">deleteNotification</a></li><li><a href="global.html#disableBiometrics">disableBiometrics</a></li><li><a href="global.html#enableBiometrics">enableBiometrics</a></li><li><a href="global.html#forgotPasswordApi">forgotPasswordApi</a></li><li><a href="global.html#formatBirthdate">formatBirthdate</a></li><li><a href="global.html#getAddressFromCoordinates">getAddressFromCoordinates</a></li><li><a href="global.html#getCurrentLocationWithAddress">getCurrentLocationWithAddress</a></li><li><a href="global.html#getCurrentPosition">getCurrentPosition</a></li><li><a href="global.html#getCurrentRegion">getCurrentRegion</a></li><li><a href="global.html#getCurrentUserApi">getCurrentUserApi</a></li><li><a href="global.html#getNotificationsList">getNotificationsList</a></li><li><a href="global.html#getTabBarVisibility">getTabBarVisibility</a></li><li><a href="global.html#getUnreadNotificationsCount">getUnreadNotificationsCount</a></li><li><a href="global.html#getUserDetails">getUserDetails</a></li><li><a href="global.html#getUserProfile">getUserProfile</a></li><li><a href="global.html#handleNotificationNavigation">handleNotificationNavigation</a></li><li><a href="global.html#handleTextureError">handleTextureError</a></li><li><a href="global.html#initSentry">initSentry</a></li><li><a href="global.html#isBiometricsAvailable">isBiometricsAvailable</a></li><li><a href="global.html#isBiometricsEnabled">isBiometricsEnabled</a></li><li><a href="global.html#lastCacheClearTime">lastCacheClearTime</a></li><li><a href="global.html#logError">logError</a></li><li><a href="global.html#logMessage">logMessage</a></li><li><a href="global.html#loginWithEmail">loginWithEmail</a></li><li><a href="global.html#loginWithEmailApi">loginWithEmailApi</a></li><li><a href="global.html#logoutApi">logoutApi</a></li><li><a href="global.html#markAllNotificationsAsRead">markAllNotificationsAsRead</a></li><li><a href="global.html#markNotificationAsRead">markNotificationAsRead</a></li><li><a href="global.html#navigateBackFromExample">navigateBackFromExample</a></li><li><a href="global.html#navigateToOptimizedFlatListExample">navigateToOptimizedFlatListExample</a></li><li><a href="global.html#otpResendApi">otpResendApi</a></li><li><a href="global.html#otpVerifyApi">otpVerifyApi</a></li><li><a href="global.html#prefetchInitialQueries">prefetchInitialQueries</a></li><li><a href="global.html#prefetchPost">prefetchPost</a></li><li><a href="global.html#prefetchUser">prefetchUser</a></li><li><a href="global.html#refreshTokenApi">refreshTokenApi</a></li><li><a href="global.html#registerWithEmail">registerWithEmail</a></li><li><a href="global.html#registerWithEmailApi">registerWithEmailApi</a></li><li><a href="global.html#requestLocationPermission">requestLocationPermission</a></li><li><a href="global.html#resetPasswordApi">resetPasswordApi</a></li><li><a href="global.html#saveFcmTokenInDB">saveFcmTokenInDB</a></li><li><a href="global.html#schema">schema</a></li><li><a href="global.html#scrubObject">scrubObject</a></li><li><a href="global.html#scrubSensitiveData">scrubSensitiveData</a></li><li><a href="global.html#setCustomStatusBar">setCustomStatusBar</a></li><li><a href="global.html#setDarkStatusBar">setDarkStatusBar</a></li><li><a href="global.html#setLightStatusBar">setLightStatusBar</a></li><li><a href="global.html#setNavigationContext">setNavigationContext</a></li><li><a href="global.html#setStatusBarHidden">setStatusBarHidden</a></li><li><a href="global.html#setUserContext">setUserContext</a></li><li><a href="global.html#setupMemoryCleaning">setupMemoryCleaning</a></li><li><a href="global.html#setupTextureErrorHandlers">setupTextureErrorHandlers</a></li><li><a href="global.html#signInWithFacebook">signInWithFacebook</a></li><li><a href="global.html#signInWithGoogle">signInWithGoogle</a></li><li><a href="global.html#startTransaction">startTransaction</a></li><li><a href="global.html#styles">styles</a></li><li><a href="global.html#updateUserProfile">updateUserProfile</a></li><li><a href="global.html#useAuthStatus">useAuthStatus</a></li><li><a href="global.html#useCurrentUser">useCurrentUser</a></li><li><a href="global.html#useLogin">useLogin</a></li><li><a href="global.html#useRegister">useRegister</a></li><li><a href="global.html#useSafeArea">useSafeArea</a></li><li><a href="global.html#useSocialLogin">useSocialLogin</a></li><li><a href="global.html#useUpdateUserProfile">useUpdateUserProfile</a></li><li><a href="global.html#useUserProfile">useUserProfile</a></li></ul></div><div class="category"><h2>Components</h2><h3>Components</h3><ul><li><a href="Advertisement.html">Advertisement</a></li><li><a href="BiometricGate.html">BiometricGate</a></li><li><a href="CButton.html">CButton</a></li><li><a href="CreateGoRaqtProfile.html">CreateGoRaqtProfile</a></li><li><a href="CustomMapView.html">CustomMapView</a></li><li><a href="CustomMarker.html">CustomMarker</a></li><li><a href="NotificationCard.html">NotificationCard</a></li><li><a href="OfferBanner.html">OfferBanner</a></li><li><a href="Subscription.html">Subscription</a></li></ul></div><div class="category"><h2>Interfaces</h2><h3>Interfaces</h3><ul><li><a href="AdvertisementProps.html">AdvertisementProps</a></li><li><a href="ApiResponse.html">ApiResponse</a></li><li><a href="BiometricGateProps.html">BiometricGateProps</a></li><li><a href="CButtonProps.html">CButtonProps</a></li><li><a href="CreateProfileProps.html">CreateProfileProps</a></li><li><a href="CustomMarkerProps.html">CustomMarkerProps</a></li><li><a href="DrawerItemType.html">DrawerItemType</a></li><li><a href="ForgotPasswordScreenProps.html">ForgotPasswordScreenProps</a></li><li><a href="Notification.html">Notification</a></li><li><a href="NotificationCardProps.html">NotificationCardProps</a></li><li><a href="PaginationState.html">PaginationState</a></li><li><a href="PermissionsScreenProps.html">PermissionsScreenProps</a></li><li><a href="RouteParams.html">RouteParams</a></li><li><a href="SentryNavigationContainerProps.html">SentryNavigationContainerProps</a></li><li><a href="SocialRegisterRequest.html">SocialRegisterRequest</a></li><li><a href="SplashScreenProps.html">SplashScreenProps</a></li><li><a href="TermsOfServiceScreenProps.html">TermsOfServiceScreenProps</a></li><li><a href="UseLogoutReturn.html">UseLogoutReturn</a></li></ul><h3><a href="global.html">Global</a></h3></div><div class="category"><h2>Navigation</h2><h3>Components</h3><ul><li><a href="DrawerNavigator.html">DrawerNavigator</a></li><li><a href="SentryNavigationContainer.html">SentryNavigationContainer</a></li></ul></div><div class="category"><h2>Screens</h2><h3>Components</h3><ul><li><a href="CommunityDetails.html">CommunityDetails</a></li><li><a href="DirectFetchTest.html">DirectFetchTest</a></li><li><a href="EmailSignupScreen.html">EmailSignupScreen</a></li><li><a href="ForgotPasswordScreen.html">ForgotPasswordScreen</a></li><li><a href="LoginScreen.html">LoginScreen</a></li><li><a href="MyMatchesScreen.html">MyMatchesScreen</a></li><li><a href="NotificationsListScreen.html">NotificationsListScreen</a></li><li><a href="PermissionsScreen.html">PermissionsScreen</a></li><li><a href="SignupScreen.html">SignupScreen</a></li><li><a href="SplashScreen.html">SplashScreen</a></li><li><a href="TermsOfServiceScreen.html">TermsOfServiceScreen</a></li><li><a href="Verification.html">Verification</a></li></ul></div><div class="category"><h2>Utils</h2><h3>Global</h3><ul><li><a href="global.html#api">api</a></li><li><a href="global.html#socialLoginApi">socialLoginApi</a></li><li><a href="global.html#useLogout">useLogout</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Source</p>
                    <h1>screens/auth/EmailSignupScreen/index.tsx</h1>
                </header>
                



    
    <section>
        <article>
            <pre class="prettyprint source linenums"><code>import React, {useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation/index';
import {useThemeStore} from '@/store/themeStore';
import {getGlobalStyles} from '@utils/styleUtils';
import {useForm, Controller} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {CInput, CButton, Icon, SafeAreaView} from '@components/index';
import {createStyles} from './styles';
import Typography from '@/components/Typography';
import {useAuthStore} from '@/store';
import {useRegister} from '@/hooks/queries/useAuth';
import {toaster, validatePassword} from '@/utils/commonFunctions';
import useTranslation from '@/hooks/useTranslation';
import {logEvent, setUserProperties} from '@/utils/GA';
type EmailSignupScreenNavigationProp = StackNavigationProp&lt;RootStackParamList, 'EmailSignup'>;

// Form validation schema
const schema = yup.object({
  fullName: yup.string().required('Full name is required'),
  email: yup.string().email('Email format is invalid').required('Email is required'),
  password: yup
    .string()
    .required('Password is required')
    .test(
      'password-validation',
      'Password must have at least 8 characters, including uppercase, lowercase, a number, and a symbol',
      value => !!value &amp;&amp; validatePassword(value).isValid,
    ),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('password')], 'Passwords must match')
    .required('Please confirm your password'),
});

// Form data type
type FormData = yup.InferType&lt;typeof schema>;

/**
 * @component
 * @category Screens
 *
 * @description  A comprehensive email registration screen that provides a complete signup flow
 * with form validation, password strength checking, and seamless navigation between
 * input fields. Features analytics tracking and error handling.
 *
 * @see {@link Sign-in} - Used to display sign-in link and navigate user to sign-in screen.
 *
 * @return {JSX.Element} Email Signup screen
 */
const EmailSignupScreen = () => {
  const navigation = useNavigation&lt;EmailSignupScreenNavigationProp>();
  const theme = useThemeStore();
  const globalStyles = getGlobalStyles({theme});
  const styles = createStyles(theme);
  const {isApiStatus} = useAuthStore();
  // Use React Query for registration
  const registerMutation = useRegister();
  const {t} = useTranslation();

  // Add refs for input fields
  const fullNameInputRef = useRef&lt;TextInput>(null);
  const emailInputRef = useRef&lt;TextInput>(null);
  const passwordInputRef = useRef&lt;TextInput>(null);
  const confirmPasswordInputRef = useRef&lt;TextInput>(null);

  // Initialize form with react-hook-form
  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm&lt;FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      fullName: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const handleBack = () => {
    navigation.goBack();
  };

  /**
   * @function onSubmit
   * @memberof EmailSignupScreen
   * @description
   * Processes the validated form data, tracks user events, and handles
   * different signup scenarios including existing users and new registrations.
   * Routes users to appropriate screens based on registration result.
   * @param {FormData} data - Form data containing fullname, email password and confirm password.
   */
  const onSubmit = (data: FormData) => {
    setUserProperties('email', data.email);
    logEvent('email_signup_button_clicked', {
      email: data.email,
      full_name: data.fullName,
      method: 'Goraqt',
    });
    // Form is valid, proceed with signup using React Query
    if (isApiStatus) {
      registerMutation.mutate(
        {
          email: data.email,
          password: data.password,
          fullName: data.fullName,
        },
        {
          onSuccess: response => {
            if (response.status) {
              if (response.data?.user_exist) {
                navigation.replace('Login', {email: data.email});
                logEvent('email_signup_with_existing_user', {
                  email: data.email,
                  full_name: data.fullName,
                  method: 'Goraqt',
                });
              } else {
                navigation.navigate('Verification', {
                  email: data.email,
                  from: 'signup',
                });
                logEvent('email_signup_with_new_user', {
                  email: data.email,
                  full_name: data.fullName,
                  method: 'Goraqt',
                });
              }
              toaster('success', response.message, 'top');
            } else {
              logEvent('email_signup_failed', {
                email: data.email,
                full_name: data.fullName,
                method: 'Goraqt',
              });
              toaster('error', response.message, 'top');
            }
          },
          onError: error => {
            logEvent('email_signup_failed', {
              email: data.email,
              full_name: data.fullName,
              method: 'Goraqt',
            });
            toaster('error', error.message, 'top');
          },
        },
      );
    } else {
      navigation.navigate('Verification', {
        email: data.email,
      });
    }
  };

  const handleSignIn = () => {
    const email = control._formValues.email;
    navigation.replace('Login', {email});
  };

  return (
    &lt;SafeAreaView includeTop={true} style={[styles.container]}>
      &lt;KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}>
        &lt;ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled">
          &lt;View style={styles.headerContainer}>
            &lt;TouchableOpacity
              onPress={handleBack}
              style={styles.backButton}
              activeOpacity={0.7}
              hitSlop={{top: 10, right: 10, bottom: 10, left: 10}}>
              &lt;Icon name="Left-chevron" size={22} color={theme.colors.gray} />
            &lt;/TouchableOpacity>
            &lt;Typography variant="subtitle" style={globalStyles.title}>
              {t('emailSignupScreen.signUp')}
            &lt;/Typography>
          &lt;/View>

          &lt;View style={styles.form}>
            &lt;View style={styles.flex}>
              &lt;View style={styles.inputContainer}>
                &lt;Controller
                  control={control}
                  name="fullName"
                  render={({field: {onChange, onBlur, value}}) => (
                    &lt;CInput
                      label={t('emailSignupScreen.fullName')}
                      showLabel={true}
                      variant="dark"
                      placeholder={t('emailSignupScreen.fullNamePlaceholder')}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.fullName}
                      error={errors.fullName?.message}
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={fullNameInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => emailInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              &lt;/View>

              &lt;View style={styles.inputContainer}>
                &lt;Controller
                  control={control}
                  name="email"
                  render={({field: {onChange, onBlur, value}}) => (
                    &lt;CInput
                      label={t('emailSignupScreen.email')}
                      showLabel={true}
                      variant="dark"
                      placeholder={t('emailSignupScreen.emailPlaceholder')}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.email}
                      error={errors.email?.message}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={emailInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => passwordInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              &lt;/View>

              &lt;View style={styles.inputContainer}>
                &lt;Controller
                  control={control}
                  name="password"
                  render={({field: {onChange, onBlur, value}}) => {
                    const validation = validatePassword(value);

                    const errorMessage =
                      errors.password?.message ||
                      (value.length > 0 &amp;&amp; !validation.isValid ? validation.message : '');

                    return (
                      &lt;CInput
                        label={t('emailSignupScreen.password')}
                        showLabel={true}
                        variant="dark"
                        placeholder={t('emailSignupScreen.passwordPlaceholder')}
                        value={value}
                        onChangeText={onChange}
                        onBlur={onBlur}
                        hasError={!!errors.password || (value.length > 0 &amp;&amp; !validation.isValid)}
                        error={errorMessage}
                        secureTextEntry
                        inputStyle={styles.input}
                        containerStyle={{marginBottom: 0}}
                        ref={passwordInputRef}
                        returnKeyType="next"
                        onSubmitEditing={() => confirmPasswordInputRef.current?.focus()}
                        blurOnSubmit={false}
                      />
                    );
                  }}
                />
              &lt;/View>

              &lt;View style={styles.inputContainer}>
                &lt;Controller
                  control={control}
                  name="confirmPassword"
                  render={({field: {onChange, onBlur, value}}) => (
                    &lt;CInput
                      label={t('emailSignupScreen.confirmPassword')}
                      showLabel={true}
                      variant="dark"
                      placeholder={t('emailSignupScreen.confirmPasswordPlaceholder')}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.confirmPassword}
                      error={errors.confirmPassword?.message}
                      secureTextEntry
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={confirmPasswordInputRef}
                      returnKeyType="done"
                      onSubmitEditing={handleSubmit(onSubmit)}
                      blurOnSubmit={false}
                    />
                  )}
                />
              &lt;/View>
            &lt;/View>

            &lt;CButton
              title={t('emailSignupScreen.submit')}
              onPress={handleSubmit(onSubmit)}
              loading={registerMutation.isPending}
              isDisabled={registerMutation.isPending}
            />

            &lt;View style={styles.signInContainer}>
              &lt;Text style={styles.signInText}>
                Already signed up?{' '}
                &lt;Text style={styles.signInLink} onPress={handleSignIn}>
                  {t('emailSignupScreen.signIn')}
                &lt;/Text>
              &lt;/Text>
            &lt;/View>
          &lt;/View>
        &lt;/ScrollView>
      &lt;/KeyboardAvoidingView>
    &lt;/SafeAreaView>
  );
};

export default EmailSignupScreen;
</code></pre>
        </article>
    </section>




            </div>
            
            <!-- Footer removed via patch -->
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

<script src="scripts/search.js"> </script>


</body>
</html>
