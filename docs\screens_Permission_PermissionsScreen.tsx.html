

<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> screens/Permission/PermissionsScreen.tsx</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="styles/style.css">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
             
                <a href="index.html">
                    <h1 class="navbar-item">GoRaqt Documentation</h1>
                </a>
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    Documentation
                </a>
                
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
                <div class="search-wrapper">
                    <input id="search" type="text" placeholder="Search docs..." class="input">
                </div>
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Global</h3><ul><li><a href="global.html#HIDE_TAB_SCREENS">HIDE_TAB_SCREENS</a></li><li><a href="global.html#SENSITIVE_FIELDS">SENSITIVE_FIELDS</a></li><li><a href="global.html#addBreadcrumb">addBreadcrumb</a></li><li><a href="global.html#apiCall">apiCall</a></li><li><a href="global.html#authenticateWithBiometrics">authenticateWithBiometrics</a></li><li><a href="global.html#calculateAge">calculateAge</a></li><li><a href="global.html#checkAuthStatusApi">checkAuthStatusApi</a></li><li><a href="global.html#checkLocationPermission">checkLocationPermission</a></li><li><a href="global.html#cleanMemoryIfNeeded">cleanMemoryIfNeeded</a></li><li><a href="global.html#clearAllData">clearAllData</a></li><li><a href="global.html#configureStatusBar">configureStatusBar</a></li><li><a href="global.html#createHealthService">createHealthService</a></li><li><a href="global.html#deleteFcmTokenFromDB">deleteFcmTokenFromDB</a></li><li><a href="global.html#deleteNotification">deleteNotification</a></li><li><a href="global.html#disableBiometrics">disableBiometrics</a></li><li><a href="global.html#enableBiometrics">enableBiometrics</a></li><li><a href="global.html#forgotPasswordApi">forgotPasswordApi</a></li><li><a href="global.html#formatBirthdate">formatBirthdate</a></li><li><a href="global.html#getAddressFromCoordinates">getAddressFromCoordinates</a></li><li><a href="global.html#getCurrentLocationWithAddress">getCurrentLocationWithAddress</a></li><li><a href="global.html#getCurrentPosition">getCurrentPosition</a></li><li><a href="global.html#getCurrentRegion">getCurrentRegion</a></li><li><a href="global.html#getCurrentUserApi">getCurrentUserApi</a></li><li><a href="global.html#getNotificationsList">getNotificationsList</a></li><li><a href="global.html#getTabBarVisibility">getTabBarVisibility</a></li><li><a href="global.html#getUnreadNotificationsCount">getUnreadNotificationsCount</a></li><li><a href="global.html#getUserDetails">getUserDetails</a></li><li><a href="global.html#getUserProfile">getUserProfile</a></li><li><a href="global.html#handleNotificationNavigation">handleNotificationNavigation</a></li><li><a href="global.html#handleTextureError">handleTextureError</a></li><li><a href="global.html#initSentry">initSentry</a></li><li><a href="global.html#isBiometricsAvailable">isBiometricsAvailable</a></li><li><a href="global.html#isBiometricsEnabled">isBiometricsEnabled</a></li><li><a href="global.html#lastCacheClearTime">lastCacheClearTime</a></li><li><a href="global.html#logError">logError</a></li><li><a href="global.html#logMessage">logMessage</a></li><li><a href="global.html#loginWithEmail">loginWithEmail</a></li><li><a href="global.html#loginWithEmailApi">loginWithEmailApi</a></li><li><a href="global.html#logoutApi">logoutApi</a></li><li><a href="global.html#markAllNotificationsAsRead">markAllNotificationsAsRead</a></li><li><a href="global.html#markNotificationAsRead">markNotificationAsRead</a></li><li><a href="global.html#navigateBackFromExample">navigateBackFromExample</a></li><li><a href="global.html#navigateToOptimizedFlatListExample">navigateToOptimizedFlatListExample</a></li><li><a href="global.html#otpResendApi">otpResendApi</a></li><li><a href="global.html#otpVerifyApi">otpVerifyApi</a></li><li><a href="global.html#prefetchInitialQueries">prefetchInitialQueries</a></li><li><a href="global.html#prefetchPost">prefetchPost</a></li><li><a href="global.html#prefetchUser">prefetchUser</a></li><li><a href="global.html#refreshTokenApi">refreshTokenApi</a></li><li><a href="global.html#registerWithEmail">registerWithEmail</a></li><li><a href="global.html#registerWithEmailApi">registerWithEmailApi</a></li><li><a href="global.html#requestLocationPermission">requestLocationPermission</a></li><li><a href="global.html#resetPasswordApi">resetPasswordApi</a></li><li><a href="global.html#saveFcmTokenInDB">saveFcmTokenInDB</a></li><li><a href="global.html#schema">schema</a></li><li><a href="global.html#scrubObject">scrubObject</a></li><li><a href="global.html#scrubSensitiveData">scrubSensitiveData</a></li><li><a href="global.html#setCustomStatusBar">setCustomStatusBar</a></li><li><a href="global.html#setDarkStatusBar">setDarkStatusBar</a></li><li><a href="global.html#setLightStatusBar">setLightStatusBar</a></li><li><a href="global.html#setNavigationContext">setNavigationContext</a></li><li><a href="global.html#setStatusBarHidden">setStatusBarHidden</a></li><li><a href="global.html#setUserContext">setUserContext</a></li><li><a href="global.html#setupMemoryCleaning">setupMemoryCleaning</a></li><li><a href="global.html#setupTextureErrorHandlers">setupTextureErrorHandlers</a></li><li><a href="global.html#signInWithFacebook">signInWithFacebook</a></li><li><a href="global.html#signInWithGoogle">signInWithGoogle</a></li><li><a href="global.html#startTransaction">startTransaction</a></li><li><a href="global.html#styles">styles</a></li><li><a href="global.html#updateUserProfile">updateUserProfile</a></li><li><a href="global.html#useAuthStatus">useAuthStatus</a></li><li><a href="global.html#useCurrentUser">useCurrentUser</a></li><li><a href="global.html#useLogin">useLogin</a></li><li><a href="global.html#useRegister">useRegister</a></li><li><a href="global.html#useSafeArea">useSafeArea</a></li><li><a href="global.html#useSocialLogin">useSocialLogin</a></li><li><a href="global.html#useUpdateUserProfile">useUpdateUserProfile</a></li><li><a href="global.html#useUserProfile">useUserProfile</a></li></ul></div><div class="category"><h2>Components</h2><h3>Components</h3><ul><li><a href="Advertisement.html">Advertisement</a></li><li><a href="BiometricGate.html">BiometricGate</a></li><li><a href="CButton.html">CButton</a></li><li><a href="CreateGoRaqtProfile.html">CreateGoRaqtProfile</a></li><li><a href="CustomMapView.html">CustomMapView</a></li><li><a href="CustomMarker.html">CustomMarker</a></li><li><a href="NotificationCard.html">NotificationCard</a></li><li><a href="OfferBanner.html">OfferBanner</a></li><li><a href="Subscription.html">Subscription</a></li></ul></div><div class="category"><h2>Interfaces</h2><h3>Interfaces</h3><ul><li><a href="AdvertisementProps.html">AdvertisementProps</a></li><li><a href="ApiResponse.html">ApiResponse</a></li><li><a href="BiometricGateProps.html">BiometricGateProps</a></li><li><a href="CButtonProps.html">CButtonProps</a></li><li><a href="CreateProfileProps.html">CreateProfileProps</a></li><li><a href="CustomMarkerProps.html">CustomMarkerProps</a></li><li><a href="DrawerItemType.html">DrawerItemType</a></li><li><a href="ForgotPasswordScreenProps.html">ForgotPasswordScreenProps</a></li><li><a href="Notification.html">Notification</a></li><li><a href="NotificationCardProps.html">NotificationCardProps</a></li><li><a href="PaginationState.html">PaginationState</a></li><li><a href="PermissionsScreenProps.html">PermissionsScreenProps</a></li><li><a href="RouteParams.html">RouteParams</a></li><li><a href="SentryNavigationContainerProps.html">SentryNavigationContainerProps</a></li><li><a href="SocialRegisterRequest.html">SocialRegisterRequest</a></li><li><a href="SplashScreenProps.html">SplashScreenProps</a></li><li><a href="TermsOfServiceScreenProps.html">TermsOfServiceScreenProps</a></li><li><a href="UseLogoutReturn.html">UseLogoutReturn</a></li></ul><h3><a href="global.html">Global</a></h3></div><div class="category"><h2>Navigation</h2><h3>Components</h3><ul><li><a href="DrawerNavigator.html">DrawerNavigator</a></li><li><a href="SentryNavigationContainer.html">SentryNavigationContainer</a></li></ul></div><div class="category"><h2>Screens</h2><h3>Components</h3><ul><li><a href="CommunityDetails.html">CommunityDetails</a></li><li><a href="DirectFetchTest.html">DirectFetchTest</a></li><li><a href="EmailSignupScreen.html">EmailSignupScreen</a></li><li><a href="ForgotPasswordScreen.html">ForgotPasswordScreen</a></li><li><a href="LoginScreen.html">LoginScreen</a></li><li><a href="MyMatchesScreen.html">MyMatchesScreen</a></li><li><a href="NotificationsListScreen.html">NotificationsListScreen</a></li><li><a href="PermissionsScreen.html">PermissionsScreen</a></li><li><a href="SignupScreen.html">SignupScreen</a></li><li><a href="SplashScreen.html">SplashScreen</a></li><li><a href="TermsOfServiceScreen.html">TermsOfServiceScreen</a></li><li><a href="Verification.html">Verification</a></li></ul></div><div class="category"><h2>Utils</h2><h3>Global</h3><ul><li><a href="global.html#api">api</a></li><li><a href="global.html#socialLoginApi">socialLoginApi</a></li><li><a href="global.html#useLogout">useLogout</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Source</p>
                    <h1>screens/Permission/PermissionsScreen.tsx</h1>
                </header>
                



    
    <section>
        <article>
            <pre class="prettyprint source linenums"><code>import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  // SafeAreaView,
  Platform,
  Linking,
  Alert,
  PermissionsAndroid,
  ImageBackground,
} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {check, PERMISSIONS, request} from 'react-native-permissions';
import {CButton, SafeAreaView} from '@/components';
import {useIsFocused} from '@react-navigation/native';
import {Images} from '@/config';
import Typography from '@/components/Typography';
import {useConfigStore} from '@/store/configStore';
import {useTranslationContext} from '@/context/TranslationContext';
import {requestNotificationPermission} from '@/components/common/PushNotification';

/**
 * @category Interfaces
 * @typedef {Object} PermissionsScreenProps
 * @property {Function} onComplete - Call when all the permissions are given
 */
interface PermissionsScreenProps {
  onComplete: () => void;
}

/**
 * @component
 * @category Screens
 *
 * @description  Displays a sequence of permission prompts for Location, Bluetooth, and Notifications.
 * Guides the user through enabling necessary permissions for the app to function properly.
 *
 * @returns {JSX.Element}
 */

const PermissionsScreen: React.FC&lt;PermissionsScreenProps> = ({onComplete}) => {
  const theme = useThemeStore();
  const [currentPermission, setCurrentPermission] = useState&lt;string>('location');
  const {t} = useTranslationContext();

  const IOS = Platform.OS === 'ios';

  const {permissions, setPermissions, setNotifications} = useConfigStore();

  const isFocused = useIsFocused();
  useEffect(() => {
    if (permissions?.location === true) {
      setCurrentPermission('bluetooth');
    }
    if (permissions?.bluetooth === true) {
      setCurrentPermission('notification');
    }
    if (permissions?.notification === true) {
      onComplete();
    }
  }, [isFocused, permissions, onComplete]);

  /**
   * @function handlePermissionResponse
   * @memberof PermissionsScreen
   * @description
   * Handles the permission response and determines next step
   * @param {'location' | 'bluetooth' | 'notification' | 'contact'} permission - Type of permission handled
   * @param {boolean} granted - Whether the permission was granted
   */

  // handle permission response
  const handlePermissionResponse = (
    permission: 'location' | 'bluetooth' | 'notification' | 'contact',
    granted: boolean,
  ) => {
    // For now, just move to the next permission or complete
    if (permission === 'location') {
      if (!granted) {
        // Location is necessary, so don't proceed if denied
        return;
      }
      setCurrentPermission('bluetooth');
    } else if (permission === 'bluetooth') {
      setCurrentPermission('notification');
    }
    // else if (permission === 'notification') {
    //   setCurrentPermission('contact');
    // }
    else {
      // All permissions handled, proceed to the app
      onComplete();
    }
  };

  /**
   * @function renderLocationPermission
   * @memberof PermissionsScreen
   * @description
   * Renders the UI block for Location permission
   * @returns {JSX.Element}
   */
  const renderLocationPermission = () => (
    &lt;View style={styles(theme).permissionCard}>
      &lt;View>
        &lt;Typography variant="permissionTitle" align="center" style={styles(theme).permissionTitle}>
          {t('permissions.locationPermissions')}
        &lt;/Typography>
        &lt;Typography variant="body" align="center" style={styles(theme).permissionDescription}>
          {t('permissions.locationDescription')}
        &lt;/Typography>
      &lt;/View>
      &lt;View>
        &lt;CButton
          title={t('permissions.enableLocation')}
          variant="primary"
          onPress={() => checkLocationPermission()}
          containerStyle={styles(theme).buttonContainer}
        />

        &lt;View style={styles(theme).dotsContainer}>
          &lt;View style={[styles(theme).dot, {backgroundColor: theme.colors.activeColor}]} />
          &lt;View style={styles(theme).dot} />
          &lt;View style={styles(theme).dot} />
        &lt;/View>
      &lt;/View>
    &lt;/View>
  );

  /**
   * @function renderBluetoothPermission
   * @memberof PermissionsScreen
   * @description
   * Renders the UI block for Bluetooth permission
   * @returns {JSX.Element}
   */
  const renderBluetoothPermission = () => (
    &lt;View style={[styles(theme).permissionCard]}>
      &lt;View>
        &lt;Typography variant="permissionTitle" align="center" style={styles(theme).permissionTitle}>
          {t('permissions.bluetoothPermissions')}
        &lt;/Typography>
        &lt;Typography variant="body" align="center" style={styles(theme).permissionDescription}>
          {t('permissions.bluetoothDescription')}
        &lt;/Typography>
      &lt;/View>

      &lt;View>
        &lt;CButton
          title={t('permissions.enableBluetooth')}
          variant="primary"
          containerStyle={[styles(theme).buttonContainer, {marginBottom: 10}]}
          onPress={() => requestBluetoothPermissions()}
        />
        &lt;CButton
          title={t('permissions.noThanks')}
          containerStyle={styles(theme).buttonContainer}
          variant="secondary"
          onPress={() => {
            handlePermissionResponse('bluetooth', false);
            setPermissions({...permissions, bluetooth: true});
          }}
        />

        &lt;View style={styles(theme).dotsContainer}>
          &lt;View style={styles(theme).dot} />
          &lt;View style={[styles(theme).dot, {backgroundColor: theme.colors.activeColor}]} />
          &lt;View style={styles(theme).dot} />
        &lt;/View>
      &lt;/View>
    &lt;/View>
  );

  /**
   * @function renderNotificationPermission
   * @memberof PermissionsScreen
   * @description
   * Renders the UI block for Notification permission
   * @returns {JSX.Element}
   */
  const renderNotificationPermission = () => (
    &lt;View style={styles(theme).permissionCard}>
      &lt;View>
        &lt;Typography variant="permissionTitle" align="center" style={styles(theme).permissionTitle}>
          {t('permissions.notificationPermissions')}
        &lt;/Typography>
        &lt;Typography variant="body" align="center" style={styles(theme).permissionDescription}>
          {t('permissions.notificationDescription')}
        &lt;/Typography>
      &lt;/View>

      &lt;View>
        &lt;CButton
          title={t('permissions.enableNotifications')}
          variant="primary"
          containerStyle={[styles(theme).buttonContainer, {marginBottom: 10}]}
          onPress={async () => {
            const granted = await requestNotificationPermission();
            if (granted) {
              setPermissions({...permissions, notification: true});
              handlePermissionResponse('notification', true);
              setNotifications(true);
            } else {
              handlePermissionResponse('notification', false);
              setPermissions({...permissions, notification: true});
              setNotifications(false);
            }
          }}
        />

        &lt;CButton
          title={t('permissions.noThanks')}
          variant="secondary"
          containerStyle={styles(theme).buttonContainer}
          onPress={() => {
            handlePermissionResponse('notification', false);
            setPermissions({...permissions, notification: true});
            setNotifications(false);
          }}
        />

        &lt;View style={styles(theme).dotsContainer}>
          &lt;View style={styles(theme).dot} />
          &lt;View style={styles(theme).dot} />
          &lt;View style={[styles(theme).dot, {backgroundColor: theme.colors.activeColor}]} />
        &lt;/View>
      &lt;/View>
    &lt;/View>
  );

  /**
   * @function checkLocationPermission
   * @memberof PermissionsScreen
   * @description
   * Check whether user granted location permission or not.
   */
  // function for check location permission
  const checkLocationPermission = async () => {
    try {
      if (IOS) {
        const result = await check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
        switch (result) {
          case 'granted':
            setPermissions({...permissions, location: true});
            return true;
          case 'denied':
            requestLocationPermission();
            return false;
          case 'blocked':
            requestLocationPermission();
            return false;
          case 'unavailable':
            return false;
        }
      } else {
        const granted = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        );
        if (granted) {
          setPermissions({...permissions, location: true});
          handlePermissionResponse('location', true);
        } else {
          requestLocationPermission();
        }
      }
    } catch (error) {
      console.error('Error occurred while requesting location permission:', error);
    }
  };

  /**
   * @function requestLocationPermission
   * @memberof PermissionsScreen
   * @description
   * Prompts the user for accessing location permission.
   * The location permission is mandatory; without granting it, the user cannot proceed further.
   * If user denied then redirect to app setting.
   */
  const requestLocationPermission = async () => {
    try {
      if (IOS) {
        const result = await request(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
        switch (result) {
          case 'granted':
            setPermissions({...permissions, location: true});
            break;
          case 'denied':
            showEnablePermissionAlert();
            break;
          case 'blocked':
            showEnablePermissionAlert();
            break;
          case 'unavailable':
            break;
        }
      } else {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        );

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          setPermissions({...permissions, location: true});
          handlePermissionResponse('location', true);
        } else {
          showEnablePermissionAlert();
        }
      }
    } catch (error) {
      console.error('Error occurred while requesting location permission:', error);
    }
  };

  /**
   * @function showEnablePermissionAlert
   * @memberof PermissionsScreen
   * @description
   * Show the alert to user for granting location permission
   */
  const showEnablePermissionAlert = () => {
    Alert.alert('Oops!', 'Application will not work properly without your location turned on.', [
      {
        text: 'Cancel',
        onPress: () => {
          console.log('Cancel Pressed');
        },
        style: 'cancel',
      },
      {
        text: 'Go to Settings',
        onPress: () => {
          if (Platform.OS === 'ios') {
            Linking.openURL('app-settings:');
          } else {
            Linking.openSettings();
          }
        },
      },
    ]);
  };

  /**
   * @function requestBluetoothPermissions
   * @memberof PermissionsScreen
   * @description
   * Used to request the necessary Bluetooth-related permission
   */
  const requestBluetoothPermissions = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ]);
        setPermissions({...permissions, bluetooth: true});
        handlePermissionResponse('bluetooth', true);
        console.log('Permission result:', granted);
      } catch (err) {
        console.warn(err);
      }
    } else if (Platform.OS === 'ios') {
      const result = await request(PERMISSIONS.IOS.BLUETOOTH);
      setPermissions({...permissions, bluetooth: true});
      handlePermissionResponse('bluetooth', true);
      console.log('iOS Bluetooth permission:', result);
    }
  };

  return (
    &lt;ImageBackground
      source={Images.splash}
      style={[styles(theme).background]} // Set opacity on the background only
      resizeMode="contain" // Or 'cover', depending on your image
    >
      &lt;SafeAreaView style={[styles(theme).container]}>
        &lt;View style={styles(theme).cardContainer}>
          {currentPermission === 'location' &amp;&amp; renderLocationPermission()}
          {currentPermission === 'bluetooth' &amp;&amp; renderBluetoothPermission()}
          {currentPermission === 'notification' &amp;&amp; renderNotificationPermission()}
        &lt;/View>
      &lt;/SafeAreaView>
    &lt;/ImageBackground>
  );
};

const {width, height} = Dimensions.get('window');

const styles = (theme: any) =>
  StyleSheet.create({
    background: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.black, // fallback color
    },
    container: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    cardContainer: {
      alignItems: 'center',
    },
    permissionCard: {
      backgroundColor: theme.colors.white,
      width: width * 0.75,
      borderRadius: 8,
      paddingVertical: 20,
      paddingHorizontal: 15,
      elevation: 5,
      // opacity: 0.95,
      minHeight: height * 0.4,
      justifyContent: 'space-between',
    },
    activeCard: {
      borderColor: theme.colors.primary,
      borderWidth: 2,
    },
    permissionTitle: {
      marginBottom: 4,
    },
    buttonContainer: {
      marginBottom: 5,
      height: 70,
    },
    permissionDescription: {
      color: theme.colors.black,
      marginBottom: 20,
      letterSpacing: 0,
      lineHeight: 18,
      fontWeight: 400,
    },

    dotsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginTop: 10,
    },
    dot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: theme.colors.divider,
      marginHorizontal: 3,
    },
  });

export default PermissionsScreen;
</code></pre>
        </article>
    </section>




            </div>
            
            <!-- Footer removed via patch -->
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

<script src="scripts/search.js"> </script>


</body>
</html>
