import React, {useRef, useState} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {View, Text, StyleSheet, ScrollView, Dimensions, useWindowDimensions} from 'react-native';
import {CButton, SafeAreaView} from '@/components';
import {useQuery} from '@tanstack/react-query';
import api from '@/services/api';
import RenderHtml from 'react-native-render-html';
import C<PERSON>oader from '@/components/CLoader';
import {useTranslationContext} from '@/context/TranslationContext';

/**
 * @category Interfaces
 * @typedef {Object} TermsOfServiceScreenProps
 * @property {Function} onAccept - Initiate when user click on Accept button.
 */
interface TermsOfServiceScreenProps {
  onAccept?: () => void;
}

/**
 * @category Interfaces
 * @typedef {Object} TermsOfServicesApiResponse
 * @property {Object} data - which contains content and title.
 */
interface ApiResponse {
  data: {
    html_body: string;
    title: string;
  };
}

/**
 * @component
 * @category Screens
 * @description A comprehensive terms of service display screen that fetches terms content
 * from an API and renders it as scrollable HTML. Features scroll-to-bottom
 * validation before enabling the accept button, ensuring users have read
 * the complete terms.
 *
 * @return {JSX.Element} The rendered Terms of services screen component
 */

const TermsOfServiceScreen: React.FC<TermsOfServiceScreenProps> = ({onAccept}) => {
  const theme = useThemeStore();
  const {width} = useWindowDimensions();
  const scrollViewRef = useRef<ScrollView>(null);
  const {t} = useTranslationContext();

  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);

  const handleScroll = ({nativeEvent}: any) => {
    const {layoutMeasurement, contentOffset, contentSize} = nativeEvent;
    const paddingToBottom = 20;

    // If content is shorter than screen height, enable the button
    if (contentSize.height <= layoutMeasurement.height) {
      setHasScrolledToBottom(true);
      return;
    }

    // Normal scroll check
    if (layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom) {
      setHasScrolledToBottom(true);
    }
  };

  const {data, isLoading} = useQuery<ApiResponse>({
    queryKey: ['terms-of-service'],
    queryFn: async () => {
      try {
        const response = await api.get('/terms-of-services');
        if (response.status === 200) {
          return response.data;
        }
      } catch (error) {
        console.error('Error fetching terms of service:', error);
        return null;
      }
    },
  });
  console.log('data', data);

  return (
    <SafeAreaView includeTop style={[styles(theme).container]}>
      <View style={styles(theme).termsContainer}>
        <Text style={styles(theme).title}>{data?.data?.title || t('terms.title')}</Text>

        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={styles(theme).scrollView}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}>
          {isLoading ? (
            <CLoader />
          ) : (
            <RenderHtml
              contentWidth={width}
              source={{html: data?.data?.html_body || ''}}
              // tagsStyles={{
              //   p: {
              //     fontSize: 14,
              //     lineHeight: 20,
              //     color: theme.colors.black,
              //     marginBottom: 10,
              //   },
              //   strong: {
              //     fontWeight: 'bold',
              //   },
              // }}
            />
          )}
        </ScrollView>
      </View>

      <CButton
        title={t('terms.accept')}
        onPress={onAccept}
        isDisabled={!hasScrolledToBottom}
        containerStyle={styles(theme).acceptButton}
      />
    </SafeAreaView>
  );
};

const {height} = Dimensions.get('window');

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.colors.background,
    },
    termsContainer: {
      width: '100%',
      height: height * 0.82,
      backgroundColor: theme.colors.white,
      borderRadius: 8,
      overflow: 'hidden',
      paddingHorizontal: 16,
    },
    title: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.black,
      paddingVertical: 15,
    },
    scrollView: {
      flexGrow: 1,
    },
    termsText: {
      fontSize: 14,
      lineHeight: 20,
      color: theme.colors.black,
      paddingBottom: 20,
    },
    boldText: {
      fontWeight: 'bold',
      fontSize: 14,
      color: theme.colors.black,
    },
    acceptButton: {
      width: '100%',
      marginTop: 20,
      marginBottom: 10,
    },
    acceptButtonText: {
      color: theme.colors.white,
      fontWeight: 'bold',
      fontSize: 16,
    },
  });

export default TermsOfServiceScreen;
