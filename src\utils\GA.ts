import analytics from '@react-native-firebase/analytics';

export const logEvent = (eventName: string, params: Record<string, any> = {}) => {
  // Log custom event
  analytics().logEvent(eventName, params);
};

export const logScreenView = (screenName: string, screenClass: string) => {
  // Log screen view
  analytics()
    .logScreenView({
      screen_name: screenName,
      screen_class: screenClass,
    })
    .then(() => {
      console.log(`Screen view logged: ${screenName}`);
    })
    .catch(err => {
      console.error('Logging failed', err);
    });
};

export const setUserProperties = (name: string, value: string) => {
  // Log app open
  analytics().setUserProperty(name, value);
};
