import React, {useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation/index';
import {useThemeStore} from '@/store/themeStore';
import {getGlobalStyles} from '@utils/styleUtils';
import {useForm, Controller} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {CInput, CButton, Icon, SafeAreaView} from '@components/index';
import {createStyles} from './styles';
import Typography from '@/components/Typography';
import {useAuthStore} from '@/store';
import {useRegister} from '@/hooks/queries/useAuth';
import {toaster, validatePassword} from '@/utils/commonFunctions';
import useTranslation from '@/hooks/useTranslation';
import {logEvent, setUserProperties} from '@/utils/GA';
type EmailSignupScreenNavigationProp = StackNavigationProp<RootStackParamList, 'EmailSignup'>;

// Form validation schema
const schema = yup.object({
  fullName: yup.string().required('Full name is required'),
  email: yup.string().email('Email format is invalid').required('Email is required'),
  password: yup
    .string()
    .required('Password is required')
    .test(
      'password-validation',
      'Password must have at least 8 characters, including uppercase, lowercase, a number, and a symbol',
      value => !!value && validatePassword(value).isValid,
    ),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('password')], 'Passwords must match')
    .required('Please confirm your password'),
});

// Form data type
type FormData = yup.InferType<typeof schema>;

/**
 * @component
 * @category Screens
 *
 * @description  A comprehensive email registration screen that provides a complete signup flow
 * with form validation, password strength checking, and seamless navigation between
 * input fields. Features analytics tracking and error handling.
 *
 * @see {@link Sign-in} - Used to display sign-in link and navigate user to sign-in screen.
 *
 * @return {JSX.Element} Email Signup screen
 */
const EmailSignupScreen = () => {
  const navigation = useNavigation<EmailSignupScreenNavigationProp>();
  const theme = useThemeStore();
  const globalStyles = getGlobalStyles({theme});
  const styles = createStyles(theme);
  const {isApiStatus} = useAuthStore();
  // Use React Query for registration
  const registerMutation = useRegister();
  const {t} = useTranslation();

  // Add refs for input fields
  const fullNameInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const passwordInputRef = useRef<TextInput>(null);
  const confirmPasswordInputRef = useRef<TextInput>(null);

  // Initialize form with react-hook-form
  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      fullName: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const handleBack = () => {
    navigation.goBack();
  };

  /**
   * @function onSubmit
   * @memberof EmailSignupScreen
   * @description
   * Processes the validated form data, tracks user events, and handles
   * different signup scenarios including existing users and new registrations.
   * Routes users to appropriate screens based on registration result.
   * @param {FormData} data - Form data containing fullname, email password and confirm password.
   */
  const onSubmit = (data: FormData) => {
    setUserProperties('email', data.email);
    logEvent('email_signup_button_clicked', {
      email: data.email,
      full_name: data.fullName,
      method: 'Goraqt',
    });
    // Form is valid, proceed with signup using React Query
    if (isApiStatus) {
      registerMutation.mutate(
        {
          email: data.email,
          password: data.password,
          fullName: data.fullName,
        },
        {
          onSuccess: response => {
            if (response.status) {
              if (response.data?.user_exist) {
                navigation.replace('Login', {email: data.email});
                logEvent('email_signup_with_existing_user', {
                  email: data.email,
                  full_name: data.fullName,
                  method: 'Goraqt',
                });
              } else {
                navigation.navigate('Verification', {
                  email: data.email,
                  from: 'signup',
                });
                logEvent('email_signup_with_new_user', {
                  email: data.email,
                  full_name: data.fullName,
                  method: 'Goraqt',
                });
              }
              toaster('success', response.message, 'top');
            } else {
              logEvent('email_signup_failed', {
                email: data.email,
                full_name: data.fullName,
                method: 'Goraqt',
              });
              toaster('error', response.message, 'top');
            }
          },
          onError: error => {
            logEvent('email_signup_failed', {
              email: data.email,
              full_name: data.fullName,
              method: 'Goraqt',
            });
            toaster('error', error.message, 'top');
          },
        },
      );
    } else {
      navigation.navigate('Verification', {
        email: data.email,
      });
    }
  };

  const handleSignIn = () => {
    const email = control._formValues.email;
    navigation.replace('Login', {email});
  };

  return (
    <SafeAreaView includeTop={true} style={[styles.container]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}>
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled">
          <View style={styles.headerContainer}>
            <TouchableOpacity
              onPress={handleBack}
              style={styles.backButton}
              activeOpacity={0.7}
              hitSlop={{top: 10, right: 10, bottom: 10, left: 10}}>
              <Icon name="Left-chevron" size={22} color={theme.colors.gray} />
            </TouchableOpacity>
            <Typography variant="subtitle" style={globalStyles.title}>
              {t('emailSignupScreen.signUp')}
            </Typography>
          </View>

          <View style={styles.form}>
            <View style={styles.flex}>
              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="fullName"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('emailSignupScreen.fullName')}
                      showLabel={true}
                      variant="dark"
                      placeholder={t('emailSignupScreen.fullNamePlaceholder')}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.fullName}
                      error={errors.fullName?.message}
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={fullNameInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => emailInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="email"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('emailSignupScreen.email')}
                      showLabel={true}
                      variant="dark"
                      placeholder={t('emailSignupScreen.emailPlaceholder')}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.email}
                      error={errors.email?.message}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={emailInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => passwordInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="password"
                  render={({field: {onChange, onBlur, value}}) => {
                    const validation = validatePassword(value);

                    const errorMessage =
                      errors.password?.message ||
                      (value.length > 0 && !validation.isValid ? validation.message : '');

                    return (
                      <CInput
                        label={t('emailSignupScreen.password')}
                        showLabel={true}
                        variant="dark"
                        placeholder={t('emailSignupScreen.passwordPlaceholder')}
                        value={value}
                        onChangeText={onChange}
                        onBlur={onBlur}
                        hasError={!!errors.password || (value.length > 0 && !validation.isValid)}
                        error={errorMessage}
                        secureTextEntry
                        inputStyle={styles.input}
                        containerStyle={{marginBottom: 0}}
                        ref={passwordInputRef}
                        returnKeyType="next"
                        onSubmitEditing={() => confirmPasswordInputRef.current?.focus()}
                        blurOnSubmit={false}
                      />
                    );
                  }}
                />
              </View>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="confirmPassword"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('emailSignupScreen.confirmPassword')}
                      showLabel={true}
                      variant="dark"
                      placeholder={t('emailSignupScreen.confirmPasswordPlaceholder')}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.confirmPassword}
                      error={errors.confirmPassword?.message}
                      secureTextEntry
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={confirmPasswordInputRef}
                      returnKeyType="done"
                      onSubmitEditing={handleSubmit(onSubmit)}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>
            </View>

            <CButton
              title={t('emailSignupScreen.submit')}
              onPress={handleSubmit(onSubmit)}
              loading={registerMutation.isPending}
              isDisabled={registerMutation.isPending}
            />

            <View style={styles.signInContainer}>
              <Text style={styles.signInText}>
                Already signed up?{' '}
                <Text style={styles.signInLink} onPress={handleSignIn}>
                  {t('emailSignupScreen.signIn')}
                </Text>
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default EmailSignupScreen;
