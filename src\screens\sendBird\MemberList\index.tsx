import React, {useState, useEffect} from 'react';
import {createGroupChannelMembersFragment, useSendbirdChat} from '@sendbird/uikit-react-native';
import {useGroupChannel} from '@sendbird/uikit-chat-hooks';
import {useNavigation, useIsFocused} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation';

type NavigationProp = StackNavigationProp<RootStackParamList, 'SendBird'>;

const MemberList = ({route}: {route: any}) => {
  const GroupChannelMembersFragment = createGroupChannelMembersFragment();
  const [refreshKey, setRefreshKey] = useState(0);
  const isFocused = useIsFocused();

  const {channelUrl} = route.params;
  const {sdk} = useSendbirdChat();
  const {channel} = useGroupChannel(sdk, channelUrl);
  const navigation = useNavigation<NavigationProp>();

  useEffect(() => {
    if (isFocused && channel) {
      setRefreshKey(prev => prev + 1);
    }
  }, [isFocused, channel]);

  if (!channel) return null;

  return (
    <GroupChannelMembersFragment
      key={refreshKey}
      channel={channel}
      onPressHeaderLeft={() => navigation.goBack()}
      onPressHeaderRight={() => {
        navigation.navigate('InviteUser', {
          channelUrl: channelUrl,
        });
      }}
    />
  );
};

export default MemberList;
