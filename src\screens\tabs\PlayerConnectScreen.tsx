import React, {useState, useEffect, useRef, forwardRef, useImperativeHandle} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {StyleSheet, BackHandler, View, Text, TouchableWithoutFeedback} from 'react-native';
import {DEFAULT_REGION, getCurrentRegion} from '@/utils/locationService';
import BottomSheet from '@gorhom/bottom-sheet';
import FindPlayers from '@/components/FindPlayer';
import ParksSearchScreen from '@/components/ParksSearch';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import PlayerSchedulePlayCard from '@/components/PlayerSchedulePlayCard';

// Define the Player type to match what's in FindPlayers component
type Player = {
  id: string;
  name: string;
  rating: string;
  location: string;
  image: string;
  color?: string;
  isPremium?: boolean;
};
// Define the ref interface for external access
export interface PlayerConnectScreenRef {
  handleMarkerPress: (marker: any) => void;
  hidePlayerScheduleContainer: () => void;
}

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'transparent',
    },
    playerScheduleContainer: {
      flexDirection: 'column',
      alignItems: 'center',
      paddingHorizontal: 16,
      position: 'absolute',
      bottom: 240,
      left: 0,
      right: 0,
      zIndex: 1,
    },
    weatherHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      marginBottom: 8,
      width: '100%',
    },
    weatherIcon: {
      fontSize: 20,
      marginRight: 8,
    },
    bottomSheet: {
      paddingHorizontal: 16,
      paddingTop: 10,
    },
    parksSearchSheet: {
      paddingTop: 20,
      paddingHorizontal: 16,
    },
    contentContainer: {
      paddingTop: 10,
    },
    overlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 0,
    },
  });

const PlayerConnectScreen = forwardRef<PlayerConnectScreenRef, any>((props, ref) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const [initialRegion, setInitialRegion] = useState(DEFAULT_REGION);
  const [selectedPlayers, setSelectedPlayers] = useState<Player[]>([]);
  const [currentMode, setCurrentMode] = useState<'findPlayers' | 'parksSearch'>('findPlayers');
  const [showPlayerScheduleContainer, setShowPlayerScheduleContainer] = useState(false);
  const [selectedMarker, setSelectedMarker] = useState<any>(null);
  console.log('selectedMarker', selectedMarker);

  const bottomSheetRef = useRef<BottomSheet>(null);

  const navigation = useNavigation<NavigationProp<any>>();

  useEffect(() => {
    const fetchUserLocation = async () => {
      try {
        const region = await getCurrentRegion();
        console.log('Initial region:', region);
        setInitialRegion(region);
      } catch (error) {
        console.error('Error getting user location:', error);
      }
    };

    fetchUserLocation();
  }, []);

  // Handle hardware back button press
  useEffect(() => {
    const backAction = () => {
      if (currentMode === 'parksSearch') {
        setCurrentMode('findPlayers');
        setTimeout(() => {
          if (bottomSheetRef.current) {
            bottomSheetRef.current.expand();
          }
        }, 300);
        return true;
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

    return () => backHandler.remove(); // Clean up the event listener
  }, [currentMode]);

  const hidePlayerScheduleContainer = () => {
    setShowPlayerScheduleContainer(false);
  };

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    handleMarkerPress: (marker: any) => {
      setSelectedMarker(marker);
      setShowPlayerScheduleContainer(true);
    },
    hidePlayerScheduleContainer,
  }));

  const handleConfirm = (players: Player[]) => {
    setSelectedPlayers(players);
    setCurrentMode('parksSearch');
  };

  const handleParkSelect = (parkData: object) => {
    navigation.navigate('PlayerConnectDateScreen', {
      parkData: parkData,
      selectedPlayersData: selectedPlayers,
    });
  };

  // Get snap points based on the current mode
  const getSnapPoints = () => {
    return currentMode === 'findPlayers' ? [220, 600] : [210, 500];
  };

  return (
    <View style={styles.container}>
      {showPlayerScheduleContainer && (
        <>
          <TouchableWithoutFeedback onPress={hidePlayerScheduleContainer}>
            <View style={styles.overlay} />
          </TouchableWithoutFeedback>

          <View style={styles.playerScheduleContainer}>
            <PlayerSchedulePlayCard
              playerName={selectedMarker?.display_name || ''}
              location={selectedMarker?.userData?.location || ''}
              image={selectedMarker?.profile_pic}
              onPress={() => {
                // First hide the weather container
                hidePlayerScheduleContainer();

                // Navigate to the SearchScreen tab with params
                navigation.navigate('SEARCH', {
                  screen: 'SearchScreen',
                  params: {
                    playerData: selectedMarker,
                    location: selectedMarker?.userData?.location,
                    playerName: selectedMarker?.display_name,
                    image: selectedMarker?.profile_pic,
                  },
                });
              }}
            />
          </View>
        </>
      )}

      <BottomSheet
        ref={bottomSheetRef}
        snapPoints={getSnapPoints()}
        enableOverDrag={false}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        enablePanDownToClose={false}
        animateOnMount
        style={currentMode === 'findPlayers' ? styles.bottomSheet : styles.parksSearchSheet}
        backgroundStyle={{backgroundColor: theme.colors.background}}
        handleIndicatorStyle={{backgroundColor: theme.colors.bottomSheetBackground}}
        onChange={index => {
          if (index === -1) {
            bottomSheetRef.current?.snapToIndex(0);
          }
          // Hide weather container when sheet moves
          if (showPlayerScheduleContainer) {
            setShowPlayerScheduleContainer(false);
          }
        }}
        onAnimate={(fromIndex, toIndex) => {
          // Hide weather container when sheet starts animating
          if (showPlayerScheduleContainer) {
            setShowPlayerScheduleContainer(false);
          }
        }}>
        {currentMode === 'findPlayers' ? (
          <FindPlayers onConfirm={handleConfirm} initialSelectedPlayers={selectedPlayers} />
        ) : (
          <ParksSearchScreen
            onParkSelect={handleParkSelect}
            type="playerConnect"
            onBackPress={() => {
              setCurrentMode('findPlayers');
            }}
          />
        )}
      </BottomSheet>
    </View>
  );
});

export default PlayerConnectScreen;
