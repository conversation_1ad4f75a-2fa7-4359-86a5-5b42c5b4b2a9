import React from 'react';
import {
  createGroupChannelMutedMembersFragment,
  useSendbirdChat,
} from '@sendbird/uikit-react-native';
import {useGroupChannel} from '@sendbird/uikit-chat-hooks';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation';

type NavigationProp = StackNavigationProp<RootStackParamList, 'SendBird'>;

const MutedList = ({route}: {route: any}) => {
  const GroupChannelMutedMembersFragment = createGroupChannelMutedMembersFragment();

  const {channelUrl} = route.params;
  const {sdk} = useSendbirdChat();
  const {channel} = useGroupChannel(sdk, channelUrl);
  const navigation = useNavigation<NavigationProp>();
  if (!channel) return null;

  return (
    <GroupChannelMutedMembersFragment
      channel={channel}
      onPressHeaderLeft={() => navigation.goBack()}
    />
  );
};

export default MutedList;
