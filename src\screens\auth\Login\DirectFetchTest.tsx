import React, {useState} from 'react';
import {View, Text, StyleSheet, Button, TextInput, Alert, ActivityIndicator} from 'react-native';

/**
 * @component
 * @category Screens
 * @description
 * DirectFetchTest component allows users to test the login API using fetch directly.
 * It provides input fields for email and password, and buttons to send login requests
 * using both JSON and form-urlencoded formats.
 * It displays the API response and handles loading states.
 * @returns {JSX.Element} - Returns the rendered component with input fields, buttons,
 * and response display.
 */
const DirectFetchTest = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('$miT2345');
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<any>(null);

  const handleLogin = async () => {
    setLoading(true);
    try {
      console.log('Sending login request with fetch:', {email, password});

      // Use fetch instead of axios
      const result = await fetch('https://6559-171-78-198-103.ngrok-free.app/go-reqt/v1/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
        }),
      });

      // Log the raw response
      console.log('Raw fetch response status:', result.status);
      console.log('Raw fetch response headers:', result.headers);

      // Parse the JSON response
      const data = await result.json();
      console.log('Parsed response data:', data);

      setResponse(data);

      if (result.ok) {
        Alert.alert('Success', 'Login successful');
      } else {
        Alert.alert('Error', data.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error with fetch:', error);
      setResponse({error: error instanceof Error ? error.message : 'Unknown error'});
      Alert.alert('Error', error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  // Try with form-urlencoded format
  const handleLoginFormUrlEncoded = async () => {
    setLoading(true);
    try {
      console.log('Sending login request with form-urlencoded:', {email, password});

      // Create form data
      const formData = new URLSearchParams();
      formData.append('email', email);
      formData.append('password', password);

      // Use fetch with form-urlencoded
      const result = await fetch('https://6559-171-78-198-103.ngrok-free.app/go-reqt/v1/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Accept: 'application/json',
        },
        body: formData.toString(),
      });

      // Log the raw response
      console.log('Raw form-urlencoded response status:', result.status);

      // Parse the JSON response
      const data = await result.json();
      console.log('Parsed form-urlencoded response data:', data);

      setResponse(data);

      if (result.ok) {
        Alert.alert('Success', 'Login successful (form-urlencoded)');
      } else {
        Alert.alert('Error', data.message || 'Login failed (form-urlencoded)');
      }
    } catch (error) {
      console.error('Login error with form-urlencoded:', error);
      setResponse({error: error instanceof Error ? error.message : 'Unknown error'});
      Alert.alert('Error', error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Direct Fetch Test</Text>

      <TextInput
        style={styles.input}
        value={email}
        onChangeText={setEmail}
        placeholder="Email"
        keyboardType="email-address"
        autoCapitalize="none"
      />

      <TextInput
        style={styles.input}
        value={password}
        onChangeText={setPassword}
        placeholder="Password"
        secureTextEntry
      />

      <View style={styles.buttonContainer}>
        <Button
          title={loading ? 'Loading...' : 'Test with JSON'}
          onPress={handleLogin}
          disabled={loading}
        />

        <View style={styles.buttonSpacer} />

        <Button
          title={loading ? 'Loading...' : 'Test with Form'}
          onPress={handleLoginFormUrlEncoded}
          disabled={loading}
        />
      </View>

      {loading && <ActivityIndicator style={styles.loader} size="large" color="#0000ff" />}

      {response && (
        <View style={styles.responseContainer}>
          <Text style={styles.responseTitle}>API Response:</Text>
          <Text style={styles.responseText}>{JSON.stringify(response, null, 2)}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  buttonSpacer: {
    width: 10,
  },
  loader: {
    marginTop: 20,
  },
  responseContainer: {
    marginTop: 20,
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  responseTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  responseText: {
    fontFamily: 'monospace',
  },
});

export default DirectFetchTest;
